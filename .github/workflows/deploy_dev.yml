name: Deploy to Azure Container App (dev)

on:
  # Allows you to trigger the workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  deploy-to-azure:
    # Optionally restrict this job to run only if the branch is dev
    if: github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repository
        uses: actions/checkout@v3

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Build and deploy Container App
        uses: azure/container-apps-deploy-action@v1
        with:
          # Path to your API source code or Dockerfile
          appSourcePath: ${{ github.workspace }}
          dockerfilePath: Dockerfile-api
          acrName: acrodmori
          containerAppName: capp-odmori-api-dev
          containerAppEnvironment: cappsenv-odmori-dev
          resourceGroup: rg-odmori-dev
          imageToBuild: acrodmori.azurecr.io/odmori-api:${{ github.sha }}