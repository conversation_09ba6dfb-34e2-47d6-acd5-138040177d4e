-- Step 1: Create a temporary table with 100 selected persons and their party assignments
CREATE TEMP TABLE temp_party_data AS
WITH selected_persons AS (
  SELECT
    id,
    first_name,
    last_name,
    row_number() OVER (ORDER BY random()) as rn
  FROM persons
  WHERE email LIKE '%@example.com'
  LIMIT 100
)
SELECT
  id as person_id,
  first_name,
  last_name,
  rn,
  CASE
    WHEN rn <= 50 THEN ((rn - 1) / 2) + 1  -- 25 parties of 2 (people 1-50)
    WHEN rn <= 80 THEN 25 + ((rn - 51) / 3) + 1  -- 10 parties of 3 (people 51-80)
    ELSE 35 + ((rn - 81) / 4) + 1  -- 5 parties of 4 (people 81-100)
  E<PERSON> as party_number,
  gen_random_uuid() as party_id
FROM selected_persons;

-- Step 2: Update party_id to be the same for people in the same party
UPDATE temp_party_data t1
SET party_id = (
  SELECT t2.party_id
  FROM temp_party_data t2
  WHERE t2.party_number = t1.party_number
  ORDER BY t2.rn
  LIMIT 1
);

-- Step 3: Insert travel parties (one per unique party_number)
INSERT INTO travel_parties (id, trip_id, main_contact_id, trip_destination_id, created_at, is_deleted)
SELECT DISTINCT
  party_id,
  '01985a39-dd62-7976-a44b-f22b46f36bb7'::uuid,
  (SELECT person_id FROM temp_party_data t2 WHERE t2.party_number = t1.party_number ORDER BY t2.rn LIMIT 1),
  '01985a68-f73e-752d-9c41-5b8e3d4651f0'::uuid,
  now(),
  false
FROM temp_party_data t1;

-- Step 4: Insert travelers (one per person)
INSERT INTO travelers (id, travel_party_id, person_id, status, price, insurance_price, tax_price, discount, travel_service_type, created_at, is_deleted)
SELECT
  gen_random_uuid(),
  party_id,
  person_id,
  1,
  200.0,
  12.0,
  10.0,
  0.0,
  1,
  now(),
  false
FROM temp_party_data;

-- Clean up
DROP TABLE temp_party_data;
