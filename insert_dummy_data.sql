-- Insert 200 dummy persons
WITH dummy_persons AS (
  SELECT 
    gen_random_uuid() as id,
    (ARRAY['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
           '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Kyle', 'Lisa', 'Noah', 'Nancy', 'Alan', 
           'Karen', 'Carl', 'Betty', 'Wayne', 'Helen', 'Ralph', 'Sandra', 'Roy', 'Donna', 'Eugene', 
           'Carol', 'Louis', 'Ruth', 'Philip', 'Sharon', 'Bobby', 'Michelle', 'Johnny', 'Laura'])[
      (random() * 120 + 1)::int
    ] as first_name,
    (ARRAY['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 
           'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 
           'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 
           'Clark', 'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King', 'Wright', 
           'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 
           'Hall', 'Rivera', 'Campbell', 'Mitchell', 'Carter', 'Roberts', 'Gomez', 'Phillips', 
           'Evans', 'Turner', 'Diaz', 'Parker', 'Cruz', 'Edwards', 'Collins', 'Reyes', 'Stewart', 
           'Morris', 'Morales', 'Murphy', 'Cook', 'Rogers', 'Gutierrez', 'Ortiz', 'Morgan', 
           'Cooper', 'Peterson', 'Bailey', 'Reed', 'Kelly', 'Howard', 'Ramos', 'Kim', 'Cox', 
           'Ward', 'Richardson', 'Watson', 'Brooks', 'Chavez', 'Wood', 'James', 'Bennett', 'Gray', 
           'Mendoza', 'Ruiz', 'Hughes', 'Price', 'Alvarez', 'Castillo', 'Sanders', 'Patel', 
           'Myers', 'Long', 'Ross', 'Foster', 'Jimenez', 'Powell', 'Jenkins', 'Perry', 'Russell', 
           'Sullivan', 'Bell', 'Coleman', 'Butler', 'Henderson', 'Barnes', 'Gonzales', 'Fisher', 
           'Vasquez', 'Simmons', 'Romero', 'Jordan', 'Patterson', 'Alexander', 'Hamilton', 'Graham', 
           'Reynolds', 'Griffin', 'Wallace', 'Moreno', 'West', 'Cole', 'Hayes', 'Bryant'])[
      (random() * 120 + 1)::int
    ] as last_name,
    (date '1950-01-01' + (random() * (date '2005-01-01' - date '1950-01-01'))::int) as birth_date,
    (ARRAY['US', 'CA', 'GB', 'DE', 'FR', 'IT', 'ES', 'AU', 'NL', 'SE', 'NO', 'DK', 'FI', 'BE', 'AT', 'CH', 'IE', 'PT', 'GR', 'PL'])[
      (random() * 20 + 1)::int
    ] as country_code,
    (ARRAY['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 
           'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 
           'Charlotte', 'San Francisco', 'Indianapolis', 'Seattle', 'Denver', 'Washington', 'Boston', 
           'El Paso', 'Nashville', 'Detroit', 'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 
           'Louisville', 'Baltimore', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento', 
           'Kansas City', 'Mesa', 'Atlanta', 'Omaha', 'Colorado Springs', 'Raleigh', 'Long Beach', 
           'Virginia Beach', 'Miami', 'Oakland', 'Minneapolis', 'Tulsa', 'Tampa', 'Arlington', 
           'New Orleans'])[
      (random() * 50 + 1)::int
    ] as city,
    now() as created_at,
    false as is_deleted,
    generate_series as row_num
  FROM generate_series(1, 200)
)
INSERT INTO persons (id, first_name, last_name, email, phone, id_document, birth_date, country_code, city, address, created_at, updated_at, created_by, updated_by, deleted_at, is_deleted)
SELECT 
  id,
  first_name,
  last_name,
  lower(first_name) || '.' || lower(last_name) || row_num || '@example.com' as email,
  '+1' || lpad((random() * 9999999999)::bigint::text, 10, '0') as phone,
  'ID' || lpad(row_num::text, 8, '0') as id_document,
  birth_date,
  country_code,
  city,
  (random() * 9999 + 1)::int || ' ' || 
  (ARRAY['Main St', 'Oak Ave', 'Pine St', 'Maple Dr', 'Cedar Ln', 'Elm St', 'Park Ave', 'First St', 'Second St', 'Third St'])[
    (random() * 10 + 1)::int
  ] as address,
  created_at,
  null as updated_at,
  null as created_by,
  null as updated_by,
  null as deleted_at,
  is_deleted
FROM dummy_persons;

-- Create travel parties and travelers from 100 randomly selected persons
WITH selected_persons AS (
  SELECT id, first_name, last_name, row_number() OVER (ORDER BY random()) as rn
  FROM persons
  WHERE email LIKE '%@example.com'
  LIMIT 100
),
party_assignments AS (
  SELECT
    id,
    first_name,
    last_name,
    -- Assign party sizes: 30% parties of 2, 40% parties of 3, 30% parties of 4
    CASE
      WHEN rn <= 30 THEN ((rn - 1) / 2) + 1  -- 15 parties of 2 (30 people)
      WHEN rn <= 70 THEN 15 + ((rn - 31) / 3) + 1  -- ~13 parties of 3 (39 people)
      ELSE 28 + ((rn - 71) / 4) + 1  -- ~7 parties of 4 (28+ people)
    END as party_number,
    CASE
      WHEN rn <= 30 THEN 2
      WHEN rn <= 70 THEN 3
      ELSE 4
    END as party_size,
    rn
  FROM selected_persons
),
travel_parties_data AS (
  SELECT DISTINCT
    gen_random_uuid() as party_id,
    party_number,
    party_size,
    (SELECT id FROM party_assignments pa2 WHERE pa2.party_number = pa1.party_number ORDER BY rn LIMIT 1) as main_contact_id
  FROM party_assignments pa1
),
inserted_travel_parties AS (
  INSERT INTO travel_parties (id, trip_id, main_contact_id, trip_destination_id, created_at, is_deleted)
  SELECT
    party_id,
    '01985a39-dd62-7976-a44b-f22b46f36bb7'::uuid as trip_id,
    main_contact_id,
    '01985a68-f73e-752d-9c41-5b8e3d4651f0'::uuid as trip_destination_id,
    now() as created_at,
    false as is_deleted
  FROM travel_parties_data
  RETURNING id, main_contact_id
)
INSERT INTO travelers (id, travel_party_id, person_id, status, price, insurance_price, tax_price, discount, travel_service_type, created_at, is_deleted)
SELECT
  gen_random_uuid() as id,
  tpd.party_id as travel_party_id,
  pa.id as person_id,
  1 as status, -- Assuming 1 is a valid status
  200.0 as price,
  12.0 as insurance_price,
  10.0 as tax_price,
  0.0 as discount,
  1 as travel_service_type, -- Assuming 1 is a valid service type
  now() as created_at,
  false as is_deleted
FROM party_assignments pa
JOIN travel_parties_data tpd ON pa.party_number = tpd.party_number;
