param defaultDomain string
param domainVerificationId string
param subdomain string
param containerAppName string

resource dnsZone 'Microsoft.Network/dnsZones@2023-07-01-preview' existing = {
  name: 'odmori.net'

  resource cname 'CNAME' = {
    name: subdomain
    properties: {
      TTL: 3600
      CNAMERecord: {
        cname: '${containerAppName}.${defaultDomain}'
      }
    }
  }
  resource verification 'TXT' = {
    name: 'asuid.${subdomain}'
    properties: {
      TTL: 3600
      TXTRecords: [
        {
          value: [domainVerificationId]
        }
      ]
    }
  }
}

