param storageAccountName string
@allowed(['dev', 'prod'])
param env string
param location string

resource storageAccount 'Microsoft.Storage/storageAccounts@2024-01-01' = {
  name: storageAccountName
  location: location
  kind: 'StorageV2'
  sku: {
    name: 'Standard_LRS'
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
  properties: {
    allowBlobPublicAccess: true
  }
}

var corsOriginsMap = {
  dev: [
    'http://localhost:3000'
    'https://admin.dev.odmori.net'
  ]
  prod: [
    'https://admin.odmori.net'
  ]
}

resource blobServices 'Microsoft.Storage/storageAccounts/blobServices@2024-01-01' = {
  name: 'default'
  parent: storageAccount
  properties: {
    cors: {
      corsRules: [
        {
          allowedHeaders: [
            '*'
          ]
          allowedMethods: [
            'DELETE'
            'GET'
            'HEAD'
            'MERGE'
            'POST'
            'OPTIONS'
            'PUT'
            'PATCH'
          ]
          allowedOrigins: corsOriginsMap[env]
          exposedHeaders: [
            '*'
          ]
          maxAgeInSeconds: 86400
        }
      ]
    }
  }
}

resource publicContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2024-01-01' = {
  name: 'public'
  parent: blobServices
  properties: {
    publicAccess: 'Blob'
  }
}

output name string = storageAccount.name
