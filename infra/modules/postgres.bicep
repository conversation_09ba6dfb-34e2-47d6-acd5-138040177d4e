param location string
param env string
param postgresServerName string
param postgresAdminLogin string
@secure()
param postgresAdminLoginPassword string

resource postgresServer 'Microsoft.DBforPostgreSQL/flexibleServers@2025-01-01-preview' = {
  name: postgresServerName
  location: location
  sku: {
    name: 'Standard_B1ms'
    tier: 'Burstable'
  }
  properties: {
    version: '16'
    administratorLogin: postgresAdminLogin
    administratorLoginPassword: postgresAdminLoginPassword
    storage: {
      storageSizeGB: 32
    }
    network: {
      publicNetworkAccess: 'Enabled'
      
    }
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}

resource allowAzureServices 'Microsoft.DBforPostgreSQL/flexibleServers/firewallRules@2025-01-01-preview' = {
  parent: postgresServer
  name: 'AllowAzureServices'
  properties: {
    startIpAddress: '0.0.0.0'
    endIpAddress: '0.0.0.0' // This special range allows all Azure services
  }
}

resource odmoriDatabase 'Microsoft.DBforPostgreSQL/flexibleServers/databases@2025-01-01-preview' = {
  parent: postgresServer
  name: 'odmori'
}

resource odmoriDevDatabase 'Microsoft.DBforPostgreSQL/flexibleServers/databases@2025-01-01-preview' = {
  parent: postgresServer
  name: 'odmori-dev'
}

output serverName string = postgresServer.name
output databaseName string = odmoriDatabase.name
output databaseDevName string = odmoriDevDatabase.name
output fullyQualifiedDomainName string = postgresServer.properties.fullyQualifiedDomainName
