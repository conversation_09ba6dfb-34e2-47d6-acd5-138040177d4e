{"dev": {"environmentVariables": [{"name": "ASPNETCORE_ENVIRONMENT", "value": "Development"}, {"name": "ASPNETCORE_HTTP_PORTS", "value": "8080"}, {"name": "Serilog__WriteTo__0__Name", "value": "ApplicationInsights"}, {"name": "Serilog__WriteTo__0__Args__connectionString", "secretRef": "application-insights-cs"}, {"name": "Serilog__WriteTo__0__Args__telemetryConverter", "value": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}]}, "prod": {"environmentVariables": [{"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "ASPNETCORE_HTTP_PORTS", "value": "8080"}, {"name": "Serilog__WriteTo__0__Name", "value": "ApplicationInsights"}, {"name": "Serilog__WriteTo__0__Args__connectionString", "secretRef": "application-insights-cs"}, {"name": "Serilog__WriteTo__0__Args__telemetryConverter", "value": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}]}}