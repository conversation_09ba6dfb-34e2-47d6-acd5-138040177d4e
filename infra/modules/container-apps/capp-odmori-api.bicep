@description('Resource group location')
param location string

param env string

@description('Container apps environment name')
param containerAppsEnvName string

@description('Service name')
param serviceName string

param pgServerName string
param pgDatabaseName string
param pgAdminLogin string

@secure()
param pgAdminLoginPassword string

param imageName string

param tag string = 'latest'

param applicationInsightsName string

@secure()
param firebaseAdmin string

var subDomains = {
  dev: 'api.dev'
  prod: 'api'
}

var subDomain = subDomains[env]
var domain = '${subDomain}.odmori.net'

var containerAppName = 'capp-${serviceName}-${env}'

var environmentConfig = loadJsonContent('./environment-config.json')['${env}']

// Reference to container registry
resource acr 'Microsoft.ContainerRegistry/registries@2023-11-01-preview' existing = {
  name: 'acrodmori'
  scope: resourceGroup('rg-odmori-shared')
}

// Reference to container apps environment
resource cappsEnv 'Microsoft.App/managedEnvironments@2024-10-02-preview' existing = {
  name: containerAppsEnvName
}

resource appInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: applicationInsightsName
}

// Create subdomain in dns zone
module dnsSubdomain '../dns-zone.bicep' = {
  name: 'dnsOdmoriApi'
  scope: resourceGroup('rg-odmori-shared')
  params: {
    containerAppName: containerAppName
    subdomain: subDomain
    defaultDomain: cappsEnv.properties.defaultDomain
    domainVerificationId: cappsEnv.properties.customDomainConfiguration.customDomainVerificationId
  }
}

// Create managed ssl certificate
resource managedCertificate 'Microsoft.App/managedEnvironments/managedCertificates@2023-05-01' = {
  name: '${containerAppsEnvName}-managed-cert-${serviceName}'
  location: location
  parent: cappsEnv
  properties: {
    domainControlValidation: 'CNAME'
    subjectName: domain
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}

resource service 'Microsoft.App/containerApps@2023-05-01' = {
  dependsOn: [
    dnsSubdomain
  ]
  name: containerAppName
  location: location
  properties: {
    managedEnvironmentId: cappsEnv.id
    configuration: {
      ingress: {
        external: true
        targetPort: 8080
        customDomains: [
          {
            name: domain
            certificateId: managedCertificate.id
            bindingType: 'SniEnabled'
            // bindingType: 'Disabled'
          }
        ]
      }
      secrets: [
        {
          name: 'registry-password'
          value: acr.listCredentials().passwords[0].value
        }
        {
          name: 'application-insights-cs'
          value: appInsights.properties.ConnectionString
        }
        {
          name: 'postgres-cs'
          value: 'Server=${pgServerName}.postgres.database.azure.com;Database=${pgDatabaseName};Port=5432;User Id=${pgAdminLogin};Password=${pgAdminLoginPassword};Ssl Mode=Require;'
        }
        {
          name: 'firebase-admin'
          value: firebaseAdmin
        }
      ]
      registries: [
        {
          passwordSecretRef: 'registry-password'
          server: acr.properties.loginServer
          username: acr.listCredentials().username
        }
      ]
    }
    template: {
      containers: [
        {
          name: serviceName
          image: '${acr.properties.loginServer}/${imageName}:${tag}'
          env: concat(environmentConfig.environmentVariables, [
            {
              name: 'ConnectionStrings__Postgres'
              secretRef: 'postgres-cs'
            }
            {
              name: 'APPLICATIONINSIGHTS_CONNECTION_STRING'
              secretRef: 'application-insights-cs'
            }
            {
              name: 'Firebase__AdminServiceAccount'
              secretRef: 'firebase-admin'
            }
          ])
        }
      ]
      scale: {
        maxReplicas: 1
        minReplicas: 0
      }
    }
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}
