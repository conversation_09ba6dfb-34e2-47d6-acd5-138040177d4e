param containerAppsEnvName string
param env string
param location string
param logAnalyticsName string
param applicationInsightsName string

resource logAnalyticsWorkspace 'Microsoft.OperationalInsights/workspaces@2023-09-01' = {
  name: logAnalyticsName
  location: location
  properties: any({
    retentionInDays: 30
    features: {
      searchVersion: 1
    }
    sku: {
      name: 'PerGB2018'
    }
  })
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}

resource appInsights 'Microsoft.Insights/components@2020-02-02' = {
  name: applicationInsightsName
  location: location
  kind: 'web'
  properties: {
    Application_Type: 'web'
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}

resource pricingplan 'microsoft.insights/components/pricingPlans@2017-10-01' = {
  name: 'current'
  parent: appInsights
  properties: {
    cap: env == 'prod' ? 10 : 1
    planType: 'Basic'
    stopSendNotificationWhenHitCap: true
    warningThreshold: 90
    }
  }

resource containerAppEnvironment 'Microsoft.App/managedEnvironments@2024-10-02-preview' = {
  location: location
  name: containerAppsEnvName
  properties: {
    vnetConfiguration: {
      infrastructureSubnetId: null
    }
    daprAIInstrumentationKey: appInsights.properties.InstrumentationKey
    appLogsConfiguration: {
      destination: 'log-analytics'
      logAnalyticsConfiguration: {
        customerId: logAnalyticsWorkspace.properties.customerId
        sharedKey: logAnalyticsWorkspace.listKeys().primarySharedKey
      }
    }
  }
  tags: {
    Env: env
    Project: 'odmori.ba'
  }
}

output name string = containerAppEnvironment.name
output cappsEnvId string = containerAppEnvironment.id
output defaultDomain string = containerAppEnvironment.properties.defaultDomain
output appInsightsInstrumentationKey string = appInsights.properties.InstrumentationKey
