@allowed([ 'dev', 'prod' ])
param env string
param pgAdminLogin string

param location string = resourceGroup().location

@description('Resources to deploy')
param resources string

var pgServerName = 'pg-odmori-prod'
var containerAppsEnvName = 'cappsenv-odmori-${env}'
var applicationInsightsName = 'appi-odmori-${env}'
var logAnalyticsName = 'la-odmori-${env}'
var storageName = 'stodmori${env}'

var branches = {
  dev: 'dev'
  prod: 'main'
}

resource kv 'Microsoft.KeyVault/vaults@2024-12-01-preview' existing = {
  name: 'kv-odmori-${env}'
  scope: resourceGroup()
}


module postgresModule 'modules/postgres.bicep' = if (empty(resources) || contains(resources, 'postgresModule')) {
  name: '${deployment().name}--postgres'
  params: {
    location: location
    env: env
    postgresServerName: pgServerName
    postgresAdminLogin: pgAdminLogin
    postgresAdminLoginPassword: kv.getSecret('pgserver-admin')
  }
}

module storageModule 'modules/storage.bicep' = if (empty(resources) || contains(resources, 'storageModule')) {
  name: '${deployment().name}--storage'
  params: {
    storageAccountName: storageName
    location: location
    env: env
  }
}


module containerAppsEnvironmentModule 'modules/capps-env.bicep' = if (empty(resources) || contains(resources, 'containerAppsEnvironmentModule')) {
  name: '${deployment().name}--container-apps-environment'
  dependsOn: [
    postgresModule
  ]
  params: {
    containerAppsEnvName: containerAppsEnvName
    env: env
    location: location
    applicationInsightsName: applicationInsightsName
    logAnalyticsName: logAnalyticsName
  }
}

module odmoriApiModule 'modules/container-apps/capp-odmori-api.bicep' = if (empty(resources) || contains(resources, 'odmoriApiModule')) {
  name: '${deployment().name}--odmori-api'
  dependsOn: [
    containerAppsEnvironmentModule
  ]
  params: {
    location: location
    env: env
    containerAppsEnvName: containerAppsEnvName
    serviceName: 'odmori-api'
    imageName: 'odmori-api-${env}'
    tag: 'latest'
    pgServerName: pgServerName
    pgDatabaseName: postgresModule.outputs.databaseName
    pgAdminLogin: pgAdminLogin
    pgAdminLoginPassword: kv.getSecret('pgserver-admin')
    applicationInsightsName: applicationInsightsName
    firebaseAdmin: kv.getSecret('firebase-admin-service-account')
  }
}

module odmoriWebModule 'modules/container-apps/capp-odmori-web.bicep' = if (empty(resources) || contains(resources, 'odmoriWebModule')) {
  name: '${deployment().name}--odmori-web'
  dependsOn: [
    containerAppsEnvironmentModule
  ]
  params: {
    location: location
    env: env
    containerAppsEnvName: containerAppsEnvName
    serviceName: 'odmori-web'
    imageName: 'odmori-web-${env}'
    tag: 'latest'
    pgServerName: pgServerName
    pgDatabaseName: postgresModule.outputs.databaseName
    pgAdminLogin: pgAdminLogin
    pgAdminLoginPassword: kv.getSecret('pgserver-admin')
    applicationInsightsName: applicationInsightsName
    storageName: storageName
    firebaseAdmin: kv.getSecret('firebase-admin-service-account')
  }
}
