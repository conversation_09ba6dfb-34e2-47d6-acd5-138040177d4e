<root>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="StartDateMustBeBeforeEndDate" xml:space="preserve">
        <value>Početni datum mora biti prije kranjeg datuma</value>
    </data>
    <data name="Required" xml:space="preserve">
        <value>{PropertyName} je obavezno</value>
    </data>
    <data name="MaxLength" xml:space="preserve">
        <value>Maksimalna dužina za {PropertyName} je {MaxLength}</value>
    </data>
    <data name="MinLength" xml:space="preserve">
        <value>Minimalna dužina za {PropertyName} je {MinLength}</value>
    </data>
    <data name="InvalidValue" xml:space="preserve">
        <value>{PropertyName} ima neispravnu vrijednost {PropertyValue}</value>
    </data>
    <data name="LessThanOrEqualTo" xml:space="preserve">
        <value>{PropertyName} mora biti manje ili jednako od {ComparisonValue}</value>
    </data>
    <data name="InvalidLength" xml:space="preserve">
        <value>Dužina od {PropertyName} mora biti jednaka {ComparisonValue}</value>
    </data>
    <data name="GreaterThanOrEqualTo" xml:space="preserve">
        <value>{PropertyName} mora biti veće ili jednako od {ComparisonValue}</value>
    </data>
    <data name="GreaterThan" xml:space="preserve">
        <value>{PropertyName} mora biti veće od {ComparisonValue}</value>
    </data>
    <data name="StartDateMustBeBeforeOrEqualToEndDate" xml:space="preserve">
        <value>Početni datum mora biti manji ili jednak krajnjem datumu</value>
    </data>
</root>