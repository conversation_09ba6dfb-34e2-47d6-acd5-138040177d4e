<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <EmbeddedResource Update="Errors.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>Errors.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Enums.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>Enums.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Enums.bs-latn-ba.resx">
          <DependentUpon>Enums.resx</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Update="Errors.bs-latn-ba.resx">
          <DependentUpon>Errors.resx</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Update="Validations.resx">
          <Generator>PublicResXFileCodeGenerator</Generator>
          <LastGenOutput>Validations.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Validations.bs-latn-ba.resx">
          <DependentUpon>Validations.resx</DependentUpon>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Errors.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Errors.resx</DependentUpon>
        </Compile>
        <Compile Update="Enums.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Enums.resx</DependentUpon>
        </Compile>
        <Compile Update="Validations.Designer.cs">
          <DesignTime>True</DesignTime>
          <AutoGen>True</AutoGen>
          <DependentUpon>Validations.resx</DependentUpon>
        </Compile>
    </ItemGroup>

</Project>
