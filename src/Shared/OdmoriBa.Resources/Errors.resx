<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="UserNotFound" xml:space="preserve">
        <value>User {0} not found</value>
    </data>
    <data name="PersonNotFound" xml:space="preserve">
        <value>Person {0} not found</value>
    </data>
    <data name="CityNotFound" xml:space="preserve">
        <value>City {0} not found</value>
    </data>
    <data name="DestinationNotFound" xml:space="preserve">
        <value>Destination {0} not found</value>
    </data>
    <data name="DestinationImageNotFound" xml:space="preserve">
        <value>Destination image {0} not found</value>
    </data>
    <data name="StopNotFound" xml:space="preserve">
        <value>Stop {id} not found</value>
    </data>
    <data name="FileRecordNotFound" xml:space="preserve">
        <value>File Record {id} not found</value>
    </data>
    <data name="ItineraryNotFound" xml:space="preserve">
        <value>Itinerary {0} not found</value>
    </data>
    <data name="PaymentNotFound" xml:space="preserve">
        <value>Payment {0} not found</value>
    </data>
    <data name="TravelerNotFound" xml:space="preserve">
        <value>Traveler {id} not found</value>
    </data>
    <data name="DeleteTravelerNotPossibleWithStatus" xml:space="preserve">
        <value>Traveler {0} cannot be delete because his status is: '{1}'</value>
    </data>
    <data name="DeleteTravelerNotPossibleWithPayments" xml:space="preserve">
        <value>Traveler {0} cannot be deleted because contains payments</value>
    </data>
    <data name="PersonExistInTrip" xml:space="preserve">
        <value>Person {0} exist in trip {1}</value>
    </data>
    <data name="TripNotFound" xml:space="preserve">
        <value>Trip {id} not found</value>
    </data>
    <data name="TripHasNoDestinations" xml:space="preserve">
        <value>Trip {id} has no destinations configured</value>
    </data>
    <data name="ReservationTimeExpired" xml:space="preserve">
        <value>Reservation time for trip {0} has expired</value>
    </data>
    <data name="CompanyNotFound" xml:space="preserve">
        <value>Company {0} not found</value>
    </data>
    <data name="BusNotFound" xml:space="preserve">
        <value>Bus {0} not found</value>
    </data>
</root>