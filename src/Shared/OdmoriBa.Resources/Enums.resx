<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="TravelerStatus_Requested" xml:space="preserve">
        <value>Requested</value>
    </data>
    <data name="TravelerStatus_Draft" xml:space="preserve">
        <value>Draft</value>
    </data>
    <data name="TravelerStatus_Confirmed" xml:space="preserve">
        <value>Confirmed</value>
    </data>
    <data name="TravelerStatus_Cancelled" xml:space="preserve">
        <value>Cancelled</value>
    </data>
    <data name="UserStatus_Active" xml:space="preserve">
        <value>Active</value>
    </data>
    <data name="UserStatus_Inactive" xml:space="preserve">
        <value>Inactive</value>
    </data>
    <data name="TripBusDirection_Departure" xml:space="preserve">
        <value>Departure</value>
    </data>
    <data name="TripBusDirection_Return" xml:space="preserve">
        <value>Return</value>
    </data>
</root>