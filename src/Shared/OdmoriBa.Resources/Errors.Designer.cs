//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace OdmoriBa.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Errors {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Errors() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("OdmoriBa.Resources.Errors", typeof(Errors).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        public static string UserNotFound {
            get {
                return ResourceManager.GetString("UserNotFound", resourceCulture);
            }
        }
        
        public static string PersonNotFound {
            get {
                return ResourceManager.GetString("PersonNotFound", resourceCulture);
            }
        }
        
        public static string CityNotFound {
            get {
                return ResourceManager.GetString("CityNotFound", resourceCulture);
            }
        }
        
        public static string DestinationNotFound {
            get {
                return ResourceManager.GetString("DestinationNotFound", resourceCulture);
            }
        }
        
        public static string DestinationImageNotFound {
            get {
                return ResourceManager.GetString("DestinationImageNotFound", resourceCulture);
            }
        }
        
        public static string StopNotFound {
            get {
                return ResourceManager.GetString("StopNotFound", resourceCulture);
            }
        }
        
        public static string FileRecordNotFound {
            get {
                return ResourceManager.GetString("FileRecordNotFound", resourceCulture);
            }
        }
        
        public static string ItineraryNotFound {
            get {
                return ResourceManager.GetString("ItineraryNotFound", resourceCulture);
            }
        }
        
        public static string PaymentNotFound {
            get {
                return ResourceManager.GetString("PaymentNotFound", resourceCulture);
            }
        }
        
        public static string TravelerNotFound {
            get {
                return ResourceManager.GetString("TravelerNotFound", resourceCulture);
            }
        }
        
        public static string DeleteTravelerNotPossibleWithStatus {
            get {
                return ResourceManager.GetString("DeleteTravelerNotPossibleWithStatus", resourceCulture);
            }
        }
        
        public static string DeleteTravelerNotPossibleWithPayments {
            get {
                return ResourceManager.GetString("DeleteTravelerNotPossibleWithPayments", resourceCulture);
            }
        }
        
        public static string PersonExistInTrip {
            get {
                return ResourceManager.GetString("PersonExistInTrip", resourceCulture);
            }
        }
        
        public static string TripNotFound {
            get {
                return ResourceManager.GetString("TripNotFound", resourceCulture);
            }
        }
        
        public static string TripHasNoDestinations {
            get {
                return ResourceManager.GetString("TripHasNoDestinations", resourceCulture);
            }
        }
        
        public static string ReservationTimeExpired {
            get {
                return ResourceManager.GetString("ReservationTimeExpired", resourceCulture);
            }
        }
        
        public static string CompanyNotFound {
            get {
                return ResourceManager.GetString("CompanyNotFound", resourceCulture);
            }
        }
        
        public static string BusNotFound {
            get {
                return ResourceManager.GetString("BusNotFound", resourceCulture);
            }
        }
    }
}
