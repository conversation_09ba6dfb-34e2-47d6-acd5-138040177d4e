<root>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="UserNotFound" xml:space="preserve">
        <value>Korisnik {0} nije pronađen</value>
    </data>
        <data name="PersonNotFound" xml:space="preserve">
        <value>Osoba {0} nije pronađena</value>
    </data>
        <data name="CityNotFound" xml:space="preserve">
        <value>Grad {0} nije pronađen</value>
    </data>
        <data name="DestinationNotFound" xml:space="preserve">
        <value>Destinacija {0} nije pronađena</value>
    </data>
        <data name="DestinationImageNotFound" xml:space="preserve">
        <value>Slika destinacija {0} nije pronađena</value>
    </data>
        <data name="StopNotFound" xml:space="preserve">
        <value>Stanica {0} nije pronađena</value>
    </data>
        <data name="FileRecordNotFound" xml:space="preserve">
        <value>File Record {0} nije pronađen</value>
    </data>
        <data name="ItineraryNotFound" xml:space="preserve">
        <value>Itinerar {0} nije pronađen</value>
    </data>
        <data name="PaymentNotFound" xml:space="preserve">
        <value>Uplata {0} nije pronađena</value>
    </data>
        <data name="TravelerNotFound" xml:space="preserve">
        <value>Putnik {0} nije pronađen</value>
    </data>
        <data name="DeleteTravelerNotPossibleWithStatus" xml:space="preserve">
        <value>Putnik {0} ne može biti obrisan u statusu: {1}</value>
    </data>
        <data name="DeleteTravelerNotPossibleWithPayments" xml:space="preserve">
        <value>Putnik {0} ne može biti obrisan jer postoje uplate</value>
    </data>
        <data name="PersonExistInTrip" xml:space="preserve">
        <value>Osoba {0} postoji u putovanju {1}</value>
    </data>
    <data name="TripNotFound" xml:space="preserve">
        <value>Putovanje {0} nije pronađeno</value>
    </data>
    <data name="TripHasNoDestinations" xml:space="preserve">
        <value>Putovanje {0} nema konfigurisane destinacije</value>
    </data>
    <data name="ReservationTimeExpired" xml:space="preserve">
        <value>Vrijeme za rezervaciju putovanja {0} je isteklo</value>
    </data>
    <data name="CompanyNotFound" xml:space="preserve">
        <value>Kompanija {0} nije pronađena</value>
    </data>
    <data name="BusNotFound" xml:space="preserve">
        <value>Autobus {0} nije pronađen</value>
    </data>
</root>