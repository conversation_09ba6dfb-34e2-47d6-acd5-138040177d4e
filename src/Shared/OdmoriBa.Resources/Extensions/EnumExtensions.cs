using System.Collections.Concurrent;
using System.Reflection;
using System.Resources;
using OdmoriBa.Resources.Attributes;

namespace OdmoriBa.Resources.Extensions;

public static class EnumExtensions
{
    private static readonly ConcurrentDictionary<Type, ResourceManager> ResourceManagerCache = new();

    // Define the default Resource Type by convention
    private static readonly Type DefaultResourceType = typeof(Enums); // Your specified default: Resources.Enums

    public static string GetLocalized(this Enum enumValue)
    {
        var enumType = enumValue.GetType();
        var fieldInfo = enumType.GetField(enumValue.ToString());

        if (fieldInfo == null) return enumValue.ToString();

        // Check if the parameterless [Localized] attribute is present
        var localizedAttr = fieldInfo.GetCustomAttribute<LocalizedEnumAttribute>();
        if (localizedAttr == null)
        {
            // If the attribute is not present, don't attempt localization, just return name
            return enumValue.ToString();
        }

        // Attribute IS present, proceed with localization using conventions
        try
        {
            // 1. Resource Type is defined by convention/hardcoded
            var resourceType = DefaultResourceType;

            // 2. Resource Key is constructed by convention
            string resourceKey = $"{enumType.Name}_{enumValue}"; // e.g., "TravelerStatus_Requested"

            // Get or add ResourceManager from cache using the determined type
            var resourceManager = ResourceManagerCache.GetOrAdd(
                resourceType,
                type => new ResourceManager(type) // Use the hardcoded type
            );

            // ResourceManager.GetString uses CultureInfo.CurrentUICulture implicitly
            string? localizedValue = resourceManager.GetString(resourceKey); // Use the constructed key

            // Fallback to enum name if key is missing in resource file
            return localizedValue ?? enumValue.ToString();
        }
        catch // Catch potential errors (e.g., MissingManifestResourceException)
        {
            return enumValue.ToString(); // Fallback on error
        }
    }
}