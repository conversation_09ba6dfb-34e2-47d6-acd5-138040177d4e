using OdmoriBa.Application.Features.Destinations.Commands;
using OdmoriBa.Presentation.Features.Destinations.Models;

namespace OdmoriBa.Presentation.Features.Destinations.Mappers;

public static class CityMappers
{
    public static CreateCityCommand ToCommand(this SaveCityRequest request) =>
        new(request.Name!, request.CountryCode!);

    public static UpdateCityCommand ToCommand(this SaveCityRequest request, Guid id) =>
        new(id, request.Name!, request.CountryCode!);
}