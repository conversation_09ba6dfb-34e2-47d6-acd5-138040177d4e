using OdmoriBa.Application.Features.Destinations.Commands;
using OdmoriBa.Presentation.Features.Destinations.Models;

namespace OdmoriBa.Presentation.Features.Destinations.Mappers;

public static class DestinationMappers
{
    public static CreateDestinationCommand ToCommand(this SaveDestinationRequest request) =>
        new(request.Name!);

    public static UpdateDestinationCommand ToCommand(this SaveDestinationRequest request, Guid id) =>
        new(id, request.Name!, request.CoverId);

    public static UpdateDestinationImageCommand ToCommand(this UpdateDestinationImageRequest request,
        Guid destinationId, Guid id) =>
        new(destinationId, id, request.Title, request.Description);
}