using OdmoriBa.Application.Features.Destinations.Commands;
using OdmoriBa.Presentation.Features.Destinations.Models;

namespace OdmoriBa.Presentation.Features.Destinations.Mappers;

public static class StopMappers
{
    public static CreateStopCommand ToCommand(this SaveStopRequest request) =>
        new(request.Address!, request.CityId, request.Longitude, request.Latitude, request.Description);

    public static UpdateStopCommand ToCommand(this SaveStopRequest request, Guid id) =>
        new(id, request.Address!, request.CityId, request.Longitude, request.Latitude, request.Description);
}