namespace OdmoriBa.Presentation.Features.Destinations.Models;

public sealed class SaveDestinationRequest
{
    public string? Name { get; set; }
    public Guid? CoverId { get; set; }
}

public sealed class SaveDestinationRequestValidator : AbstractValidator<SaveDestinationRequest>
{
    public SaveDestinationRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.Required);
    }
}