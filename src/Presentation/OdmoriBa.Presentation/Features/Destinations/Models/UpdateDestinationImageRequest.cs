namespace OdmoriBa.Presentation.Features.Destinations.Models;

public sealed class UpdateDestinationImageRequest
{
    public string? Title { get; set; }
    public string? Description { get; set; }
}

public sealed class UpdateDestinationImageRequestValidator : AbstractValidator<UpdateDestinationImageRequest>
{
    public UpdateDestinationImageRequestValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(500)
            .WithMessage(Resources.Validations.Required);
    }
}