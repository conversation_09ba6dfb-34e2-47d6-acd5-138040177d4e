namespace OdmoriBa.Presentation.Features.Destinations.Models;

public sealed class SaveStopRequest
{
    public string? Address { get; set; }
    public Guid CityId { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }
    public string? Description { get; set; }
}

public sealed class SaveStopRequestValidator : AbstractValidator<SaveStopRequest>
{
    public SaveStopRequestValidator()
    {
        RuleFor(x => x.Address)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.CityId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .WithName("City");
    }
}