namespace OdmoriBa.Presentation.Features.Destinations.Models;

public sealed class SaveCityRequest
{
    public string? Name { get; set; }
    public string? CountryCode { get; set; }
}

public sealed class SaveCityRequestValidator : AbstractValidator<SaveCityRequest>
{
    public SaveCityRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.CountryCode)
            .NotEmpty()
            .WithName("Država")
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(2)
            .WithMessage(Resources.Validations.MaxLength);
    }
}