namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class CloneTripRequest
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public sealed class CloneTripRequestValidator : AbstractValidator<CloneTripRequest>
{
    public CloneTripRequestValidator()
    {
        RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x)
            .Must(x => x.StartDate < x.EndDate)
            .WithMessage(Resources.Validations.StartDateMustBeBeforeOrEqualToEndDate);
    }
}