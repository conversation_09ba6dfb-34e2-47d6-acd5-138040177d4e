using OdmoriBa.Application.Features.Customers.Models;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class CreateReservationRequest
{
    public Guid TripDestinationId { get; set; }
    public string? RequestNote { get; set; }
    public Guid? DeparturePointId { get; set; }
    public List<PersonDto> AdditionalPersons { get; set; } = [];
    public int? AppliedPoints { get; set; }
}

public sealed class CreateReservationRequestValidator : AbstractValidator<CreateReservationRequest>
{
    public CreateReservationRequestValidator()
    {
        RuleFor(x => x.TripDestinationId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.AppliedPoints)
            .GreaterThan(0)
            .When(s => s.AppliedPoints.HasValue)
            .WithMessage(Resources.Validations.MinLength);

        RuleForEach(x => x.AdditionalPersons)
            .ChildRules(person =>
            {
                person.When(p => p.Id == Guid.Empty, () =>
                {
                    person.RuleFor(p => p.FirstName)
                        .NotEmpty().WithMessage(Resources.Validations.Required);

                    person.RuleFor(p => p.LastName)
                        .NotEmpty().WithMessage(Resources.Validations.Required);

                    person.RuleFor(p => p.BirthDate)
                        .NotEmpty().WithMessage(Resources.Validations.Required);

                    person.RuleFor(p => p.Email)
                        .EmailAddress()
                        .When(p => !string.IsNullOrEmpty(p.Email))
                        .WithMessage(Resources.Validations.InvalidValue);
                });
            });
    }
}