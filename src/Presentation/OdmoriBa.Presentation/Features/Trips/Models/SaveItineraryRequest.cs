namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveItineraryRequest
{
    public Guid TripDestinationId { get; set; }
    public DateTime? DateTime { get; set; }
    public string Title { get; set; } = null!;
    public string Description { get; set; } = null!;
}

public sealed class SaveItineraryRequestValidator : AbstractValidator<SaveItineraryRequest>
{
    public SaveItineraryRequestValidator()
    {
        RuleFor(x => x.TripDestinationId)
            .NotEmpty()
            .WithName("Trip destination")
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.DateTime)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Description)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}