using OdmoriBa.Application.Features.Travelers.Models;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class CreateTravelPartyRequest
{
    public Guid TripId { get; set; }
    public Guid TripDestinationId { get; set; }
    public Guid MainContactId { get; set; }
    public List<TravelPartyTravelerCreateDto>? Travelers { get; set; }
    public string? Note { get; set; }
}

public sealed class CreateTravelPartyRequestValidator : AbstractValidator<CreateTravelPartyRequest>
{
    public CreateTravelPartyRequestValidator()
    {
        RuleFor(x => x.TripId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.TripDestinationId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.MainContactId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Travelers)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .DependentRules(() =>
            {
                RuleFor(x => x.MainContactId)
                    .Must((a, b) => a.Travelers!.Any(s => s.PersonId == b))
                    .WithMessage("Main contact must be one of the travelers");
            });
    }
}