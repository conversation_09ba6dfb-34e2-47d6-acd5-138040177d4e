namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class UpdateTravelPartyRequest
{
    public Guid MainContactId { get; set; }
    public string? Note { get; set; }
}

public sealed class UpdateTravelPartyRequestValidator : AbstractValidator<UpdateTravelPartyRequest>
{
    public UpdateTravelPartyRequestValidator()
    {
        RuleFor(x => x.MainContactId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}