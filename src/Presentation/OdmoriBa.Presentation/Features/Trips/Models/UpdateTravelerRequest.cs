namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class UpdateTravelerRequest
{
    public Guid TravelPartyId { get; set; }
    public double Price { get; set; }
    public double InsurancePrice { get; set; }
    public double TaxPrice { get; set; }
    public double Discount { get; set; }
    public string? Note { get; set; }
    public Guid? DeparturePointId { get; set; }
    public Guid? ReturnDeparturePointId { get; set; }
}

public sealed class UpdateTravelerRequestValidator : AbstractValidator<UpdateTravelerRequest>
{
    public UpdateTravelerRequestValidator()
    {
        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.InsurancePrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.TaxPrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.Discount)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.TravelPartyId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}