using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveTripBusRequest
{
    public Guid BusId { get; set; }
    public string? Name { get; set; }
    public TripBusDirection Direction { get; set; }
}

public sealed class SaveTripBusRequestValidator : AbstractValidator<SaveTripBusRequest>
{
    public SaveTripBusRequestValidator()
    {
        RuleFor(x => x.BusId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}