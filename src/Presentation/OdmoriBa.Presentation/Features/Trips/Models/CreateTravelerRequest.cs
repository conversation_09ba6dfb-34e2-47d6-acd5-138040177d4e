namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class CreateTravelerRequest
{
    public Guid PersonId { get; set; }
    public double Discount { get; set; }
    public Guid? DeparturePointId { get; set; }
    public Guid? ReturnDeparturePointId { get; set; }
    public string? Note { get; set; }
}

public sealed class CreateTravelerRequestValidator : AbstractValidator<CreateTravelerRequest>
{
    public CreateTravelerRequestValidator()
    {
        RuleFor(x => x.PersonId)
            .NotEmpty()
            .WithName("Person")
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.Discount)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
    }
}