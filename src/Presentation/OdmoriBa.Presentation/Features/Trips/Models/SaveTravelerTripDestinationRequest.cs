namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveTravelerTripDestinationRequest
{
    public Guid? TripDestinationId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public sealed class SaveTravelerTripDestinationRequestValidator : AbstractValidator<SaveTravelerTripDestinationRequest>
{
    public SaveTravelerTripDestinationRequestValidator()
    {
        RuleFor(x => x.TripDestinationId)
            .NotNull()
            .WithName("Destination")
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}