using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveTripStopRequest
{
    public Guid StopId { get; set; }
    public DateTime? BeginAt { get; set; }
    public DateTime? EndAt { get; set; }
    public TripStopType? Type { get; set; }
    public string? Description { get; set; }
}

public sealed class SaveTripStopRequestValidator : AbstractValidator<SaveTripStopRequest>
{
    public SaveTripStopRequestValidator()
    {
        RuleFor(x => x.StopId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.BeginAt)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.EndAt)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Type)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}