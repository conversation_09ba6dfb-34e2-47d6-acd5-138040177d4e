namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class ApplyLoyaltyDiscountRequest
{
    public int Points { get; set; }
}

public sealed class ApplyLoyaltyDiscountRequestValidator
    : AbstractValidator<ApplyLoyaltyDiscountRequest>
{
    public ApplyLoyaltyDiscountRequestValidator()
    {
        RuleFor(x => x.Points)
            .GreaterThan(0)
            .WithMessage(Resources.Validations.GreaterThan);
    }
}