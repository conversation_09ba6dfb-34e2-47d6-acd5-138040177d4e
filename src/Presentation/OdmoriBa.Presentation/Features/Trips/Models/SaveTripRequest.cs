using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveTripRequest
{
    public string Title { get; set; } = null!;
    public string? Description { get; set; }
    public TripType? Type { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public TransportationType? TransportationType { get; set; }
}

public sealed class SaveTripRequestValidator : AbstractValidator<SaveTripRequest>
{
    public SaveTripRequestValidator()
    {
        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.Type)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x)
            .Must(x => x.StartDate <= x.EndDate)
            .WithMessage(Resources.Validations.StartDateMustBeBeforeOrEqualToEndDate);

        RuleFor(x => x.TransportationType)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}