using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SavePaymentRequest
{
    public Guid? PaidByPersonId { get; set; }
    public double Amount { get; set; }
    public PaymentType? Type { get; set; }
    public DateTime? PaidAt { get; set; }
    public string? Note { get; set; }
}

public sealed class SavePaymentRequestValidator : AbstractValidator<SavePaymentRequest>
{
    public SavePaymentRequestValidator()
    {
        RuleFor(x => x.PaidByPersonId)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.Type)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.PaidAt)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}