using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Presentation.Features.Trips.Models;

public sealed class SaveTripDestinationRequest
{
    public Guid DestinationId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public double Price { get; set; }
    public double InsurancePrice { get; set; }
    public double TaxPrice { get; set; }
    public TripDestinationLoyalty Loyalty { get; set; } = new();
    public bool Featured { get; set; }
}

public sealed class SaveTripDestinationRequestValidator : AbstractValidator<SaveTripDestinationRequest>
{
    public SaveTripDestinationRequestValidator()
    {
        RuleFor(x => x.DestinationId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.Loyalty)
            .NotNull()
            .WithMessage(Resources.Validations.Required)
            .DependentRules(() =>
            {
                RuleFor(x => x.Loyalty.Points)
                    .NotNull()
                    .WithMessage(Resources.Validations.Required)
                    .GreaterThanOrEqualTo(0)
                    .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
                
                RuleFor(x => x.Loyalty.MaximumPointsToSpend)
                    .NotNull()
                    .WithMessage(Resources.Validations.Required)
                    .GreaterThanOrEqualTo(0)
                    .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
            });

        RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x)
            .Must(x => x.StartDate <= x.EndDate)
            .WithMessage(Resources.Validations.StartDateMustBeBeforeOrEqualToEndDate);
        
        RuleFor(x => x.Price)
            .NotNull()
            .WithMessage(Resources.Validations.Required)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.InsurancePrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
        
        RuleFor(x => x.TaxPrice)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);
    }
}