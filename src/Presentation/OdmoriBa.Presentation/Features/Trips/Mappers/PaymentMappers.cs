using OdmoriBa.Application.Features.Travelers.Commands;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.Presentation.Features.Trips.Mappers;

public static class PaymentMappers
{
    public static CreatePaymentCommand ToCreateCommand(this SavePaymentRequest request, Guid travelPartyId) =>
        new(
            travelPartyId,
            request.PaidByPersonId!.Value,
            request.Amount,
            request.PaidAt!.Value,
            request.Type!.Value,
            request.Note);

    public static UpdatePaymentCommand ToUpdateCommand(this SavePaymentRequest request, Guid travelPartyId, Guid id) =>
        new(
            travelPartyId,
            id,
            request.PaidByPersonId!.Value,
            request.Amount,
            request.PaidAt!.Value,
            request.Type!.Value,
            request.Note);
}