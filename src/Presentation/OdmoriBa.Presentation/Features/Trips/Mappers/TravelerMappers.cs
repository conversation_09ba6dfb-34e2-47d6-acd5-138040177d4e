using OdmoriBa.Application.Features.Travelers.Commands;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.Presentation.Features.Trips.Mappers;

public static class TravelerMappers
{
    public static CreateTravelerCommand ToCommand(this CreateTravelerRequest request, Guid travelPartyId) =>
        new(
            travelPartyId,
            request.PersonId,
            request.Discount,
            request.DeparturePointId,
            request.ReturnDeparturePointId,
            request.Note
        );

    public static UpdateTravelerCommand ToCommand(this UpdateTravelerRequest request, Guid travelPartyId, Guid id) =>
        new(
            travelPartyId,
            id,
            request.Price,
            request.InsurancePrice,
            request.TaxPrice,
            request.Discount,
            request.Note,
            request.DeparturePointId,
            request.ReturnDeparturePointId
        );


    public static CreateTravelPartyCommand ToCommand(this CreateTravelPartyRequest request) =>
        new(
            request.TripId,
            request.TripDestinationId,
            request.MainContactId,
            request.Travelers!,
            request.Note!
        );

    public static UpdateTravelPartyCommand ToCommand(this UpdateTravelPartyRequest request, Guid id) =>
        new(
            id,
            request.MainContactId,
            request.Note!
        );
}