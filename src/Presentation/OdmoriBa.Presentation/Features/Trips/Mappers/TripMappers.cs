using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Application.Features.Travelers.Commands;
using OdmoriBa.Application.Features.Trips.Commands;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.Presentation.Features.Trips.Mappers;

public static class TripMappers
{
    public static CreateTripCommand ToCommand(this SaveTripRequest request) =>
        new(
            request.Title,
            request.Description,
            request.Type!.Value,
            request.StartDate!.Value.ToDateOnly(),
            request.EndDate!.Value.ToDateOnly(),
            request.TransportationType!.Value
        );

    public static UpdateTripCommand ToCommand(this SaveTripRequest request, Guid id) =>
        new(
            id,
            request.Title,
            request.Description,
            request.Type!.Value,
            request.StartDate!.Value.ToDateOnly(),
            request.EndDate!.Value.ToDateOnly(),
            request.TransportationType!.Value
        );

    public static CreateTripDestinationCommand ToCreateCommand(this SaveTripDestinationRequest request, Guid tripId) =>
        new(tripId,
            request.DestinationId,
            request.StartDate!.Value.ToDateOnly(),
            request.EndDate!.Value.ToDateOnly(),
            request.Price,
            request.InsurancePrice,
            request.TaxPrice,
            request.Loyalty,
            request.Featured);

    public static UpdateTripDestinationCommand ToUpdateCommand(this SaveTripDestinationRequest request, Guid tripId,
        Guid id) =>
        new(tripId,
            id,
            request.DestinationId,
            request.StartDate!.Value.ToDateOnly(),
            request.EndDate!.Value.ToDateOnly(),
            request.Price,
            request.InsurancePrice,
            request.TaxPrice,
            request.Loyalty,
            request.Featured);

    public static CreateTripStopCommand ToCreateCommand(this SaveTripStopRequest request, Guid tripId) =>
        new(
            tripId,
            request.StopId,
            request.BeginAt!.Value,
            request.EndAt!.Value,
            request.Type!.Value,
            request.Description
        );

    public static UpdateTripStopCommand ToUpdateCommand(this SaveTripStopRequest request, Guid tripId, Guid id) =>
        new(
            tripId,
            id,
            request.BeginAt!.Value,
            request.EndAt!.Value,
            request.Type!.Value,
            request.Description
        );

    public static CreateItineraryCommand ToCommand(this SaveItineraryRequest request) =>
        new(
            request.TripDestinationId,
            request.DateTime!.Value,
            request.Title,
            request.Description
        );

    public static UpdateItineraryCommand ToCommand(this SaveItineraryRequest request, Guid id) =>
        new(
            request.TripDestinationId,
            id,
            request.DateTime!.Value,
            request.Title,
            request.Description
        );

    public static CloneTripCommand ToCommand(this CloneTripRequest request, Guid tripId) =>
        new(
            tripId,
            request.StartDate!.Value.ToDateOnly(),
            request.EndDate!.Value.ToDateOnly()
        );

    public static CreateReservationCommand ToCommand(this CreateReservationRequest request, Guid userId, Guid tripId) =>
        new(
            userId,
            tripId,
            request.TripDestinationId,
            request.RequestNote,
            request.DeparturePointId,
            request.AdditionalPersons,
            request.AppliedPoints
        );

    public static CreateTripBusCommand ToCommand(this SaveTripBusRequest request, Guid tripId) =>
        new(tripId, request.BusId, request.Name!, request.Direction);

    public static UpdateTripBusCommand ToCommand(this SaveTripBusRequest request, Guid tripId, Guid id) =>
        new(tripId, id, request.BusId, request.Name!, request.Direction);

    public static ApplyLoyaltyDiscountCommand ToCommand(this ApplyLoyaltyDiscountRequest request, Guid travelPartyId, Guid personId) =>
        new(travelPartyId, personId, request.Points);
}