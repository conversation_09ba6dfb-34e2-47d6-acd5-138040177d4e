using OdmoriBa.Application.Features.Companies.Commands;
using OdmoriBa.Presentation.Features.Companies.Models;

namespace OdmoriBa.Presentation.Features.Companies.Mappers;

public static class CompanyMappers
{
    public static CreateCompanyCommand ToCommand(this SaveCompanyRequest request) =>
        new(request.Name,
            request.Address,
            request.ContactPhone,
            request.ContactEmail);

    public static UpdateCompanyCommand ToCommand(this SaveCompanyRequest request, Guid id) =>
        new(id,
            request.Name,
            request.Address,
            request.ContactPhone,
            request.ContactEmail);
}