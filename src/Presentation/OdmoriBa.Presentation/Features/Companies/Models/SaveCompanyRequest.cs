namespace OdmoriBa.Presentation.Features.Companies.Models;

public sealed class SaveCompanyRequest
{
    public string? Name { get; set; }
    public string? Address { get; set; }
    public Phone? ContactPhone { get; set; }
    public string? ContactEmail { get; set; }
}

public sealed class SaveCompanyRequestValidator : AbstractValidator<SaveCompanyRequest>
{
    public SaveCompanyRequestValidator()
    {
        RuleFor(p => p.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(p => p.Address)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.ContactEmail)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength)
            .EmailAddress()
            .When(x => !string.IsNullOrEmpty(x.ContactEmail))
            .WithMessage(Resources.Validations.InvalidValue);
    }
}