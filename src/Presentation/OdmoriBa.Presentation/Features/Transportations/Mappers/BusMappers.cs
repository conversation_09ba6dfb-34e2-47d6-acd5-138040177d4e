using OdmoriBa.Application.Features.Transportations.Commands;
using OdmoriBa.Presentation.Features.Transportations.Models;

namespace OdmoriBa.Presentation.Features.Transportations.Mappers;

public static class BusMappers
{
    public static CreateBusCommand ToCommand(this SaveBusRequest request) =>
        new(request.Name, request.Capacity, request.CompanyId);

    public static UpdateBusCommand ToCommand(this SaveBusRequest request, Guid id) =>
        new(id, request.Name, request.CompanyId, request.Capacity, request.Status);
}