using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Presentation.Features.Transportations.Models;

public sealed class SaveBusRequest
{
    public string Name { get; set; } = null!;
    public int Capacity { get; set; }
    public Guid CompanyId { get; set; }
    public BusStatus Status { get; set; }
}

public sealed class SaveBusRequestValidator : AbstractValidator<SaveBusRequest>
{
    public SaveBusRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Capacity)
            .GreaterThanOrEqualTo(0)
            .WithMessage(Resources.Validations.GreaterThanOrEqualTo);

        RuleFor(x => x.CompanyId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}