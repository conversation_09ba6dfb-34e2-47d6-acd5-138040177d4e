namespace OdmoriBa.Presentation.Features.Customers.Models;

public sealed class UpdateUserRequest
{
    public string? Email { get; set; }
    public Phone? Phone { get; set; }
}

public sealed class UpdateUserRequestValidator : AbstractValidator<UpdateUserRequest>
{
    public UpdateUserRequestValidator()
    {
        RuleFor(x => x.Email)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength)
            .EmailAddress()
            .WithMessage(Resources.Validations.InvalidValue);

        When(x => x.Phone != null, () =>
        {
            RuleFor(x => x.Phone!.Number)
                .MaximumLength(10)
                .WithMessage(Resources.Validations.MaxLength);

            RuleFor(x => x.Phone!.CountryCode)
                .MaximumLength(4)
                .WithMessage(Resources.Validations.MaxLength);
        });
    }
}