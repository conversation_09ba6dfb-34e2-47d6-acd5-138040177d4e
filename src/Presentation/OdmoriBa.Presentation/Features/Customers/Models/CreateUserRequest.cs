namespace OdmoriBa.Presentation.Features.Customers.Models;

public sealed class CreateUserRequest
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public Phone? Phone { get; set; }
    public string? IdDocument { get; set; }
    public DateTime? BirthDate { get; set; }
    public string? CountryCode { get; set; }
    public string? City { get; set; }
    public string? Address { get; set; }
    public string? SignInProvider { get; set; }
}

public sealed class CreateUserRequestValidator : AbstractValidator<CreateUserRequest>
{
    public CreateUserRequestValidator()
    {
        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.Email)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength)
            .EmailAddress()
            .WithMessage(Resources.Validations.InvalidValue);

        RuleFor(x => x.Phone)
            .NotNull()
            .WithMessage(Resources.Validations.Required)
            .DependentRules(() =>
            {
                RuleFor(x => x.Phone!.Number)
                    .MaximumLength(10)
                    .WithMessage(Resources.Validations.MaxLength);

                RuleFor(x => x.Phone!.CountryCode)
                    .MaximumLength(4)
                    .WithMessage(Resources.Validations.MaxLength);
            });

        RuleFor(x => x.IdDocument)
            .MaximumLength(15)
            .WithMessage(Resources.Validations.MaxLength);
        
        RuleFor(x => x.CountryCode)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(2)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.City)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.Address)
            .MaximumLength(100)
            .WithMessage(Resources.Validations.MaxLength);

        RuleFor(x => x.SignInProvider)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}