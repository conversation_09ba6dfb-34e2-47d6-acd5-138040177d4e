using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Presentation.Features.Customers.Models;

public sealed class SaveDeviceBindingRequest
{
    /// <summary>
    /// Push notification token
    /// </summary>
    public string PushNotificationToken { get; set; } = null!;

    public DeviceType? DeviceType { get; set; }

    /// <summary>
    /// Android ID, iOS identifierForVendor
    /// </summary>
    public string DeviceId { get; set; } = null!;

    /// <summary>
    /// Version of app (eg. 1.0.0)
    /// </summary>
    public string AppVersion { get; set; } = null!;

    /// <summary>
    /// OS version (eg. 18.1)
    /// </summary>
    public string OsVersion { get; set; } = null!;

    /// <summary>
    /// Device model (eg. iPhone 15 Pro)
    /// </summary>
    public string DeviceModel { get; set; } = null!;
}

public sealed class SaveDeviceBindingRequestValidator : AbstractValidator<SaveDeviceBindingRequest>
{
    public SaveDeviceBindingRequestValidator()
    {
        RuleFor(x => x.PushNotificationToken)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.DeviceType)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.DeviceId)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.DeviceModel)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.OsVersion)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.AppVersion)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}