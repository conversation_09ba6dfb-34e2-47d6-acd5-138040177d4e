using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Application.Features.Customers.Commands;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.Presentation.Features.Customers.Mappers;

public static class UserMappers
{
    public static CreateUserCommand ToCommand(this CreateUserRequest request, string firebaseUid) =>
        new(
            firebaseUid,
            request.FirstName!,
            request.LastName!,
            request.Email!,
            request.Phone!,
            request.IdDocument,
            request.BirthDate!.Value.ToDateOnly(),
            request.CountryCode,
            request.City,
            request.Address,
            request.SignInProvider
        );

    public static UpdateUserCommand ToCommand(this UpdateUserRequest request, Guid id) =>
        new(
            id,
            request.Email!,
            request.Phone!
        );

    public static SaveDeviceCommand ToCommand(this SaveDeviceBindingRequest request, Guid userId)
    {
        return new SaveDeviceCommand(userId, request.DeviceId, request.DeviceType!.Value, request.AppVersion, 
            request.OsVersion, request.DeviceModel, request.PushNotificationToken);
    }
}