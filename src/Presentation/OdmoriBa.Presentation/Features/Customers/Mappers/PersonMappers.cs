using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Application.Features.Customers.Commands;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.Presentation.Features.Customers.Mappers;

public static class PersonMappers
{
    public static CreatePersonCommand ToCommand(this SavePersonRequest request) =>
        new(
            request.FirstName!,
            request.LastName!,
            request.Email,
            request.Phone,
            request.IdDocument,
            request.BirthDate!.Value.ToDateOnly(),
            request.CountryCode,
            request.City,
            request.Address
        );

    public static UpdatePersonCommand ToCommand(this SavePersonRequest request, Guid id) =>
        new(
            id,
            request.FirstName!,
            request.LastName!,
            request.Email,
            request.Phone,
            request.IdDocument,
            request.BirthDate!.Value.ToDateOnly(),
            request.CountryCode,
            request.City,
            request.Address
        );
}