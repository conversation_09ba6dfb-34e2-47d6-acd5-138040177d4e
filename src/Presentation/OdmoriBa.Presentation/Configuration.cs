using System.Globalization;
using System.Reflection;
using Destructurama;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using SharpGrip.FluentValidation.AutoValidation.Mvc.Extensions;

namespace OdmoriBa.Presentation;

public static class Configuration
{
    public static WebApplicationBuilder AddPresentation(this WebApplicationBuilder builder)
    {
        builder.ConfigureSerilog();

        if (!builder.Environment.IsProduction())
        {
            builder.ConfigureOpenTelemetry();
        }
        
        builder.Services.AddValidation();
        
        builder.Services.AddLocalization();
        
        return builder;
    }

    public static IApplicationBuilder UsePresentation(this IApplicationBuilder app)
    {
        app.UseLocalization();
        return app;
    }

    private static IServiceCollection AddValidation(this IServiceCollection services)
    {
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly())
            .AddFluentValidationAutoValidation(o => o.DisableBuiltInModelValidation = true);

        return services;
    }

    private static WebApplicationBuilder ConfigureSerilog(this WebApplicationBuilder builder,
        string? serviceName = null)
    {
        serviceName ??= $"{builder.Environment.ApplicationName}.{builder.Environment.EnvironmentName}";
        builder.Host.UseSerilog(
            (_, loggerConfiguration) =>
            {
                loggerConfiguration.ReadFrom.Configuration(builder.Configuration);
                loggerConfiguration.Destructure.UsingAttributes();
                loggerConfiguration.WriteTo.OpenTelemetry(options => 
                    options.ResourceAttributes.Add("service.name", serviceName));
            });
        return builder;
    }
    
    public static WebApplicationBuilder ConfigureOpenTelemetry(this WebApplicationBuilder builder, string? serviceName = null)
    {
        serviceName ??= $"{builder.Environment.ApplicationName}.{builder.Environment.EnvironmentName}";
        
        var otelBuilder = builder.Services.AddOpenTelemetry();
        if (!builder.Environment.IsProduction())
        {
            otelBuilder.UseOtlpExporter();
        }

        otelBuilder.ConfigureResource(resource => resource.AddService(serviceName))
            .WithMetrics(metrics =>
            {
                metrics.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation();
            })
            .WithTracing(tracing =>
            {
                tracing.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddEntityFrameworkCoreInstrumentation();
            });


        return builder;
    }
    
    
    private static IApplicationBuilder UseLocalization(this IApplicationBuilder app)
    {
        var supportedCultures = new[]
        {
            new CultureInfo("bs-Latn-BA"),
            // new CultureInfo("en-US"),
        };

        var options = new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture("bs-Latn-BA"),
            SupportedCultures = supportedCultures,
            SupportedUICultures = supportedCultures
        };
        
        return app.UseRequestLocalization(options);
    }
}