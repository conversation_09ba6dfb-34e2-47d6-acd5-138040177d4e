using Microsoft.Extensions.Hosting;

namespace OdmoriBa.Presentation.Common.Extensions;

public static class EnvironmentExtensions
{
    public static bool IsTesting(this IHostEnvironment hostEnvironment) => hostEnvironment.IsEnvironment("Testing");
    public static bool IsLocal(this IHostEnvironment hostEnvironment) => hostEnvironment.IsEnvironment("Local");
    public static bool IsQa(this IHostEnvironment hostEnvironment) => hostEnvironment.IsEnvironment("QA");
}