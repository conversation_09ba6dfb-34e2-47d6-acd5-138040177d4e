using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.StaticData;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
public sealed class CountryController : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(ListResultDto<Country>), StatusCodes.Status200OK)]
    public ListResultDto<Country> Get()
    {
        return new ListResultDto<Country>(CountryData.All);
    }
}