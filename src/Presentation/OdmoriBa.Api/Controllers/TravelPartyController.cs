using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Presentation.Features.Trips.Mappers;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public sealed class TravelPartyController(IMediator mediator, IIdentityService identityService) : ControllerBase
{
    [HttpPost("{travelPartyId:guid}/LoyaltyDiscount")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IResult> ApplyLoyaltyDiscount(Guid travelPartyId, [FromBody] ApplyLoyaltyDiscountRequest request,
        CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(travelPartyId, identityService.UserId), cancellationToken))
            .ToResult();
    }
}