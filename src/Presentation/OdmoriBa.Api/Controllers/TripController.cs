using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Application.Features.Trips.Queries;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Core.Domains.Trips.Entities;
using OdmoriBa.Presentation.Features.Trips.Mappers;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
public sealed class TripController(IMediator mediator, IIdentityService identityService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(PaginatedResultDto<TripDto>), StatusCodes.Status200OK)]
    public async Task<IResult> Get([FromQuery] GetTripsQuery query, CancellationToken cancellationToken)
    {
        return (await mediator.Send(query with { Status = TripStatus.Published }, cancellationToken)).ToResult();
    }

    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TripDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> Get(Guid id, CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetTripByIdQuery(id), cancellationToken)).ToResult();
    }
    
    [HttpPost("{tripId:guid}/Reservation")]
    [ProducesResponseType(typeof(UserTravelPartyDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize]
    public async Task<IResult> CreateReservation(Guid tripId, [FromBody] CreateReservationRequest request, CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(identityService.UserId, tripId), cancellationToken)).ToResult();
    }
}