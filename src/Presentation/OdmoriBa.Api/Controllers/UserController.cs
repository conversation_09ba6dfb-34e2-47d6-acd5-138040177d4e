using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Presentation.Features.Customers.Mappers;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public sealed class UserController(IMediator mediator, IIdentityService identityService) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IResult> Create([FromBody] CreateUserRequest request, CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(identityService.IdentityId), cancellationToken)).ToResult();
    }

    [HttpPatch]
    [ProducesResponseType(typeof(UserDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> Update([FromBody] UpdateUserRequest request, CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(identityService.UserId), cancellationToken)).ToResult();
    }

    [HttpPost("DeviceBinding")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IResult> CreateDeviceBiding(SaveDeviceBindingRequest request,
        CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(identityService.UserId), cancellationToken)).ToResult();
    }
}