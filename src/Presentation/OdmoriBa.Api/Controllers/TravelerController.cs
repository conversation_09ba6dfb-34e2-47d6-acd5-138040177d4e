using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Travelers.Queries;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Application.Interfaces.Auth;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public sealed class TravelerController(IMediator mediator, IIdentityService identityService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(ListResultDto<UserTripListItemDto>), StatusCodes.Status200OK)]
    public async Task<IResult> GetUserTrips(CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetUserTripsQuery(identityService.UserId), cancellationToken)).ToResult();
    }

    [HttpGet("{travelerId:guid}")]
    [ProducesResponseType(typeof(TravelPartyDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> GetUserTripById(Guid travelerId, CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetUserTravelPartyQuery(identityService.UserId, travelerId), cancellationToken))
            .ToResult();
    }
}