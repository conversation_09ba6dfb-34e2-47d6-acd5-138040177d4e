using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Customers.Queries;
using OdmoriBa.Application.Features.Travelers.Queries;
using OdmoriBa.Application.Interfaces.Auth;
using OdmoriBa.Presentation.Features.Customers.Mappers;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public sealed class PersonController(IMediator mediator, IIdentityService identityService) : ControllerBase
{
    [HttpGet("Me")]
    [ProducesResponseType(typeof(PersonDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> GetMe(CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetPersonByIdQuery(identityService.UserId), cancellationToken)).ToResult();
    }

    [HttpPut("Me")]
    [ProducesResponseType(typeof(PersonDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> UpdateMe(SavePersonRequest request, CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(identityService.UserId), cancellationToken)).ToResult();
    }

    [HttpGet("RelatedPersons")]
    [ProducesResponseType(typeof(ListResultDto<PersonDto>), StatusCodes.Status200OK)]
    public async Task<IResult> GetRelatedPersons(CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetUserPartyPersonsQuery(identityService.UserId), cancellationToken))
            .ToResult();
    }

    [HttpGet("Loyalty")]
    [ProducesResponseType(typeof(LoyaltyCardDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IResult> GetLoyaltyCard(CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetLoyaltyCardQuery(identityService.UserId), cancellationToken)).ToResult();
    }
}