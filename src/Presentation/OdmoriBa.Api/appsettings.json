{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}, "Firebase": {"ProjectId": "odmori-ba-8a068", "Issuer": "https://securetoken.google.com/odmori-ba-8a068", "Authority": "https://securetoken.google.com/odmori-ba-8a068"}, "ConnectionStrings": {"Postgres": "Host=localhost;Port=5432;Database=odmori;Username=********;Password=********"}, "AllowedHosts": "*", "AzureStorage": {"PublicContainerUrl": "https://stodmoridev.blob.core.windows.net/public"}}