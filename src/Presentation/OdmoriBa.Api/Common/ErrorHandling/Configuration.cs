namespace OdmoriBa.Api.Common.ErrorHandling;

public static class Configuration
{
    public static IServiceCollection AddErrorHandling(this IServiceCollection services)
    {
        services.AddProblemDetails(options =>
        {
            options.CustomizeProblemDetails = context =>
            {
                if (string.IsNullOrEmpty(context.ProblemDetails.Instance))
                {
                    context.ProblemDetails.Instance =
                        $"{context.HttpContext.Request.Method} {context.HttpContext.Request.Path}";
                }
                context.ProblemDetails.Extensions.TryAdd("requestId", context.HttpContext.TraceIdentifier);
            };
        }).AddExceptionHandler<ApiExceptionHandler>();
        return services;
    }
}