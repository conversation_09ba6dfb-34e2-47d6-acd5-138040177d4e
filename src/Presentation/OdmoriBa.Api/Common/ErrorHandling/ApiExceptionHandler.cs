using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;

namespace OdmoriBa.Api.Common.ErrorHandling;

public class ApiExceptionHandler(IWebHostEnvironment environment, IProblemDetailsService problemDetailsService)
    : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception,
        CancellationToken cancellationToken)
    {
        int statusCode = StatusCodes.Status500InternalServerError; // Default

        if (exception is SecurityTokenException)
        {
            statusCode = StatusCodes.Status401Unauthorized;
        }
        
        var problemDetails = new ProblemDetails
        {
            Status = statusCode, 
            Detail = exception.Message,
            Instance = $"{httpContext.Request.Method} {httpContext.Request.Path}"
        };

        if (!environment.IsProduction())
        {
            problemDetails.Extensions = new Dictionary<string, object?> { ["stackTrace"] = exception.StackTrace };
        }
        
        httpContext.Response.StatusCode = statusCode;

        return await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
        {
            HttpContext = httpContext,
            ProblemDetails = problemDetails,
            Exception = exception,
        });
    }
}