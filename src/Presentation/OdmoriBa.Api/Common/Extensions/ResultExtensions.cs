using OdmoriBa.Core.Common;
using OdmoriBa.Core.Common.Errors;

namespace OdmoriBa.Api.Common.Extensions;

public static class ResultExtensions
{
    public static IResult ToResult<T>(this Result<T> result) where T : class
    {
        return result.Match(Results.Ok, error => error.ToProblemDetails());
    }

    public static IResult ToResult(this Result result)
    {
        return result.Match(() => Results.Ok(), error => error.ToProblemDetails());
    }

    private static IResult ToProblemDetails(this Error error)
    {
        if (error is null)
        {
            throw new InvalidOperationException();
        }

        return Results.Problem(
            title: error.Code,
            detail: error.Description,
            type: GetType(error.Type),
            statusCode: GetStatusCode(error.Type),
            extensions: error.Extensions);

        static string GetType(ErrorType errorType) =>
            errorType switch
            {
                ErrorType.Validation or ErrorType.Problem => "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                ErrorType.NotFound => "https://tools.ietf.org/html/rfc7231#section-6.5.4",
                ErrorType.Conflict => "https://tools.ietf.org/html/rfc7231#section-6.5.8",
                ErrorType.Failure => "https://tools.ietf.org/html/rfc7231#section-6.6.1",
                _ => throw new ArgumentOutOfRangeException(nameof(errorType), errorType, null)
            };

        static int GetStatusCode(ErrorType errorType) =>
            errorType switch
            {
                ErrorType.Validation or ErrorType.Problem => StatusCodes.Status400BadRequest,
                ErrorType.NotFound => StatusCodes.Status404NotFound,
                ErrorType.Conflict => StatusCodes.Status409Conflict,
                ErrorType.Failure => StatusCodes.Status500InternalServerError,
                _ => throw new ArgumentOutOfRangeException(nameof(errorType), errorType, null)
            };
    }
}