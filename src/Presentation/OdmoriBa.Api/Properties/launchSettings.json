{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5047", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:7014;http://localhost:5047", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}}}