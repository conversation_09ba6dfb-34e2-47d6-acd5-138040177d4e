using OdmoriBa.Web.Components.Pages.Destinations.Destination;
using OdmoriBa.Web.Components.Pages.Persons.Person;
using OdmoriBa.Web.Components.Pages.Trips.Trip;

namespace OdmoriBa.Web;

public static class Configuration
{
    public static IServiceCollection AddWeb(this IServiceCollection services)
    {
        return services.AddState();
    }

    public static IServiceCollection AddState(this IServiceCollection services)
    {
        services.AddScoped<TripState>();
        services.AddScoped<DestinationState>();
        services.AddScoped<PersonState>();
        return services;
    }
}