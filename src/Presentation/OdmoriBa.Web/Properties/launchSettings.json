{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:2450", "sslPort": 44370}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5209", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}, "https": {"commandName": "Project", "dotnetRunMessages": false, "launchBrowser": true, "applicationUrl": "https://localhost:7283;http://localhost:5209", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}}}