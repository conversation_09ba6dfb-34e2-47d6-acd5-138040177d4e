@page "/settings/companies"
@using OdmoriBa.Application.Features.Companies.Models
@using OdmoriBa.Application.Features.Companies.Queries
@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Firme</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Postavke", href: "/settings", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Firme", href: "/settings/companies", icon: Icons.Material.Filled.Work)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled"
               Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudTable Items="_elements" Hover="true" Dense="true" Striped="true" SortLabel="Sort By"
          Filter="new Func<CompanyDto,bool>(Filter)">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_searchTerm"
                          T="string" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

        </MudStack>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>
            Naziv
        </MudTh>
        <MudTh>
            Adresa
        </MudTh>
        <MudTh>
            Telefon
        </MudTh>
        <MudTh>
            Email
        </MudTh>
        <MudTh></MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="Naziv">@context.Name</MudTd>
        <MudTd DataLabel="Adresa">@context.Address</MudTd>
        <MudTd DataLabel="Telefon">@context.ContactPhone</MudTd>
        <MudTd DataLabel="Email">@context.ContactEmail</MudTd>
        <MudTd DataLabel="Akcije">
            <MudIconButton OnClick="@(_ => SaveAsync(context))" Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                           aria-label="Edit"/>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText>No records found</MudText>
    </NoRecordsContent>
    <LoadingContent>
        <MudText>Loading...</MudText>
    </LoadingContent>
    <PagerContent>
        <MudTablePager/>
    </PagerContent>
</MudTable>

@code {

    private List<CompanyDto> _elements = [];
    private string? _searchTerm;

    protected override async Task OnInitializedAsync()
    {
        var result = await Mediator.Send(new GetCompaniesQuery());

        _elements = result.Match(
            value => value.Items,
            error =>
            {
                Snackbar.Add($"Error loading companies: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task SaveAsync(CompanyDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveCompanyDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveCompanyDialog>($"{(data is null ? "Dodaj" : "Uredi")} firmu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (data is null)
            {
                _elements.Add((result.Data as CompanyDto)!);
            }
            else
            {
                _elements[_elements.FindIndex(s => s.Id == data.Id)] = (result.Data as CompanyDto)!;
            }
        }
    }

    private bool Filter(CompanyDto element)
    {
        return string.IsNullOrWhiteSpace(_searchTerm) || element.Name.ToLower().Contains(_searchTerm.ToLower());
    }

}