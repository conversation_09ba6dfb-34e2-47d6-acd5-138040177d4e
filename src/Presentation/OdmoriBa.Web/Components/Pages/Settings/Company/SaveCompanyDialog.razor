@using OdmoriBa.Application.Features.Companies.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Core.Common.ValueObjects
@using OdmoriBa.Presentation.Features.Companies.Mappers
@using OdmoriBa.Presentation.Features.Companies.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Naziv"/>
                
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Address"
                              For="@(() => _request.Address)"
                              Immediate="true"
                              Label="Adresa"/>
                
                
                <MudStack Row>
                    <MudStack Row Spacing="0">
                        <MudTextField
                            Style="width: 63px" Variant="Variant.Outlined" Margin="Margin.Dense"
                            @bind-Value="_request.ContactPhone!.CountryCode"
                            InputType="InputType.Telephone"
                            For="@(() => _request.ContactPhone!.CountryCode)"
                            Immediate="true"
                            Label="Poz."/>

                        <MudTextField
                            Variant="Variant.Outlined" Margin="Margin.Dense"
                            @bind-Value="_request.ContactPhone!.Number"
                            For="@(() => _request.ContactPhone!.Number)"
                            InputType="InputType.Telephone"
                            Immediate="true"
                            Label="Broj telefona"/>
                    </MudStack>
                    
                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.ContactEmail"
                                  For="@(() => _request.ContactEmail)"
                                  Immediate="true"
                                  Label="Email"/>
                </MudStack>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public CompanyDto? Data { get; set; }

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveCompanyRequest _request = new();
    private readonly SaveCompanyRequestValidator _validator = new();
    
    protected override void OnInitialized()
    {
        _request.ContactPhone = new Phone("+387", "");
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.Address = Data.Address;
            _request.ContactPhone = Data.ContactPhone;
            _request.ContactEmail = Data.ContactEmail;
        }
    }
    
    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Company {result.Value.Name} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

}