@page "/settings/stops"
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Stanice</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Postavke", href: "/settings", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Stanice", href: "/settings/stops", icon: Icons.Material.Filled.LocationOn)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled" Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudTable Items="_elements" Hover="true" Dense="true" Striped="true" SortLabel="Sort By"
          Filter="new Func<StopDto,bool>(Filter)">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_searchTerm"
                          T="string" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="CityDto?"
                             Label="Grad"
                             DebounceInterval="300"
                             SearchFunc="@SearchCities"
                             @bind-Value="_city"
                             ResetValueOnEmptyText="true"
                             Clearable="true"
                             ToStringFunc="@(c => c?.Name)"/>
        </MudStack>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>
            <MudTableSortLabel SortBy="new Func<StopDto, object>(x => x.City?.Name!)">Grad
            </MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel SortBy="new Func<StopDto, object>(x => x.Address!)">Adresa
            </MudTableSortLabel>
        </MudTh>
        <MudTh></MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="Grad">@context.City?.Name</MudTd>
        <MudTd DataLabel="Adresa">@context.Address</MudTd>
        <MudTd DataLabel="Akcije">
            <MudIconButton OnClick="@(_ => SaveAsync(context))" Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                           aria-label="Edit"/>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText>No records found</MudText>
    </NoRecordsContent>
    <LoadingContent>
        <MudText>Loading...</MudText>
    </LoadingContent>
    <PagerContent>
        <MudTablePager/>
    </PagerContent>
</MudTable>

@code {

    private List<StopDto> _elements = [];
    private List<CityDto> _cities = [];
    private string? _searchTerm;
    private CityDto? _city;

    protected override async Task OnInitializedAsync()
    {
        var result = await Mediator.Send(new GetStopsQuery());

        _elements = result.Match(
            value =>
            {
                _cities = value.Items.Select(s => s.City!).DistinctBy(c => c.Id).ToList();
                return value.Items;
            },
            error =>
            {
                Snackbar.Add($"Error loading countries: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task SaveAsync(StopDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveStopDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveStopDialog>($"{(data is null ? "Kreiraj" : "Uredi")} stanicu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (data is null)
            {
                _elements.Add((result.Data as StopDto)!);
            }
            else
            {
                _elements[_elements.FindIndex(s => s.Id == data.Id)] = (result.Data as StopDto)!;
            }
        }
    }

    private bool Filter(StopDto element)
    {
        return (string.IsNullOrWhiteSpace(_searchTerm) || 
                element.Address!.ToLower().Contains(_searchTerm.ToLower()) ||
                element.City!.Name.ToLower().Contains(_searchTerm.ToLower()))
               && (_city is null || element.CityId == _city.Id);
    }
    
    private Task<IEnumerable<CityDto>> SearchCities(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _cities.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
            );
        return Task.FromResult(result);
    }

}