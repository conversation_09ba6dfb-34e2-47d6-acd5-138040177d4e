@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Destinations.Mappers
@using OdmoriBa.Presentation.Features.Destinations.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Address"
                              For="@(() => _request.Address)"
                              Immediate="true"
                              Label="Adresa"/>

                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Grad"
                                 For="@(() => _request.CityId)"
                                 @bind-Value="_request.CityId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchCities"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _cities.GetValueOrDefault(c)?.Name!)"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public StopDto? Data { get; set; }

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveStopRequest _request = new();
    private readonly SaveStopRequestValidator _validator = new();

    private Dictionary<Guid, CityDto> _cities = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.CityId = Data!.CityId;
            _request.Address = Data!.Address;
            _request.Longitude = Data.Longitude;
            _request.Latitude = Data.Latitude;
            _request.Description = Data.Description;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var countriesResult = await Mediator.Send(new GetCitiesQuery());

        _cities = countriesResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading cities: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, CityDto>();
            });
    }

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Stop is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private Task<IEnumerable<Guid>> SearchCities(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _cities.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
        ).Select(s => s.Id);
        return Task.FromResult(result);
    }

}