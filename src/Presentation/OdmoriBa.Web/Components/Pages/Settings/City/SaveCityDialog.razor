@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.StaticData
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Destinations.Mappers
@using OdmoriBa.Presentation.Features.Destinations.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Naziv"/>

                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="string"
                                 Label="Drzava"
                                 For="@(() => _request.CountryCode!)"
                                 @bind-Value="_request.CountryCode"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchCountries"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => CountryData.GetNameWithFlag(c))"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public CityDto? Data { get; set; }

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveCityRequest _request = new();
    private readonly SaveCityRequestValidator _validator = new();

    private readonly Dictionary<string, Country> _countries = CountryData.Countries;

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.CountryCode = Data.CountryCode;
        }
    }
    
    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"City {result.Value.Name} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private Task<IEnumerable<string>> SearchCountries(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _countries.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
        ).Select(s => s.Code);
        return Task.FromResult(result);
    }

}