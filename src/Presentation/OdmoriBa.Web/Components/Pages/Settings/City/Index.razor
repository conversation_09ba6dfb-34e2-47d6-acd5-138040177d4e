@page "/settings/cities"
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Application.StaticData
@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Gradovi</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Postavke", href: "/settings", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Gradovi", href: "/settings/cities", icon: Icons.Material.Filled.LocationCity)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled" Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudTable Items="_elements" Hover="true" Dense="true" Striped="true" SortLabel="Sort By"
          Filter="new Func<CityDto,bool>(Filter)">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_searchTerm"
                          T="string" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Country?"
                             Label="Drzava"
                             DebounceInterval="300"
                             SearchFunc="@SearchCountries"
                             @bind-Value="_country"
                             ResetValueOnEmptyText="true"
                             Clearable="true"
                             ToStringFunc="@(c => $"{c?.Flag} {c?.Name}")"/>
        </MudStack>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>
            <MudTableSortLabel SortBy="new Func<CityDto, object>(x => x.Name)">Naziv
            </MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel SortBy="new Func<CityDto, object>(x => x.CountryCode)">Drzava
            </MudTableSortLabel>
        </MudTh>
        <MudTh></MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="Naziv">@context.Name</MudTd>
        <MudTd DataLabel="Drzava">@CountryData.GetNameWithFlag(context?.CountryCode)</MudTd>
        <MudTd DataLabel="Akcije">
            <MudIconButton OnClick="@(_ => SaveAsync(context))" Icon="@Icons.Material.Filled.Edit" Color="Color.Primary"
                           aria-label="Edit"/>
        </MudTd>
    </RowTemplate>
    <NoRecordsContent>
        <MudText>No records found</MudText>
    </NoRecordsContent>
    <LoadingContent>
        <MudText>Loading...</MudText>
    </LoadingContent>
    <PagerContent>
        <MudTablePager/>
    </PagerContent>
</MudTable>

@code {

    private List<CityDto> _elements = [];
    private List<Country> _countries = [];
    private string? _searchTerm;
    private Country? _country;

    protected override async Task OnInitializedAsync()
    {
        var result = await Mediator.Send(new GetCitiesQuery());

        _elements = result.Match(
            value =>
            {
                _countries = CountryData.All.Where(s => value.Items.Select(c => c.CountryCode).Contains(s.Code)).ToList();
                return value.Items;
            },
            error =>
            {
                Snackbar.Add($"Error loading cities: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task SaveAsync(CityDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveCityDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveCityDialog>($"{(data is null ? "Dodaj" : "Uredi")} grad", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (data is null)
            {
                _elements.Add((result.Data as CityDto)!);
            }
            else
            {
                _elements[_elements.FindIndex(s => s.Id == data.Id)] = (result.Data as CityDto)!;
            }
        }
    }

    private bool Filter(CityDto element)
    {
        return (string.IsNullOrWhiteSpace(_searchTerm) || element.Name.ToLower().Contains(_searchTerm.ToLower()))
               && (_country is null || element.CountryCode == _country.Code);
    }
    
    private Task<IEnumerable<Country>> SearchCountries(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _countries.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
            );
        return Task.FromResult(result);
    }

}