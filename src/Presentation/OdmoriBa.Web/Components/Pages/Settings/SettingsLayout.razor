@inherits LayoutComponentBase
@layout Layout.MainLayout

@Body

<MudDrawer Style="top: 0" Class="mt-10 pt-4" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Variant="@DrawerVariant.Mini">
    <MudNavMenu>
        <MudNavLink Href="settings/cities" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.LocationCity">Gradovi
        </MudNavLink>
        <MudNavLink Href="settings/stops" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.LocationOn">Stanice
        </MudNavLink>
        <MudNavLink Href="settings/companies" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Work">Firme
        </MudNavLink>
        <MudNavLink Href="settings/buses" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.DirectionsBus">Autobusi
        </MudNavLink>
    </MudNavMenu>
    <MudSpacer/>
    <MudStack Row>
        <MudSpacer/>
        <MudToggleIconButton
            ToggledChanged="@(_ => _drawerOpen = !_drawerOpen)"
            Toggled="_drawerOpen"
            Size="Size.Small"
            Icon="@Icons.Material.Filled.ArrowForward"
            ToggledIcon="@Icons.Material.Filled.ArrowBack"
            Color="Color.Inherit" Edge="Edge.Start"/>
    </MudStack>
</MudDrawer>

@code {
    private bool _drawerOpen;
}