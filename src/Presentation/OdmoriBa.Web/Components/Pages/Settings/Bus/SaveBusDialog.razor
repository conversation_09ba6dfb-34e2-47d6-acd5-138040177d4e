@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Companies.Models
@using OdmoriBa.Application.Features.Companies.Queries
@using OdmoriBa.Application.Features.Transportations.Models
@using OdmoriBa.Core.Domains.Transportations.Entities
@using OdmoriBa.Presentation.Features.Transportations.Mappers
@using OdmoriBa.Presentation.Features.Transportations.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudStack Row>
                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Name"
                                  For="@(() => _request.Name)"
                                  Immediate="true"
                                  Label="Naziv"/>
                
                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.Capacity" Label="Broj sjedišta" Variant="Variant.Outlined"/>
                
                </MudStack>
                
                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Firma"
                                 For="@(() => _request.CompanyId)"
                                 @bind-Value="_request.CompanyId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchCompanies"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _companies.GetValueOrDefault(c)?.Name!)"/>
                
                @if (_isEdit)
                {
                    <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                               T="BusStatus"
                               Label="Status"
                               @bind-Value="_request.Status"
                               For="@(() => _request.Status)">
                        @foreach (var item in Enum.GetValues<BusStatus>())
                        {
                            <MudSelectItem T="BusStatus" Value="item">
                                @item.GetDescription()
                            </MudSelectItem>
                        }
                    </MudSelect>
                }
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public BusDto? Data { get; set; }

    private MudForm _form = null!; 
    private bool _isEdit;
    private bool _loading;
    private Core.Common.Errors.Error? _error;

    private readonly SaveBusRequest _request = new();
    private readonly SaveBusRequestValidator _validator = new();

    private Dictionary<Guid, CompanyDto> _companies = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.CompanyId = Data.CompanyId;
            _request.Capacity = Data.Capacity;
            _request.Status = Data.Status;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        var countriesResult = await Mediator.Send(new GetCompaniesQuery());

        _companies = countriesResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading companies: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, CompanyDto>();
            });
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Stop is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog.Cancel();

    private Task<IEnumerable<Guid>> SearchCompanies(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _companies.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
        ).Select(s => s.Id);
        return Task.FromResult(result);
    }

}