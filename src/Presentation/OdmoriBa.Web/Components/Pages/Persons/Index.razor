@page "/persons"
@using OdmoriBa.Application.Features.Customers.Enums
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Customers.Queries

@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager
@inject IDialogService DialogService

<PageTitle>Osobe</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Osobe", href: "/persons", icon: Icons.Material.Filled.People)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SavePersonAsync())" Variant="Variant.Filled"
               Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudDataGrid @ref="_dataGrid" T="PersonDto" ServerData="ServerReload" Filterable="false" Striped="true" Hover="true"
             SortMode="SortMode.None" RowClick="@(row => NavigationManager.NavigateTo($"persons/{row.Item.Id}"))">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense"
                          @bind-Value="_query.SearchTerm" T="string"
                          @bind-Value:after="Filter" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.FirstName" Title="Ime"/>
        <PropertyColumn Property="x => x.LastName" Title="Prezime"/>
        <PropertyColumn Property="x => x.IdDocument" Title="LK/Pasoš"/>
        <PropertyColumn Property="x => x.City" Title="Grad"/>
        <PropertyColumn Property="x => x.Address" Title="Adresa"/>
        <PropertyColumn Property="x => x.BirthDate" Title="Datum rođenja" Format="d"/>
        <PropertyColumn Property="x => x.Email" Title="Email"/>
        <PropertyColumn Property="x => x.Phone" Title="Telefon"/>
        <PropertyColumn Property="x => x.IdDocument" Title="LK/Pasoš"/>
        <TemplateColumn Title="Korisnik">
            <CellTemplate>
                @if (context.Item.User is not null)
                {
                    <MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success"/>
                }
                else
                {
                    <MudIcon Icon="@Icons.Material.Filled.Close" Color="Color.Error"/>
                }
            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="PersonDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    private MudDataGrid<PersonDto> _dataGrid = null!;
    private readonly GetPersonsQuery _query = new();

    private async Task<GridData<PersonDto>> ServerReload(GridState<PersonDto> state)
    {
        _query.Page = state.Page + 1;
        _query.PageSize = state.PageSize;

        var sortDefinition = state.SortDefinitions.FirstOrDefault();
        if (sortDefinition != null)
        {
            _query.SortBy = Enum.Parse<PersonSort>(sortDefinition.SortBy);
            _query.SortDirection = sortDefinition.Descending ? Application.Common.Enums.SortDirection.Desc : Application.Common.Enums.SortDirection.Asc;
        }

        var result = await Mediator.Send(_query);

        return result.Match(
            value => new GridData<PersonDto>
            {
                TotalItems = value.TotalCount,
                Items = value.Items
            },
            error =>
            {
                Snackbar.Add($"Error loading persons: {error.Code}: {error.Description}", Severity.Error);
                return new GridData<PersonDto>();
            });
    }

    private Task Filter()
    {
        return _dataGrid.ReloadServerData();
    }

    private async Task SavePersonAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };

        var dialog = await DialogService.ShowAsync<SavePersonDialog>("Kreiraj osobu", options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            await _dataGrid.ReloadServerData();
        }
    }

}