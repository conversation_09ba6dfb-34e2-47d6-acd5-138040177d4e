@using OdmoriBa.Application.Common.Constants
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.StaticData
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Core.Common.ValueObjects
@using OdmoriBa.Presentation.Features.Customers.Mappers
@using OdmoriBa.Presentation.Features.Customers.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudStack Row>
                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.FirstName"
                                  For="@(() => _request.FirstName)"
                                  Immediate="true"
                                  Label="Ime"/>

                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.LastName"
                                  For="@(() => _request.LastName)"
                                  Immediate="true"
                                  Label="Prezime"/>

                </MudStack>

                <MudStack Row>
                    <MudDatePicker Label="Datum rođenja" ShowToolbar="false" For="@(() => _request.BirthDate)"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense"
                                   @bind-Date="_request.BirthDate" Placeholder="Odaberi datum"/>

                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.IdDocument"
                                  For="@(() => _request.IdDocument)"
                                  Immediate="true"
                                  Label="LK/Pasoš"/>
                </MudStack>

                <MudStack Row>
                    <MudStack Row Spacing="0">
                        <MudTextField
                            Disabled="@(Data?.User != null && Data.User.SignInProvider == SignInProviders.Phone)"
                            Style="width: 63px" Variant="Variant.Outlined" Margin="Margin.Dense"
                            @bind-Value="_request.Phone!.CountryCode"
                            InputType="InputType.Telephone"
                            For="@(() => _request.Phone!.CountryCode)"
                            Immediate="true"
                            Label="Poz."/>

                        <MudTextField
                            Disabled="@(Data?.User != null && Data.User.SignInProvider == SignInProviders.Phone)"
                            Variant="Variant.Outlined" Margin="Margin.Dense"
                            @bind-Value="_request.Phone!.Number"
                            For="@(() => _request.Phone!.Number)"
                            InputType="InputType.Telephone"
                            Immediate="true"
                            Label="Broj telefona"/>
                    </MudStack>

                    <MudTextField
                        Disabled="@(Data?.User != null && Data.User.SignInProvider == SignInProviders.Password)"
                        Variant="Variant.Outlined" Margin="Margin.Dense"
                        @bind-Value="_request.Email"
                        For="@(() => _request.Email)"
                        InputType="InputType.Email"
                        Immediate="true"
                        Label="Email"/>
                </MudStack>

                <MudStack Row>
                    <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.City"
                                  For="@(() => _request.City)"
                                  Immediate="true"
                                  Label="Grad"/>

                    <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="string"
                                     Label="Drzava"
                                     For="@(() => _request.CountryCode!)"
                                     @bind-Value="_request.CountryCode"
                                     DebounceInterval="300"
                                     SearchFunc="@SearchCountries"
                                     ResetValueOnEmptyText="true"
                                     ShowProgressIndicator="true"
                                     ToStringFunc="@(c => CountryData.GetNameWithFlag(c))"/>
                </MudStack>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Address"
                              For="@(() => _request.Address)"
                              Immediate="true"
                              Label="Adresa"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; }

    [Parameter] public PersonDto? Data { get; set; }

    private MudForm _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SavePersonRequest _request = new();
    private readonly SavePersonRequestValidator _validator = new();

    private readonly Dictionary<string, Country> _countries = CountryData.Countries;

    protected override void OnInitialized()
    {
        _request.Phone = new Phone("+387", "");
        _request.CountryCode = "BA";
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.FirstName = Data!.FirstName;
            _request.LastName = Data!.LastName;
            _request.BirthDate = Data.BirthDate;
            _request.CountryCode = Data.CountryCode;
            _request.City = Data.City;
            _request.Address = Data.Address;
            _request.Phone = Data.Phone;
            _request.Email = Data.Email;
            _request.IdDocument = Data.IdDocument;
        }
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Person {value.FirstName} {value.LastName} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog.Cancel();

    private Task<IEnumerable<string>> SearchCountries(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _countries.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower())
        ).Select(s => s.Code);
        return Task.FromResult(result);
    }

}