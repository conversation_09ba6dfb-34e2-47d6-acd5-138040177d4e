@using OdmoriBa.Application.Features.Customers.Commands
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Customers.Queries
@using OdmoriBa.Core.Common.Errors
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog?.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudAlert Severity="Severity.Warning" Variant="Variant.Filled" Dense="true">
            Nakon spajanja osoba @Source?.FullName (ID: @Source?.Id) ce biti obrisana i sva putovanja ce biti prebacena
            na drugu osobu
        </MudAlert>

        <br/>

        <MudStack Spacing="3">
            <PersonPreview Person="Source"/>
            <MudStack Row>
                <MudSpacer/>
                <MudIcon Icon="@Icons.Material.Filled.ArrowDownward"></MudIcon>
                <MudSpacer/>
            </MudStack>

            <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="PersonDto"
                             Label="Pretrazi osobu.."
                             @bind-Value="_target"
                             DebounceInterval="300"
                             SearchFunc="@SearchPersons"
                             ResetValueOnEmptyText="true"
                             ShowProgressIndicator="true"
                             Clearable="true"
                             ToStringFunc="@(p => p?.FullName!)">

                <ItemTemplate Context="person">
                    <PersonPreview Person="person"/>
                </ItemTemplate>
            </MudAutocomplete>
            
            @if (_target != null)
            {
                <PersonPreview Person="_target"/>
                <br />
            }


        </MudStack>

    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading || _target == null" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] [EditorRequired] public PersonDto Source { get; set; } = null!;

    private PersonDto? _target;

    private bool _loading;
    private Error? _error;

    private async Task Submit()
    {
        _loading = true;
        var result = await Mediator.Send(new MergePersonsCommand(Source.Id, _target!.Id));

        result.Switch(() =>
        {
            Snackbar.Add("Osoba je spojena", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(_target));
        }, error => _error = error);

        _loading = false;
    }

    private async Task<IEnumerable<PersonDto>> SearchPersons(string searchTerm, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(searchTerm)) return [];
        var result = await Mediator.Send(new GetPersonsQuery { SearchTerm = searchTerm }, cancellationToken);

        return result.Match(
            value => value.Items.Where(s => s.Id != Source.Id),
            error =>
            {
                Snackbar.Add($"Error on search persons: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private void Cancel() => MudDialog!.Cancel();
}