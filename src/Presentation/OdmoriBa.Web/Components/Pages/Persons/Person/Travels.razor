@page "/persons/{PersonId:guid}/travels"
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Queries
@using OdmoriBa.Core.Domains.Travelers.Entities
@using OdmoriBa.Resources.Extensions
@implements IDisposable
@inject PersonState PersonState
@inject ISnackbar Snackbar
@inject IMediator Mediator

<PageTitle>Putovanja</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Osobe", href: "/persons", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(_person?.FullName!, href: $"/persons/{_person?.Id}", icon: Icons.Material.Filled.Person),
                   new BreadcrumbItem("Putovanja", href: $"/persons/{_person?.Id}/travels", icon: Icons.Material.Filled.CardTravel)
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudDataGrid Items="@_travelers" T="PersonTripDto" Dense="true" Striped="true" SortMode="SortMode.None">
    <Columns>
        <TemplateColumn Title="Putovanje">
            <CellTemplate> @context.Item.Trip?.Title </CellTemplate>
        </TemplateColumn>

        <TemplateColumn Title="Datum">
            <CellTemplate> @context.Item.Trip?.StartDate.ToString("d") </CellTemplate>
        </TemplateColumn>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudChip T="string" Variant="Variant.Outlined"
                         Color="@(context.Item?.Status switch
                                {
                                    TravelerStatus.Requested => Color.Warning,
                                    TravelerStatus.Draft => Color.Primary,
                                    TravelerStatus.Cancelled => Color.Error,
                                    TravelerStatus.Confirmed => Color.Success,
                                    _ => Color.Default
                                })">
                    @context.Item?.Status.GetLocalized()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="PersonTripDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    [Parameter] public Guid PersonId { get; set; }

    private PersonDto? _person;
    private List<PersonTripDto> _travelers = [];

    protected override async Task OnParametersSetAsync()
    {
        _person = await PersonState.LoadPersonAsync(PersonId);

        var result = await Mediator.Send(new GetPersonTripsQuery(_person.Id));

        _travelers = result.Match(
            value => value.Items,
            error =>
            {
                Snackbar.Add($"Error loading trips: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    protected override void OnInitialized()
    {
        PersonState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        PersonState.OnChange -= StateHasChanged;
    }

}