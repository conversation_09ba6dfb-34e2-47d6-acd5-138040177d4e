@using OdmoriBa.Application.Common.Constants
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.StaticData
@using OdmoriBa.Core.Domains.Customers.Entities
@using OdmoriBa.Resources.Extensions

@if (Person == null!)
{
    return;
}

<MudPaper Class="pa-2" Elevation="4">
    <MudGrid Spacing="1">
        <MudItem md="7" xs="12">
            <MudText Typo="Typo.h5" GutterBottom="true">@Person.FullName</MudText>

            <div class="d-flex align-items-center mb-1">
                <MudIcon Icon="@Icons.Material.Filled.Phone" Class="mr-2" Color="Color.Primary" Size="Size.Small"/>
                <MudText Typo="Typo.body2">@Person.Phone</MudText>
            </div>

            <div class="d-flex align-items-center mb-1">
                <MudIcon Icon="@Icons.Material.Filled.Email" Class="mr-2" Color="Color.Primary" Size="Size.Small"/>
                <MudText Typo="Typo.body2">@Person.Email</MudText>
            </div>

            <div class="d-flex align-items-center mb-1">
                <MudIcon Icon="@Icons.Material.Filled.Cake" Class="mr-2" Color="Color.Info" Size="Size.Small"/>
                <MudText Typo="Typo.body2">Datum rođenja: @(Person.BirthDate.ToString("dd MMM yy"))</MudText>
            </div>

            <div class="d-flex align-items-center mb-1">
                <MudIcon Icon="@Icons.Material.Filled.LocationOn" Class="mr-2" Color="Color.Info" Size="Size.Small"/>
                <MudText Typo="Typo.body2">
                    @Person.City
                    @if (!string.IsNullOrEmpty(Person.CountryCode))
                    {
                        <span>, @CountryData.GetNameWithFlag(Person?.CountryCode)</span>
                    }
                </MudText>
            </div>

            <div class="d-flex align-items-center mb-1">
                <MudIcon Icon="@Icons.Material.Filled.Badge" Class="mr-2" Color="Color.Default" Size="Size.Small"/>
                <MudText Typo="Typo.body2">LK/Pasoš: @Person.IdDocument</MudText>
            </div>
        </MudItem>

        <MudItem md="5" xs="12" Class="d-flex align-items-center justify-content-center">
            @if (Person.User is null)
            {
                <MudStack AlignItems="AlignItems.Center" Class="pa-2 mud-text-secondary">
                    <MudIcon Icon="@Icons.Material.Filled.PersonOff" Size="Size.Medium"/>
                    <MudText Typo="Typo.subtitle1">Nema korisnički račun</MudText>
                    <MudText Typo="Typo.caption">Ova osoba nije registrovana.</MudText>
                </MudStack>
            }
            else
            {
                <MudStack AlignItems="AlignItems.Center" Spacing="0">
                    <MudImage Src="img/avatar.png" Alt="Profile image" Width="64" Height="64"
                              Class="rounded-circle mb-1"/>

                    <MudChip T="string" Variant="Variant.Outlined" Label="true" Size="Size.Small"
                             Color="@(Person.User.Status switch
                                    {
                                        UserStatus.Active => Color.Success,
                                        UserStatus.Inactive => Color.Error,
                                        _ => Color.Default
                                    })">
                        @Person.User.Status.GetLocalized()
                    </MudChip>

                    <div class="d-flex align-items-center mt-1">
                        <MudIcon Icon="@Icons.Material.Filled.Event" Class="mr-1" Size="Size.Small"/>
                        <MudText Typo="Typo.caption">
                            Registrovan: @(Person.User.CreatedAt.ToString("dd MMM yy"))</MudText>
                    </div>

                    <div class="d-flex align-items-center">
                        <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-1" Size="Size.Small"/>
                        <MudText Typo="Typo.caption">Način prijave:
                            @(Person.User.SignInProvider switch
                            {
                                SignInProviders.Password => "Email",
                                SignInProviders.Phone => "Telefon",
                                SignInProviders.Facebook => "Facebook",
                                SignInProviders.Google => "Google",
                                _ => "-"
                            })
                        </MudText>
                    </div>
                </MudStack>
            }
        </MudItem>
    </MudGrid>
</MudPaper>

@code {
    [Parameter] [EditorRequired] public PersonDto Person { get; set; } = null!;
}