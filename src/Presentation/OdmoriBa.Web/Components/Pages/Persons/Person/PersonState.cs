using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Customers.Queries;
using OdmoriBa.Web.Common.State;

namespace OdmoriBa.Web.Components.Pages.Persons.Person;

public sealed class PersonState(IMediator mediator) : BaseState
{
    public Guid? PersonId { get; private set; }
    public PersonDto? Person { get; private set; }
    public LoyaltyCardDto? LoyaltyCard { get; private set; }

    public async Task<PersonDto> LoadPersonAsync(Guid personId)
    {
        if (PersonId == personId && Person != null) return Person;
        PersonId = personId;
        var result = await mediator.Send(new GetPersonByIdQuery(personId));

        result.Switch(
            value => Person = value,
            SetError
        );

        var loyaltyResult = await mediator.Send(new GetLoyaltyCardQuery(personId));

        loyaltyResult.Switch(
            value => LoyaltyCard = value,
            SetError
        );

        NotifyStateChanged();
        return Person!;
    }

    public async Task<LoyaltyCardDto> LoadLoyaltyCardAsync(Guid personId)
    {
        var loyaltyResult = await mediator.Send(new GetLoyaltyCardQuery(personId));

        loyaltyResult.Switch(
            value => LoyaltyCard = value,
            SetError
        );

        NotifyStateChanged();
        return LoyaltyCard!;
    }

    public void SetPerson(PersonDto person)
    {
        if (PersonId != person.Id) return;
        Person = person;
        NotifyStateChanged();
    }
}