@page "/persons/{PersonId:guid}"
@using OdmoriBa.Application.Common.Constants
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Core.Domains.Customers.Entities
@using OdmoriBa.Resources.Extensions

@implements IDisposable
@inject PersonState PersonState
@inject IDialogService DialogService
@inject NavigationManager Nav

<PageTitle>Putovanje | Detalji</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Osobe", href: "/persons", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(_person?.FullName!, href: $"/persons/{_person?.Id}", icon: Icons.Material.Filled.Person)
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudGrid>
    <MudItem md="4">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Informacije</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack>
                    <div>
                        <MudText Typo="Typo.body2">Ime</MudText>
                        <MudText Typo="Typo.body1">@_person?.FirstName</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Prezime</MudText>
                        <MudText Typo="Typo.body1">@_person?.LastName</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Datum rođenja</MudText>
                        <MudText Typo="Typo.body1">@_person?.BirthDate.ToString("d")</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Grad</MudText>
                        <MudText Typo="Typo.body1">@_person?.City</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Adresa</MudText>
                        <MudText Typo="Typo.body1">@_person?.Address</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">LK/Pasoš</MudText>
                        <MudText Typo="Typo.body1">@_person?.IdDocument</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Email</MudText>
                        <MudText Typo="Typo.body1">@_person?.Email</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Telefon</MudText>
                        <MudText Typo="Typo.body1">@_person?.Phone</MudText>
                    </div>

                </MudStack>
            </MudCardContent>
            <MudCardActions>
                <MudStack Row Spacing="3">
                    <MudButton StartIcon="@Icons.Material.Filled.Merge" OnClick="MergePersonsAsync" Variant="Variant.Filled"
                               Color="Color.Warning">
                        Spoji
                    </MudButton>
                    <MudButton StartIcon="@Icons.Material.Filled.Edit" OnClick="SavePersonAsync" Variant="Variant.Filled"
                               Color="Color.Info">
                        Uredi
                    </MudButton>
                </MudStack>
            </MudCardActions>
        </MudCard>
    </MudItem>
    <MudItem md="4">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Poeni</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack>
                    <div>
                        <MudText Typo="Typo.body2">Total</MudText>
                        <MudText Typo="Typo.body1">@_loyaltyCard?.TotalPoints</MudText>
                    </div>

                    <div>
                        <MudText Typo="Typo.body2">Historija poena</MudText>
                                            
                        @foreach (var item in _loyaltyCard?.PointTransactions ?? [])
                        {
                            <MudText Typo="Typo.body1">@item.Value - @item.Type.GetDescription()</MudText>
                            <MudText Typo="Typo.body1">Putovanje: @item.TripDestination?.Destination?.Name - @item.TripDestination?.StartDate.ToString("d") - @item.TripDestination?.EndDate.ToString("d")</MudText>
                            <MudDivider/>
                        }

                        
                    </div>

                </MudStack>
            </MudCardContent>
            <MudCardActions>
                <MudStack Row Spacing="3">
                <MudButton StartIcon="@Icons.Material.Filled.Add"  Variant="Variant.Filled"
                           Color="Color.Info">
                    Dodaj poene
                </MudButton>
                </MudStack>
            </MudCardActions>
        </MudCard>
    </MudItem>
    <MudItem md="4">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Korisnički podaci</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                @if (_person?.User is null)
                {
                    <MudText Align="Align.Center" Typo="Typo.body1">Korisnik nema kreiran račun</MudText>
                }
                else
                {
                    <MudStack>
                        <div>
                            <MudText Typo="Typo.body2">Email</MudText>
                            <MudText Typo="Typo.body1">@_person?.User?.Email</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Telefon</MudText>
                            <MudText Typo="Typo.body1">@_person?.User?.Phone</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Status</MudText>
                            <MudChip T="string" Variant="Variant.Outlined"
                                     Color="@(_person.User?.Status switch
                                            {
                                                UserStatus.Active => Color.Success,
                                                UserStatus.Inactive => Color.Error,
                                                _ => Color.Default
                                            })">
                                @_person?.User?.Status.GetLocalized()
                            </MudChip>
                        </div>

                        <div>
                            <MudText Typo="Typo.body2">Datum registracije</MudText>
                            <MudText Typo="Typo.body1">@_person?.User?.CreatedAt.ToString("g")</MudText>
                        </div>

                        <div>
                            <MudText Typo="Typo.body2">Način prijave</MudText>
                            <MudText Typo="Typo.body1">@(_person?.User?.SignInProvider switch
                                                       {
                                                           SignInProviders.Password => "Email",
                                                           SignInProviders.Phone => "Telefon",
                                                           SignInProviders.Facebook => "Facebook",
                                                           SignInProviders.Google => "Google",
                                                           _ => '-'
                                                       })</MudText>
                        </div>
                        
                        @if (_person?.User?.EmailVerified == true)
                        {
                            <div>
                                <MudText Typo="Typo.body2">Verifikovan email</MudText>
                                <MudText Typo="Typo.body1">@(_person?.User?.EmailVerified == true ? "Da" : "Ne")</MudText>
                            </div>
                        }
                    </MudStack>
                }
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>

@code {
    [Parameter] public Guid PersonId { get; set; }

    private PersonDto? _person;
    private LoyaltyCardDto? _loyaltyCard;

    protected override void OnInitialized()
    {
        PersonState.OnChange += StateHasChanged;
    }

    protected override async Task OnParametersSetAsync()
    {
        _person = await PersonState.LoadPersonAsync(PersonId);
        _loyaltyCard = await PersonState.LoadLoyaltyCardAsync(PersonId);
    }

    public void Dispose()
    {
        PersonState.OnChange -= StateHasChanged;
    }

    private async Task SavePersonAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SavePersonDialog> { { x => x.Data, _person } };

        var dialog = await DialogService.ShowAsync<SavePersonDialog>("Uredi osobu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            PersonState.SetPerson((result.Data as PersonDto)!);
            _person = (result.Data as PersonDto)!;
        }
    }
    
    private async Task MergePersonsAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<MergePersonsDialog> { { x => x.Source, _person } };

        var dialog = await DialogService.ShowAsync<MergePersonsDialog>("Spoji osobe", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var target = (result.Data as PersonDto)!;
            PersonState.SetPerson(target);
            _person = target;
            Nav.NavigateTo($"/persons/{target.Id}");
        }
    }

}