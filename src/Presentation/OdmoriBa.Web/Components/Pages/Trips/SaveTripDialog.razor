@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="TripType?"
                           Label="Tip"
                           @bind-Value="_request.Type"
                           For="@(() => _request.Type)">
                    @foreach (var item in Enum.GetValues<TripType>())
                    {
                        <MudSelectItem T="TripType?" Value="item">
                            @item.GetDescription()
                        </MudSelectItem>
                    }
                </MudSelect>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Title"
                              For="@(() => _request.Title)"
                              Immediate="true"
                              Label="Naziv"/>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense"
                              @bind-Value="_request.Description"
                              Lines="5"
                              For="@(() => _request.Description)"
                              Immediate="true"
                              Label="Opis"/>

                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="TransportationType?"
                           Label="Tip prevoza"
                           @bind-Value="_request.TransportationType"
                           For="@(() => _request.TransportationType)">
                    <MudSelectItem T="TransportationType?" Value="TransportationType.Bus">
                        Autobus
                    </MudSelectItem>
                    <MudSelectItem T="TransportationType?" Value="TransportationType.Airplane">
                        Avion
                    </MudSelectItem>
                </MudSelect>

                <MudDateRangePicker Variant="Variant.Outlined" Label="Polazak/Povratak"
                                    Margin="Margin.Dense"
                                    DateRange="new DateRange(_request.StartDate, _request.EndDate)"
                                    For="@(() => _request.StartDate)"
                                    DateRangeChanged="@(dr =>
                                                      {
                                                          _request.StartDate = dr.Start;
                                                          _request.EndDate = dr.End;
                                                      })"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public TripDto? Data { get; set; }

    private MudForm _form = null!;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveTripRequest _request = new();

    private readonly SaveTripRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Type = Data!.Type;
            _request.Title = Data.Title;
            _request.Description = Data.Description;
            _request.StartDate = Data.StartDate;
            _request.EndDate = Data.EndDate;
            _request.TransportationType = Data.TransportationType;
        }
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Trip {value.Title} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog.Cancel();
}