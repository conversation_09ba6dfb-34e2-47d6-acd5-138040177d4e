@page "/trips"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Application.Features.Trips.Enums
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Application.Features.Trips.Queries
@using OdmoriBa.Core.Domains.Trips.Entities
@using SortDirection = OdmoriBa.Application.Common.Enums.SortDirection

@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject NavigationManager Nav

<PageTitle>Putovanja</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled"
               Color="Color.Primary">
        Novo putovanje
    </MudButton>
</MudToolBar>

<MudDataGrid @ref="_dataGrid" T="TripListItemDto"
             ServerData="ServerReload" Filterable="false" Striped="true"
             Dense="true" Hover="true"
             SortMode="SortMode.Single">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_query.SearchTerm"
                          T="string" @bind-Value:after="Filter" Placeholder="Search"
                          Adornment="Adornment.Start"
                          Clearable="true"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudDateRangePicker Label="Period"
                                Variant="Variant.Outlined"
                                Margin="Margin.Dense"
                                Clearable="true"
                                DateRangeChanged="@(dr =>
                                                  {
                                                      _query.FromDate = dr.Start.ToDateOnly();
                                                      _query.ToDate = dr.End.ToDateOnly();
                                                      Filter();
                                                  })"/>

            <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                       MultiSelectionTextFunc="@(new Func<List<string>, string>(GetMultiSelectionDestinationText))"
                       Label="Destinacije" MultiSelection="true" Clearable="true"
                       @bind-SelectedValues="_query.DestinationId" @bind-SelectedValues:after="Filter">
                @foreach (var destination in _destinations)
                {
                    <MudSelectItem T="Guid" Value="@destination.Id">@destination.Name</MudSelectItem>
                }
            </MudSelect>

            <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                       T="TripStatus"
                       MultiSelection="true"
                       Label="Status"
                       Clearable="true"
                       MultiSelectionTextFunc="@(list => string.Join(",", list!.Select(s => Enum.Parse<TripStatus>(s!).GetDescription())))"
                       @bind-SelectedValues="_query.Status"
                       @bind-SelectedValues:after="Filter">

                @foreach (var item in Enum.GetValues<TripStatus>())
                {
                    <MudSelectItem T="TripStatus" Value="@item">@item.GetDescription()</MudSelectItem>
                }
            </MudSelect>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.Title" Title="Naziv" Sortable="false">
            <CellTemplate>
                <MudIcon
                    Icon="@(context.Item.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive)"
                    Color="Color.Primary"/>
                @context.Item.Title
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.Type" Title="Tip">
            <CellTemplate>@context.Item.Type.GetDescription()</CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.StartDate" Title="Od" Format="d"/>
        <PropertyColumn Property="x => x.EndDate" Title="Do" Format="d"/>
        <PropertyColumn Property="x => x.Status">
            <CellTemplate>
                <MudChip T="string" Variant="Variant.Outlined"
                         Color="@(context.Item.Status.GetColor())">
                    @context.Item.Status.GetDescription()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.Destinations" Title="Destinacije" Sortable="false">
            <CellTemplate>
                @foreach (var destination in context.Item.Destinations ?? [])
                {
                    <MudChip T="string" Color="Color.Primary" Size="Size.Small">@destination</MudChip>
                }
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.NumberOfTravelers" Title="Broj putnika" Sortable="false"/>
        <PropertyColumn Property="x => x.NumberOfTravelerRequests" Title="Novih prijava" Sortable="false">
            <CellTemplate>
                <MudChip T="string"
                         Color="@(context.Item.NumberOfTravelerRequests > 0 ? Color.Warning : Color.Default)">
                    @context.Item.NumberOfTravelerRequests
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <TemplateColumn>
            <CellTemplate>
                <MudTooltip Text="Postavke">
                    <MudIconButton Href="@($"trips/{context.Item!.Id}")" Icon="@Icons.Material.Filled.Settings">
                        Detalji
                    </MudIconButton>
                </MudTooltip>
                <MudTooltip Text="Putnici">
                    <MudIconButton Href="@($"trips/{context.Item!.Id}/travelers")"
                                   Icon="@Icons.Material.Filled.People">
                        Putnici
                    </MudIconButton>
                </MudTooltip>

                <MudTooltip Text="Autobusi">
                    <MudIconButton Href="@($"trips/{context.Item!.Id}/buses")"
                                   Icon="@Icons.Material.Filled.DirectionsBus">
                        Autobusi
                    </MudIconButton>
                </MudTooltip>
            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="TripListItemDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    private MudDataGrid<TripListItemDto> _dataGrid = null!;
    readonly GetTripListQuery _query = new()
    {
        Status = [TripStatus.Draft, TripStatus.Published]
    };
    private IEnumerable<DestinationDto> _destinations = [];

    protected override async Task OnInitializedAsync()
    {
        var destinationsResult = await Mediator.Send(new GetDestinationsQuery());

        _destinations = destinationsResult.Match(
            value => value.Items,
            error =>
            {
                Snackbar.Add($"Error loading destinations: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task<GridData<TripListItemDto>> ServerReload(GridState<TripListItemDto> state)
    {
        _query.Page = state.Page + 1;
        _query.PageSize = state.PageSize;

        var sortDefinition = state.SortDefinitions.FirstOrDefault();
        if (sortDefinition != null)
        {
            _query.SortBy = Enum.Parse<TripSort>(sortDefinition.SortBy);
            _query.SortDirection = sortDefinition.Descending ? SortDirection.Desc : SortDirection.Asc;
        }

        var result = await Mediator.Send(_query);

        return result.Match(
            value => new GridData<TripListItemDto>
            {
                TotalItems = value.TotalCount,
                Items = value.Items
            },
            error =>
            {
                Snackbar.Add($"Error loading trips: {error.Code}: {error.Description}", Severity.Error);
                return new GridData<TripListItemDto>();
            });
    }

    private void Filter()
    {
        _dataGrid.ReloadServerData();
    }


    private async Task SaveAsync(TripDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveTripDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveTripDialog>($"{(data is null ? "Kreiraj" : "Uredi")} putovanje", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            Nav.NavigateTo($"/trips/{(result.Data as TripDto)!.Id}");
            await _dataGrid.ReloadServerData();
        }
    }

    private string GetMultiSelectionDestinationText(List<string> destinationIds)
    {
        var destinationNames = _destinations.Where(s => destinationIds.Contains(s.Id.ToString()))
            .Select(s => s.Name);

        return string.Join(", ", destinationNames);
    }

}