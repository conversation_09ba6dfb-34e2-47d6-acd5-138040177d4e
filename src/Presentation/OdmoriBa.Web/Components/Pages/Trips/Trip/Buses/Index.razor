@page "/trips/{TripId:guid}/buses"
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities

@implements IDisposable

@inject IDialogService DialogService
@inject TripState TripState
@inject IMediator Mediator
@inject ISnackbar Snackbar

<PageTitle>Putovanje | Putnici</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel),
                   new BreadcrumbItem($"{_trip?.Title} ({_trip?.DateRange})", href: $"/trips/{_trip?.Id!}", icon: _trip?.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive),
                   new BreadcrumbItem("Autobusi", href: $"/trips/{_trip?.Id!}/buses", icon: Icons.Material.Filled.DirectionsBus),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>

    <MudButton StartIcon="@Icons.Material.Filled.EventSeat"
               Href="@($"/trips/{TripId}/seat-assignment")"
               Variant="Variant.Outlined"
               Color="Color.Secondary"
               Class="mr-2">
        Raspored sjedišta
    </MudButton>

    <MudButton StartIcon="@Icons.Material.Filled.Add"
               OnClick="@(_ => SaveTripBusAsync())"
               Variant="Variant.Filled"
               Color="Color.Primary">
        Dodaj autobus
    </MudButton>

</MudToolBar>

<MudGrid>
    <MudItem md="6">
        <MudDataGrid Items="@_trip?.TripBuses.Where(s => s.Direction == TripBusDirection.Departure)" T="TripBusDto"
                     Striped="true"
                     Dense="true">
            <ToolBarContent>
                <MudText Typo="Typo.h6">Polazak</MudText>
            </ToolBarContent>
            <Columns>
                <PropertyColumn Property="x => x.Name" Title="Naziv"/>
                <PropertyColumn Property="x => x.Bus!.FullName" Title="Bus"/>
                <PropertyColumn Property="x => x.Bus!.Company!.Name" Title="Firma"/>
                <PropertyColumn Property="x => x.Bus!.Capacity" Title="Kapacitet"/>
                <TemplateColumn>
                    <CellTemplate>
                        <MudIconButton OnClick="@(_ => SaveTripBusAsync(context.Item))"
                                       Icon="@Icons.Material.Filled.Edit"
                                       Color="Color.Primary"
                                       aria-label="Edit"/>

                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" aria-label="Obrisi"
                                       OnClick="() => DeleteTripBusAsync(context.Item!)"/>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    </MudItem>
    <MudItem md="6">
        <MudDataGrid Items="@_trip?.TripBuses.Where(s => s.Direction == TripBusDirection.Return)" T="TripBusDto"
                     Striped="true"
                     Dense="true">
            <ToolBarContent>
                <MudText Typo="Typo.h6">Povratak</MudText>
            </ToolBarContent>
            <Columns>
                <PropertyColumn Property="x => x.Name" Title="Naziv"/>
                <PropertyColumn Property="x => x.Bus!.FullName" Title="Bus"/>
                <PropertyColumn Property="x => x.Bus!.Company!.Name" Title="Firma"/>
                <PropertyColumn Property="x => x.Bus!.Capacity" Title="Kapacitet"/>
                <TemplateColumn>
                    <CellTemplate>
                        <MudIconButton OnClick="@(_ => SaveTripBusAsync(context.Item))"
                                       Icon="@Icons.Material.Filled.Edit"
                                       Color="Color.Primary"
                                       aria-label="Edit"/>

                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" aria-label="Obrisi"
                                       OnClick="() => DeleteTripBusAsync(context.Item!)"/>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    </MudItem>
</MudGrid>


@code {
    [Parameter] public Guid TripId { get; set; }
    
    private TripDto? _trip = new();
    private List<TravelPartyDto> _travelParties = [];

    protected override async Task OnParametersSetAsync()
    {
        _trip = await TripState.LoadTripAsync(TripId);
        _travelParties = await TripState.LoadTravelPartiesAsync(TripId);
        
    }

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

    private async Task SaveTripBusAsync(TripBusDto? data = null)
    {
        var isEdit = data is not null;

        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveTripBusDialog>
        {
            { x => x.Data, data },
            { x => x.Trip, _trip },
            { x => x.IsEdit, isEdit }
        };

        var dialog = await DialogService.ShowAsync<SaveTripBusDialog>($"{(!isEdit ? "Dodaj" : "Uredi")} autobus", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var tripBus = (result.Data as TripBusDto)!;
            if (!isEdit)
            {
                _trip!.TripBuses.Add(tripBus);
            }
            else
            {
                _trip!.TripBuses[_trip.TripStops.FindIndex(s => s.Id == data!.Id)] = tripBus;
            }
        }
    }
    
    private async Task DeleteTripBusAsync(TripBusDto tripBus)
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati autobus iz ovog putovanja!",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeleteTripBusCommand(TripId, tripBus.Id));

            result.Switch(() =>
            {
                _trip?.TripBuses.Remove(tripBus);
                Snackbar.Add($"Autobus {tripBus.Name} je obrisan iz putovanja", Severity.Success);
            }, error => { Snackbar.Add($"Error on delete trip stop: {error.Code}: {error.Description}", Severity.Error); });
        }
    }

}