@using OdmoriBa.Application.Features.Transportations.Models
@using OdmoriBa.Application.Features.Transportations.Queries
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Autobus"
                                 For="@(() => _request.BusId)"
                                 @bind-Value="_request.BusId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchBuses"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _buses.GetValueOrDefault(c)?.FullName!)"/>
                
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Naziv"/>
                
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="TripBusDirection"
                           Label="Smjer"
                           @bind-Value="_request.Direction"
                           For="@(() => _request.Direction)">
                    <MudSelectItem T="TripBusDirection" Value="TripBusDirection.Departure">
                        @TripBusDirection.Departure.GetLocalized()
                    </MudSelectItem>
                    <MudSelectItem T="TripBusDirection" Value="TripBusDirection.Return">
                        @TripBusDirection.Return.GetLocalized()
                    </MudSelectItem>
                </MudSelect>

            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public TripBusDto? Data { get; set; }
    [Parameter] public TripDto? Trip { get; set; }
    [Parameter] public bool IsEdit { get; set; }

    private MudForm _form = null!;
    private bool _loading;
    private Core.Common.Errors.Error? _error;

    private Dictionary<Guid, BusDto> _buses = new();

    private readonly SaveTripBusRequest _request = new();
    private readonly SaveTripBusRequestValidator _validator = new();

    protected override async Task OnInitializedAsync()
    {
        if (Data is not null)
        {
            _request.Name = Data!.Name;
            _request.BusId = Data!.BusId;
            _request.Direction = Data.Direction;
        }

        var stopsResult = await Mediator.Send(new GetBusesQuery());

        _buses = stopsResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading buses: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, BusDto>();
            });
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            !IsEdit ? await Mediator.Send(_request.ToCommand(Trip!.Id)) : await Mediator.Send(_request.ToCommand(Trip!.Id, Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add("Autobus je spremljen", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private Task<IEnumerable<Guid>> SearchBuses(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _buses.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name.ToLower().Contains(searchTerm.ToLower()) || s.Company!.Name.ToLower().Contains(searchTerm.ToLower()
            )).Select(s => s.Id);
        return Task.FromResult(result);
    }

    private void Cancel() => MudDialog.Cancel();
}