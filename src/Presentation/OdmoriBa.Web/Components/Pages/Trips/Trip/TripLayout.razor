@inherits LayoutComponentBase
@layout Layout.MainLayout
@implements IDisposable
@inject TripState TripState

@Body

<MudDrawer Style="top: 0" Class="mt-10 pt-4" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always"
           Variant="@DrawerVariant.Mini">
    <MudNavMenu>

        <MudNavLink Href="@($"trips/{TripState.TripId}")" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Settings">
            Detalji
        </MudNavLink>
        
        <MudNavLink Href="@($"trips/{TripState.TripId}/travelers")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.People">
            Putnici
        </MudNavLink>
        
        <MudNavLink Href="@($"trips/{TripState.TripId}/buses")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.DirectionsBus">
            Autobusi
        </MudNavLink>

    </MudNavMenu>
    <MudSpacer/>
    <MudStack Row>
        <MudSpacer/>
        <MudToggleIconButton
            ToggledChanged="@(_ => _drawerOpen = !_drawerOpen)"
            Toggled="_drawerOpen"
            Size="Size.Small"
            Icon="@Icons.Material.Filled.ArrowForward"
            ToggledIcon="@Icons.Material.Filled.ArrowBack"
            Color="Color.Inherit" Edge="Edge.Start"/>
    </MudStack>
</MudDrawer>

@code {
    private bool _drawerOpen;

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

}