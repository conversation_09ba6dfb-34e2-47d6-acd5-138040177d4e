@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Destinacija"
                                 For="@(() => _request.DestinationId)"
                                 @bind-Value="_request.DestinationId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchDestinations"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _destinations.GetValueOrDefault(c)?.Name!)"/>

                <MudDateRangePicker Label="Polazak/Povratak"
                                    Variant="Variant.Outlined"
                                    Margin="Margin.Dense"
                                    DateRange="new DateRange(_request.StartDate, _request.EndDate)"
                                    For="@(() => _request.StartDate)"
                                    DateRangeChanged="@(dr =>
                                                      {
                                                          _request.StartDate = dr.Start;
                                                          _request.EndDate = dr.End;
                                                      })"/>

                <MudStack Row>
                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.Price" Label="Cijena" Variant="Variant.Outlined"/>

                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.InsurancePrice" Label="PZO" Variant="Variant.Outlined"/>

                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.TaxPrice" Label="Taksa" Variant="Variant.Outlined"/>
                </MudStack>

                <MudDivider/>

                <MudStack Row>
                    <MudNumericField Margin="Margin.Dense"
                                     @bind-Value="_request.Loyalty.Points"
                                     Label="Poeni" Variant="Variant.Outlined"/>

                    <MudCheckBox T="bool" @bind-Value="_request.Loyalty.CanSpendPoints" Label="Može iskoristit poene"/>

                    <MudNumericField Margin="Margin.Dense"
                                     Disabled="_request.Loyalty.CanSpendPoints == false"
                                     @bind-Value="_request.Loyalty.MaximumPointsToSpend"
                                     Label="Maksimalan broj poena za iskoristi" Variant="Variant.Outlined"/>
                </MudStack>

                <MudCheckBox T="bool" @bind-Value="_request.Featured" Label="Izdvojeno putovanje"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public TripDestinationDto? Data { get; set; }
    [Parameter] public bool IsEdit { get; set; }

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private Dictionary<Guid, DestinationDto> _destinations = new();

    private readonly SaveTripDestinationRequest _request = new();
    private readonly SaveTripDestinationRequestValidator _validator = new();

    protected override async Task OnInitializedAsync()
    {
        if (Data is not null)
        {
            _request.DestinationId = Data!.DestinationId;
            _request.StartDate = Data.StartDate;
            _request.EndDate = Data.EndDate;
            _request.Price = Data.Price;
            _request.InsurancePrice = Data.InsurancePrice;
            _request.TaxPrice = Data.TaxPrice;
            _request.Featured = Data.Featured;
            _request.Loyalty = Data.Loyalty;
        }

        var destinationsResult = await Mediator.Send(new GetDestinationsQuery());

        _destinations = destinationsResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading destinations: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, DestinationDto>();
            });
    }

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            !IsEdit ? await Mediator.Send(_request.ToCreateCommand(Data!.TripId)) : await Mediator.Send(_request.ToUpdateCommand(Data!.TripId, Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Trip destination {value.Destination?.Name} is {(IsEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private Task<IEnumerable<Guid>> SearchDestinations(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _destinations.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.Name!.ToLower().Contains(searchTerm.ToLower()
            )).Select(s => s.Id);
        return Task.FromResult(result);
    }

    private void Cancel() => MudDialog.Cancel();
}