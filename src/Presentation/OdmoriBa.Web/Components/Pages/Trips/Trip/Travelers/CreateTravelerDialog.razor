@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Customers.Queries
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@using OdmoriBa.Web.Components.Pages.Persons
@using OdmoriBa.Web.Components.Pages.Persons.Person
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        Dodaj putnika u grupu: @TravelParty?.MainContact?.FullNameAndCity
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_requestValidator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">

                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Pretrazi osobu.."
                                 For="@(() => _request.PersonId)"
                                 @bind-Value="_request.PersonId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchPersons"
                                 SelectOnActivation="false"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 Clearable="true"
                                 ToStringFunc="@(c => _persons.GetValueOrDefault(c)?.FullName!)">

                    <ItemTemplate Context="e">
                        <PersonPreview Person="@_persons.GetValueOrDefault(e)"/>
                    </ItemTemplate>

                    <NoItemsTemplate>
                        <MudButton Style="z-index: 1" OnClick="@(_ => SavePersonAsync())" Variant="Variant.Filled"
                                   Color="Color.Primary">
                            <MudIcon Icon="@Icons.Material.Filled.Add"/>
                            Dodaj novu osobu
                        </MudButton>
                    </NoItemsTemplate>
                </MudAutocomplete>

                @if (_request.PersonId != Guid.Empty)
                {
                    <PersonPreview Person="@_persons.GetValueOrDefault(_request.PersonId)"/>
                }

                <MudStack Row>
                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.Discount" Label="Popust" Variant="Variant.Outlined"/>
                </MudStack>
                <MudStack Row>
                    <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                               T="Guid?"
                               Label="Mjesto ulaska"
                               @bind-Value="_request.DeparturePointId">
                        @foreach (var item in Trip?.TripStops ?? [])
                        {
                            <MudSelectItem T="Guid?" Value="item.Id">
                                @item.Stop?.City?.Name - @item.Stop?.Address
                            </MudSelectItem>
                        }
                    </MudSelect>

                    <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                               T="Guid?"
                               Label="Mjesto povratka"
                               @bind-Value="_request.ReturnDeparturePointId">
                        @foreach (var item in Trip?.TripStops ?? [])
                        {
                            <MudSelectItem T="Guid?" Value="item.Id">
                                @item.Stop?.City?.Name - @item.Stop?.Address
                            </MudSelectItem>
                        }
                    </MudSelect>
                </MudStack>
            </MudStack>

            <MudTextField Lines="3" Margin="Margin.Dense" @bind-Value="_request.Note" For="() => _request.Note"
                          Label="Napomena"
                          Variant="Variant.Outlined"/>

        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public TripDto? Trip { get; set; }
    [Parameter] public TravelPartyDto? TravelParty { get; set; }

    private MudForm _form = null!;
    private bool _loading;
    private Error? _error;

    private Dictionary<Guid, PersonDto> _persons = new();

    private readonly CreateTravelerRequest _request = new();

    private readonly CreateTravelerRequestValidator _requestValidator = new();

    private async Task Submit()
    {
        await _form.Validate();

        await _requestValidator.ValidateAsync(_request);
        if (!_form.IsValid) return;

        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(TravelParty!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Putnik {value.Person?.FullName} je dodan", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private async Task<IEnumerable<Guid>> SearchPersons(string searchTerm, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(searchTerm)) return [];
        var result = await Mediator.Send(new GetPersonsQuery { SearchTerm = searchTerm }, cancellationToken);

        _persons = result.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error on search persons: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, PersonDto>();
            });

        return _persons.Keys;
    }

    private async Task SavePersonAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };

        var dialog = await DialogService.ShowAsync<SavePersonDialog>("Kreiraj osobu", options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var person = result.Data as PersonDto;
            _request.PersonId = person!.Id;
            _persons.TryAdd(person.Id, person);
        }
    }

    private void Cancel() => MudDialog.Cancel();
}