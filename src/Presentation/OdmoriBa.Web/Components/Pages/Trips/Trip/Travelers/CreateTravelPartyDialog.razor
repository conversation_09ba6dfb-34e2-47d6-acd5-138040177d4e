@using OdmoriBa.Application.Features.Customers.Models
@using OdmoriBa.Application.Features.Customers.Queries
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@using OdmoriBa.Web.Components.Pages.Persons
@using OdmoriBa.Web.Components.Pages.Persons.Person
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog?.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudGrid>
            <MudItem md="6">
                <MudStack Spacing="3">
                    <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="PersonDto?"
                                     Label="Pretrazi osobu.."
                                     @bind-Value="_selectedPerson"
                                     @bind-Value:after="@(() =>
                                                        {
                                                            _travelerRequest.PersonId = _selectedPerson?.Id ?? Guid.Empty;
                                                            return Task.CompletedTask;
                                                        })"
                                     DebounceInterval="300"
                                     SearchFunc="@SearchPersons"
                                     ResetValueOnEmptyText="true"
                                     ShowProgressIndicator="true"
                                     Clearable="true"
                                     ToStringFunc="@(c => c?.FullName!)">

                        <ItemTemplate Context="e">
                            <PersonPreview Person="e"/>
                        </ItemTemplate>

                        <NoItemsTemplate>
                            <MudButton Style="z-index: 1" OnClick="@(_ => SavePersonAsync())" Variant="Variant.Filled"
                                       Color="Color.Primary">
                                <MudIcon Icon="@Icons.Material.Filled.Add"/>
                                Dodaj novu osobu
                            </MudButton>
                        </NoItemsTemplate>
                    </MudAutocomplete>

                    @if (_travelerRequest.PersonId != Guid.Empty)
                    {
                        <PersonPreview Person="_selectedPerson"/>

                        <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                                   T="Guid?"
                                   Label="Mjesto ulaska"
                                   @bind-Value="_travelerRequest.DeparturePointId">
                            @foreach (var item in Trip.TripStops ?? [])
                            {
                                <MudSelectItem T="Guid?" Value="item.Id">
                                    @item.Stop?.City?.Name - @item.Stop?.Address
                                </MudSelectItem>
                            }
                        </MudSelect>


                        <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                         @bind-Value="_travelerRequest.Discount" Label="Popust"
                                         Variant="Variant.Outlined"/>

                        <MudTextField Variant="Variant.Outlined" @bind-Value="_travelerRequest.Note" Label="Napomena"
                                      Lines="3"/>
                    }

                    <MudButton Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add"
                               Disabled="_travelerRequest.PersonId == Guid.Empty"
                               Variant="Variant.Filled"
                               OnClick="@(AddTraveler)">
                        Dodaj u grupu
                    </MudButton>
                </MudStack>
            </MudItem>

            <MudItem md="6">
                <MudDataGrid T="PersonDto" Dense="true" Items="@_selectedPersons"
                             SortMode="SortMode.None"
                             ShowColumnOptions="false"
                             Bordered="true">
                    <Columns>
                        <PropertyColumn Property="p => p.FullName" Title="Ime i prezime"/>
                        <PropertyColumn Property="p => p.Phone" Title="Telefon"/>
                        <PropertyColumn Property="p => p.BirthDate" Title="Datum R" Format="d"/>
                        <PropertyColumn Property="p => p.City" Title="Grad"/>

                        <TemplateColumn Title="Primarni kontakt">
                            <CellTemplate>
                                <MudCheckBox T="bool" Value="_request.MainContactId == context.Item.Id"
                                             ValueChanged="@(v => { _request.MainContactId = v ? context.Item.Id : Guid.Empty; })"/>
                            </CellTemplate>
                        </TemplateColumn>

                        <TemplateColumn>
                            <CellTemplate>
                                <MudIconButton Icon="@Icons.Material.Filled.Cancel"
                                               OnClick="@(() =>
                                                        {
                                                            _request.Travelers!.Remove(_request.Travelers.First(s => s.PersonId == context.Item!.Id));
                                                            _selectedPersons.Remove(context.Item!);
                                                        })"></MudIconButton>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                </MudDataGrid>

                <MudTextField Variant="Variant.Outlined" @bind-Value="_request.Note" Label="Napomena" Lines="3"/>

            </MudItem>

        </MudGrid>


    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading || _selectedPersons.Count == 0"
                   Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] [EditorRequired] public TripDto Trip { get; set; } = null!;
    [Parameter] [EditorRequired] public TripDestinationDto TripDestination { get; set; } = null!;

    private bool _loading;
    private Error? _error;

    private PersonDto? _selectedPerson;
    private readonly List<PersonDto> _selectedPersons = [];
    private TravelPartyTravelerCreateDto _travelerRequest = new();

    private readonly CreateTravelPartyRequest _request = new()
    {
        Travelers = []
    };

    private readonly CreateTravelPartyRequestValidator _requestValidator = new();

    protected override void OnInitialized()
    {
        _request.TripId = Trip.Id;
        _request.TripDestinationId = TripDestination.Id;
    }

    private void AddTraveler()
    {
        _selectedPersons.Add(_selectedPerson!);

        if (_request.Travelers!.Count == 0) _request.MainContactId = _travelerRequest.PersonId;

        _request.Travelers!.Add(_travelerRequest);
        _travelerRequest = new TravelPartyTravelerCreateDto();
        _selectedPerson = null;
    }

    private async Task Submit()
    {
        var validationResult = await _requestValidator.ValidateAsync(_request);

        if (!validationResult.IsValid)
        {
            _error = Error.Validation("InvalidRequest", "Podaci nisu validni");
            return;
        }


        _loading = true;
        var result = await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Putnici kreirani", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private async Task<IEnumerable<PersonDto>> SearchPersons(string searchTerm, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(searchTerm)) return [];
        var result = await Mediator.Send(new GetPersonsQuery { SearchTerm = searchTerm }, cancellationToken);

        return result.Match(
            value => value.Items,
            error =>
            {
                Snackbar.Add($"Error on search persons: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private async Task SavePersonAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };

        var dialog = await DialogService.ShowAsync<SavePersonDialog>("Kreiraj osobu", options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            _travelerRequest.PersonId = (result.Data as PersonDto)!.Id;
            _selectedPerson = result.Data as PersonDto;
        }
    }

    private void Cancel() => MudDialog!.Cancel();

}