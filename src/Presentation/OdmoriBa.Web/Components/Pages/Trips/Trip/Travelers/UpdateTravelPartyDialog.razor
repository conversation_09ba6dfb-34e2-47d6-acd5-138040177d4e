@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        Uredi grupu - @Data?.MainContact?.FullNameAndCity
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudGrid>
            <MudItem md="12">
                <MudDataGrid T="TravelerDto" Dense="true" Items="@Data?.Travelers"
                             SortMode="SortMode.None"
                             ShowColumnOptions="false"
                             Bordered="true">
                    <Columns>
                        <PropertyColumn Property="p => p.Person!.FullName" Title="Ime i prezime"/>
                        <PropertyColumn Property="p => p.Person!.Phone" Title="Telefon"/>
                        <PropertyColumn Property="p => p.Person!.BirthDate" Title="Datum R" Format="d"/>
                        <PropertyColumn Property="p => p.Person!.City" Title="Grad"/>
                        
                        <TemplateColumn Title="Lider">
                            <CellTemplate>
                                <MudCheckBox T="bool" Value="_request.MainContactId == context.Item.PersonId"
                                             ValueChanged="@(v => { _request.MainContactId = v ? context.Item.PersonId : Guid.Empty; })"/>
                            </CellTemplate>
                        </TemplateColumn>
                        
                    </Columns>
                </MudDataGrid>

                <MudTextField Variant="Variant.Outlined" @bind-Value="_request.Note" Label="Napomena" Lines="3"/>

            </MudItem>

        </MudGrid>


    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public TravelPartyDto? Data { get; set; }
    
    private bool _loading;
    private Error? _error;
    
    private readonly UpdateTravelPartyRequest _request = new();

    private readonly UpdateTravelPartyRequestValidator _requestValidator = new();

    protected override void OnInitialized()
    {
        if (Data is not null)
        {
            _request.MainContactId = Data.MainContactId;
            _request.Note = Data.Note;
        }
    }
    
    private async Task Submit()
    {
        var validationResult = await _requestValidator.ValidateAsync(_request);

        if (!validationResult.IsValid)
        {
            _error = Error.Validation("InvalidRequest", "Podaci nisu validni");
            return;
        }
        
        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(Data!.Id));
        
        result.Switch(value =>
        {
            Snackbar.Add("Grupa spremljena", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }
    
    private void Cancel() => MudDialog!.Cancel();

}