@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Travelers.Queries
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog?.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_requestValidator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">

                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Grupa"
                                 For="@(() => _request.TravelPartyId)"
                                 @bind-Value="_request.TravelPartyId"
                                 DebounceInterval="300"
                                 SelectOnActivation="false"
                                 SearchFunc="@SearchTravelParties"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _travelParties.GetValueOrDefault(c)?.MainContact?.FullNameAndCity!)"/>

                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="Guid?"
                           Label="Mjesto ulaska"
                           @bind-Value="_request.DeparturePointId">
                    @foreach (var item in Trip?.TripStops ?? [])
                    {
                        <MudSelectItem T="Guid?" Value="item.Id">
                            @item.Stop?.City?.Name - @item.Stop?.Address
                        </MudSelectItem>
                    }
                </MudSelect>

                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="Guid?"
                           Label="Mjesto povratka"
                           @bind-Value="_request.ReturnDeparturePointId">
                    @foreach (var item in Trip?.TripStops ?? [])
                    {
                        <MudSelectItem T="Guid?" Value="item.Id">
                            @item.Stop?.City?.Name - @item.Stop?.Address
                        </MudSelectItem>
                    }
                </MudSelect>

                <MudStack Row>
                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.Price" Label="Cijena" Variant="Variant.Outlined"/>

                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.Discount" Label="Popust" Variant="Variant.Outlined"/>

                </MudStack>
                <MudStack Row>
                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.InsurancePrice" Label="PZO" Variant="Variant.Outlined"/>

                    <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                     @bind-Value="_request.TaxPrice" Label="Taksa" Variant="Variant.Outlined"/>
                </MudStack>

                <MudTextField Lines="3" Margin="Margin.Dense" @bind-Value="_request.Note" For="() => _request.Note"
                              Label="Napomena"
                              Variant="Variant.Outlined"/>

            </MudStack>

        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" Color="Color.Error"
                   Disabled="_loading" OnClick="Delete">Obrisi putnika
        </MudButton>
        <MudSpacer/>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public TripDto? Trip { get; set; }
    [Parameter] public TravelerDto? Data { get; set; }

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private readonly UpdateTravelerRequest _request = new();

    private readonly UpdateTravelerRequestValidator _requestValidator = new();

    private Dictionary<Guid, TravelPartyDto> _travelParties = new();

    protected override async Task OnInitializedAsync()
    {
        if (Data is not null)
        {
            _request.TravelPartyId = Data.TravelPartyId;
            _request.Price = Data.Price;
            _request.InsurancePrice = Data.InsurancePrice;
            _request.TaxPrice = Data.TaxPrice;
            _request.Discount = Data.Discount;
            _request.DeparturePointId = Data.DeparturePointId;
            _request.ReturnDeparturePointId = Data.ReturnDeparturePointId;
            _request.Note = Data.Note;
        }

        var countriesResult = await Mediator.Send(new GetTravelPartiesQuery(Trip!.Id));

        _travelParties = countriesResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading travel parties: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, TravelPartyDto>();
            });
    }

    private async Task Submit()
    {
        await _form!.Validate();

        await _requestValidator.ValidateAsync(_request);
        if (!_form.IsValid) return;

        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(Data!.TravelPartyId, Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Putnik {value.Person?.FullName} je spremljen", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private async Task Delete()
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati putnika?",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeleteTravelerCommand(Data!.TravelPartyId, Data.Id));

            result.Switch(() =>
            {
                Snackbar.Add("Putnik obrisan", Severity.Success);
                MudDialog!.Close(DialogResult.Ok(Data!.Id));
            }, error => _error = error);
        }
    }

    private Task<IEnumerable<Guid>> SearchTravelParties(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _travelParties.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.MainContact!.FullName.ToLower().Contains(searchTerm.ToLower())
        ).Select(s => s.Id);
        return Task.FromResult(result);
    }

}