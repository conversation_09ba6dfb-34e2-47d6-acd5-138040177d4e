@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Core.Domains.Travelers.Entities
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog.Title - @TravelParty.MainContact?.FullName
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">

                <MudNumericField Margin="Margin.Dense" AdornmentText="KM" Adornment="Adornment.Start"
                                 @bind-Value="_request.Amount" Label="Uplata" Variant="Variant.Outlined"/>
                
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="Guid?"
                           Label="Uplatio"
                           @bind-Value="_request.PaidByPersonId"
                           For="@(() => _request.PaidByPersonId)">
                    
                    @foreach (var traveler in TravelParty.Travelers ?? [])
                    {
                        <MudSelectItem T="Guid?" Value="@traveler.PersonId">
                            @traveler.Person?.FullName
                        </MudSelectItem>
                    }
                </MudSelect>
                
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="PaymentType?"
                           Label="Tip uplate"
                           @bind-Value="_request.Type"
                           For="@(() => _request.Type)">
                    <MudSelectItem T="PaymentType?" Value="PaymentType.Cash">
                        @PaymentType.Cash.GetDescription()
                    </MudSelectItem>
                    <MudSelectItem T="PaymentType?" Value="PaymentType.Bank">
                        @PaymentType.Bank.GetDescription()
                    </MudSelectItem>
                </MudSelect>

                <MudDatePicker Label="Datum" ShowToolbar="false"
                               For="@(() => _request.PaidAt)"
                               Margin="Margin.Dense"
                               Variant="Variant.Outlined"
                               @bind-Date="_request.PaidAt" Placeholder="Odaberi datum"/>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense"
                              @bind-Value="_request.Note"
                              Lines="3"
                              For="@(() => _request.Note)"
                              Immediate="true"
                              Label="Napomena"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        @if (IsEdit)
        {
            <MudButton StartIcon="@Icons.Material.Filled.Delete" Variant="Variant.Filled" Color="Color.Error" Disabled="_loading" OnClick="Delete">Obrisi uplatu</MudButton>
        }
        <MudSpacer/>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public PaymentDto? Data { get; set; }
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] [EditorRequired] public TravelPartyDto TravelParty { get; set; } = null!;

    private MudForm _form = null!;
    private bool _loading;
    private Error? _error;
    
    private readonly SavePaymentRequest _request = new()
    {
        PaidAt = DateTime.UtcNow
    };
    private readonly SavePaymentRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        if (Data is not null)
        {
            _request.Amount = Data!.Amount;
            _request.PaidAt = Data.PaidAt;
            _request.Type = Data.Type;
            _request.Note = Data.Note;
            _request.PaidByPersonId = Data.PaidByPersonId;
        }
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            !IsEdit ? await Mediator.Send(_request.ToCreateCommand(TravelParty!.Id)) : await Mediator.Send(_request.ToUpdateCommand(TravelParty!.Id, Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add("Uplata spremljena", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog.Cancel();


    private async Task Delete()
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati uplatu?",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeletePaymentCommand(TravelParty!.Id, Data!.Id));

            result.Switch(() =>
            {
                Snackbar.Add("Payment deleted", Severity.Success);
                MudDialog.Close(DialogResult.Ok(Data!.Id));
            }, error => _error = error);
        }
    }
}