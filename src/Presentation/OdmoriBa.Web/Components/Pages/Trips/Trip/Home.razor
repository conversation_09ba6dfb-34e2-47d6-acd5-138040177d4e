@page "/trips/{TripId:guid}"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Core.Domains.Trips.Extensions

@implements IDisposable

@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject TripState TripState
@inject NavigationManager Nav

@if (_trip == null!) return;

<PageTitle>Putovanje | Detalji</PageTitle>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel),
                   new BreadcrumbItem($"{_trip.Title} ({_trip.DateRange})", href: $"/trips/{_trip.Id}", icon: _trip.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudGrid>
    <MudItem md="12" sm="12" xs="12" >
        
        <MudCard>
            <MudCardContent>
                <MudStack AlignItems="AlignItems.Center" Wrap="Wrap.Wrap">
                    <MudMenu>
                        <ActivatorContent>
                            <MudChip T="string" Variant="Variant.Outlined" 
                                     Size="Size.Large"
                                     Icon="@_trip.Status.GetIcon()"
                                     Color="_trip.Status.GetColor()">
                                @_trip.Status.GetDescription()
                            </MudChip>
                        </ActivatorContent>
                        <ChildContent>
                            <MudMenuItem Label="U pripremi"
                                         OnClick="() => UpdateStatus(TripStatus.Draft)"
                                         Icon="@TripStatus.Draft.GetIcon()"
                                         IconColor="TripStatus.Draft.GetColor()"
                                         Disabled="!_trip.Status.CanTransitionTo(TripStatus.Draft)"/>
                            <MudMenuItem Label="Objavi"
                                         OnClick="() => UpdateStatus(TripStatus.Published)"
                                         Icon="@TripStatus.Published.GetIcon()"
                                         IconColor="TripStatus.Published.GetColor()"
                                         Disabled="!_trip.Status.CanTransitionTo(TripStatus.Published)"/>
                            <MudDivider />
                            <MudMenuItem Label="Završi"
                                         OnClick="() => UpdateStatus(TripStatus.Completed)"
                                         Icon="@TripStatus.Completed.GetIcon()"
                                         IconColor="TripStatus.Completed.GetColor()"
                                         Disabled="!_trip.Status.CanTransitionTo(TripStatus.Completed) || _trip.EndDate > DateTime.Now"/>
                            <MudDivider />
                            <MudMenuItem Label="Otkaži"
                                         OnClick="() => UpdateStatus(TripStatus.Cancelled)"
                                         Icon="@TripStatus.Cancelled.GetIcon()"
                                         IconColor="TripStatus.Cancelled.GetColor()"
                                         Disabled="!_trip.Status.CanTransitionTo(TripStatus.Cancelled) || (_trip.StartDate < DateTime.Now && _trip.EndDate < DateTime.Now)"/>
                        </ChildContent>
                    </MudMenu>
                    
                    <MudStack Row Wrap="Wrap.Wrap">
                        <div>
                            <MudText Typo="Typo.body2">Tip</MudText>
                            <MudText Typo="Typo.body1">@_trip.Type?.GetDescription()</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Naziv</MudText>
                            <MudText Typo="Typo.body1">@_trip.Title</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Polazak</MudText>
                            <MudText Typo="Typo.body1">@(_trip.StartDate.ToString("d"))</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Povratak</MudText>
                            <MudText Typo="Typo.body1">@(_trip.EndDate.ToString("d"))</MudText>
                        </div>
                        <div>
                            <MudText Typo="Typo.body2">Prevoz</MudText>
                            <MudText
                                Typo="Typo.body1">
                                @(_trip.TransportationType == TransportationType.Bus ? "Autobus" : "Avion")
                            </MudText>
                        </div>
                        <MudButton Disabled="_trip.Status == TripStatus.Completed" StartIcon="@Icons.Material.Filled.Edit"
                                   OnClick="SaveTripAsync"
                                   Variant="Variant.Filled" Color="Color.Info">
                            Uredi
                        </MudButton>

                        <MudButton StartIcon="@Icons.Material.Filled.ContentCopy" OnClick="CloneTripAsync"
                                   Variant="Variant.Filled" Color="Color.Info">
                            Kloniraj
                        </MudButton>
                    </MudStack>
                </MudStack>
               
            </MudCardContent>
        </MudCard>
    </MudItem>
    <MudItem md="12" sm="12" xs="12">
        <MudStack>
            <MudTable Items="_trip.TripDestinations" Hover="true" Dense="true" Striped="true">
                <ToolBarContent>
                    <MudText Typo="Typo.h6">Destinacije</MudText>
                    <MudSpacer/>
                    <MudButton StartIcon="@Icons.Material.Filled.Add"
                               Disabled="@(_trip.Type == TripType.OneDestination && _trip.TripDestinations.Count > 0)"
                               OnClick="@(_ => SaveTripDestinationAsync())"
                               Variant="Variant.Filled"
                               Color="Color.Primary">
                        Dodaj
                    </MudButton>
                </ToolBarContent>
                <HeaderContent>
                    <MudTh>Izdvojen</MudTh>
                    <MudTh>Destinacija</MudTh>
                    <MudTh>Polazak</MudTh>
                    <MudTh>Povratak</MudTh>
                    <MudTh>Cijena</MudTh>
                    <MudTh>PZO</MudTh>
                    <MudTh>Taksa</MudTh>
                    <MudTh>Poeni</MudTh>
                    <MudTh>Korištenje poena</MudTh>
                    <MudTh>Max broj poena</MudTh>
                    <MudTd></MudTd>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Izdvojen">
                        <MudIcon
                            Icon="@(context.Featured ? Icons.Material.Filled.Check : Icons.Material.Filled.Cancel)"></MudIcon>
                    </MudTd>
                    <MudTd DataLabel="Destinacija">@context.Destination?.Name</MudTd>
                    <MudTd DataLabel="Polazak">@context.StartDate.ToString("d")</MudTd>
                    <MudTd DataLabel="Povratak">@context.EndDate.ToString("d")</MudTd>
                    <MudTd DataLabel="Cijena">@context.Price</MudTd>
                    <MudTd DataLabel="PZO">@context.InsurancePrice</MudTd>
                    <MudTd DataLabel="Taksa">@context.TaxPrice</MudTd>
                    <MudTd DataLabel="Poeni">@context.Loyalty.Points</MudTd>
                    <MudTd DataLabel="Korištenje poena">
                        <MudIcon
                            Icon="@(context.Loyalty.CanSpendPoints ? Icons.Material.Filled.Check : Icons.Material.Filled.Cancel)"></MudIcon>
                    </MudTd>
                    <MudTd DataLabel="Max broj poena">@context.Loyalty.MaximumPointsToSpend</MudTd>
                    <MudTd DataLabel="Akcije">
                        <MudIconButton OnClick="@(_ => SaveTripDestinationAsync(context))"
                                       Icon="@Icons.Material.Filled.Edit"
                                       Color="Color.Primary"
                                       aria-label="Edit"/>
                        <MudTooltip Text="Plan i program">
                            <MudToggleIconButton ToggledChanged="@(x =>
                                                                 {
                                                                     if (x) _expandedItems.Add(context.Id);
                                                                     else _expandedItems.Remove(context.Id);
                                                                 })"
                                                 Icon="@Icons.Material.Outlined.ViewList"
                                                 ToggledIcon="@Icons.Material.Filled.ViewList"/>
                        </MudTooltip>
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" aria-label="Obrisi"
                                       OnClick="@(() => DeleteTripDestinationAsync(context))"/>
                    </MudTd>
                </RowTemplate>
                <ChildRowContent>
                    @if (_expandedItems.Contains(context.Id))
                    {
                        <MudTr>
                            <td colspan="7">
                                <MudTable Items="@context.Itinerary" Hover="true" Dense="true" Striped="true">
                                    <ToolBarContent>
                                        <MudText Typo="Typo.body1">Plan i program</MudText>
                                        <MudSpacer/>
                                        <MudButton OnClick="@(_ => SaveItineraryAsync(context))"
                                                   Color="Color.Primary">
                                            <MudIcon Icon="@Icons.Material.Filled.Add"/>
                                            Dodaj
                                        </MudButton>
                                    </ToolBarContent>
                                    <HeaderContent>
                                        <MudTh>Datum i vrijeme</MudTh>
                                        <MudTh>Naslov</MudTh>
                                        <MudTh>Opis</MudTh>
                                        <MudTd></MudTd>
                                    </HeaderContent>
                                    <RowTemplate Context="itineray">
                                        <MudTd DataLabel="Datum i vrijeme">@itineray.DateTime?.ToString("g")</MudTd>
                                        <MudTd DataLabel="Naslov">@itineray.Title</MudTd>
                                        <MudTd DataLabel="Opis">@itineray.Description</MudTd>
                                        <MudTd DataLabel="Akcije">
                                            <MudIconButton OnClick="@(_ => SaveItineraryAsync(context, itineray))"
                                                           Icon="@Icons.Material.Filled.Edit"
                                                           Color="Color.Primary"
                                                           aria-label="Edit"/>

                                            <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error"
                                                           aria-label="Obrisi"
                                                           OnClick="@(() => DeleteItineraryAsync(context.Id, itineray))"/>
                                        </MudTd>
                                    </RowTemplate>
                                    <NoRecordsContent>
                                        <MudText>Nije definisan plan i program</MudText>
                                    </NoRecordsContent>
                                </MudTable>
                            </td>
                        </MudTr>
                    }
                </ChildRowContent>

                <NoRecordsContent>
                    <MudText>Nema destinacija</MudText>
                </NoRecordsContent>
            </MudTable>

            <MudDataGrid Items="@_trip.TripStops" T="TripStopDto"
                         Striped="true"
                         Dense="true">
                <ToolBarContent>
                    <MudText Typo="Typo.h6">Stanice</MudText>
                    <MudSpacer/>
                    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveTripStopAsync())"
                               Variant="Variant.Filled"
                               Color="Color.Primary">
                        Dodaj
                    </MudButton>
                </ToolBarContent>
                <Columns>
                    <PropertyColumn Property="x => x.Stop!.City!.Name" Title="Grad"/>
                    <PropertyColumn Property="x => x.Stop!.Address" Title="Adresa"/>
                    <PropertyColumn Property="x => x.BeginAt" Title="Od" Format="g"/>
                    <PropertyColumn Property="x => x.EndAt" Title="Do" Format="g"/>
                    <PropertyColumn Property="x => x.Type" Title="Tip">
                        <CellTemplate>@context.Item.Type!.GetDescription()</CellTemplate>
                    </PropertyColumn>
                    <TemplateColumn>
                        <CellTemplate>
                            <MudIconButton OnClick="@(_ => SaveTripStopAsync(context.Item))"
                                           Icon="@Icons.Material.Filled.Edit"
                                           Color="Color.Primary"
                                           aria-label="Edit"/>

                            <MudIconButton Icon="@Icons.Material.Filled.Delete" Color="Color.Error" aria-label="Obrisi"
                                           OnClick="@(() => DeleteTripStopAsync(context.Item!))"/>
                        </CellTemplate>
                    </TemplateColumn>
                </Columns>
            </MudDataGrid>

        </MudStack>
    </MudItem>
</MudGrid>

@code {

    [Parameter] public Guid TripId { get; set; }

    private bool _loading;

    private TripDto _trip = null!;

    protected override async Task OnParametersSetAsync()
    {
        _trip = await TripState.LoadTripAsync(TripId);
    }

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

    private readonly HashSet<Guid> _expandedItems = [];

    private async Task SaveTripDestinationAsync(TripDestinationDto? data = null)
    {
        var isEdit = data is not null;
        data ??= new TripDestinationDto
        {
            TripId = _trip.Id,
            StartDate = _trip.StartDate,
            EndDate = _trip.EndDate,
            Loyalty = new TripDestinationLoyalty()
        };

        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveTripDestinationDialog>
        {
            { x => x.Data, data },
            { x => x.IsEdit, isEdit }
        };

        var dialog = await DialogService.ShowAsync<SaveTripDestinationDialog>($"{(!isEdit ? "Kreiraj" : "Uredi")} destinaciju", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (!isEdit)
            {
                _trip.TripDestinations.Add((result.Data as TripDestinationDto)!);
            }
            else
            {
                _trip.TripDestinations[_trip.TripDestinations.FindIndex(s => s.Id == data.Id)] = (result.Data as TripDestinationDto)!;
            }
        }
    }

    public async Task UpdateStatus(TripStatus newStatus)
    {
        MarkupString message = newStatus switch
        {
            TripStatus.Draft => (MarkupString)@"
                                              Jeste li sigurni da želite vratit status <b>u obradu</b>? 
                                              <br /> Nakon ovoga putovanje više neće biti vidljivo u aplikaciji i neće biti moguće prijave
                                              ",
            TripStatus.Published => (MarkupString)@"
                                                  Jeste li sigurni da želite <b>objaviti</b> putovanje? 
                                                  <br /> Nakon ovoga putovanje će biti vidljivo u aplikaciji i biće omogućena prijava na putovanje
                                                  ",
            TripStatus.Cancelled => (MarkupString)@"
                                                  Jeste li sigurni da želite <b>otkazati</b> ovo putovanje? 
                                                  <br /> Nakon ovoga svim putnicima će biti poslano obavještenje da je putovanje otkazano i više se neće moći uređivati ovo putovanje
                                                  ",
            TripStatus.Completed => (MarkupString)@"
                                                  Jeste li sigurni da želite <b>završiti</b> putovanje? 
                                                  <br /> Svim putnicima će biti dodjeljenji poeni.
                                                  <br /> Putovanje više neće moći biti uređivano.
                                                  ",
            _ => throw new ArgumentOutOfRangeException(nameof(newStatus), newStatus, null)
        };

        bool? dialogResult = await DialogService.ShowMessageBox(
            "Potvrda",
            message,
            yesText: "Da, nastavi", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
             _loading = true;
             StateHasChanged();
            var result = await Mediator.Send(new UpdateTripStatusCommand(TripId, newStatus));
            
            result.Switch(() =>
            {
                _trip.Status = newStatus;
                Snackbar.Add($"Status je promijenjen u: {newStatus.GetDescription()}", Severity.Success);
            }, error => { Snackbar.Add($"Error on update status: {error.Code}: {error.Description}", Severity.Error); });

            _loading = false;
        }
    }

    private async Task SaveItineraryAsync(TripDestinationDto tripDestination, ItineraryDto? data = null)
    {
        var isEdit = data is not null;
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveItineraryDialog>
        {
            { x => x.Data, data },
            { x => x.TripDestination, tripDestination },
            { x => x.IsEdit, isEdit }
        };

        var dialog = await DialogService.ShowAsync<SaveItineraryDialog>($"{(!isEdit ? "Dodaj" : "Uredi")} plan i program", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (!isEdit)
            {
                tripDestination.Itinerary ??= [];
                tripDestination.Itinerary.Add((result.Data as ItineraryDto)!);
            }
            else
            {
                tripDestination.Itinerary![tripDestination.Itinerary.FindIndex(s => s.Id == data!.Id)] = (result.Data as ItineraryDto)!;
            }
        }
    }

    private async Task SaveTripStopAsync(TripStopDto? data = null)
    {
        var isEdit = data is not null;

        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveTripStopDialog>
        {
            { x => x.Data, data },
            { x => x.Trip, _trip },
            { x => x.IsEdit, isEdit }
        };

        var dialog = await DialogService.ShowAsync<SaveTripStopDialog>($"{(!isEdit ? "Dodaj" : "Uredi")} stanicu", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (!isEdit)
            {
                _trip.TripStops ??= [];
                _trip.TripStops.Add((result.Data as TripStopDto)!);
            }
            else
            {
                _trip.TripStops[_trip.TripStops.FindIndex(s => s.Id == data!.Id)] = (result.Data as TripStopDto)!;
            }
        }
    }


    private async Task DeleteTripDestinationAsync(TripDestinationDto tripDestination)
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati destinaciju iz ovog putovanja!",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeleteTripDestinationCommand(TripId, tripDestination.Id));

            result.Switch(() =>
            {
                _trip.TripDestinations.Remove(tripDestination);
                Snackbar.Add($"Trip destination {tripDestination.Destination?.Name} is deleted", Severity.Success);
            }, error => { Snackbar.Add($"Error on delete trip destination: {error.Code}: {error.Description}", Severity.Error); });
        }
    }

    private async Task DeleteItineraryAsync(Guid tripDestinationId, ItineraryDto itinerary)
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati stavku plana i programa!",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeleteItineraryCommand(tripDestinationId, itinerary.Id));

            result.Switch(() =>
            {
                _trip.TripDestinations.FirstOrDefault(s => s.Id == itinerary.TripDestinationId)?.Itinerary?.Remove(itinerary);
                Snackbar.Add($"Plan i program stavka: {itinerary.Title} je obrisana", Severity.Success);
            }, error => { Snackbar.Add($"Error on delete itineraru: {error.Code}: {error.Description}", Severity.Error); });
        }
    }

    private async Task DeleteTripStopAsync(TripStopDto tripStop)
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Upozorenje",
            "Jeste li sigurni da zelite obrisati stanicu iz ovog putovanja!",
            yesText: "Da, obriši!", cancelText: "Ne, odustani");

        if (dialogResult == true)
        {
            var result = await Mediator.Send(new DeleteTripStopCommand(TripId, tripStop.Id));

            result.Switch(() =>
            {
                _trip.TripStops.Remove(tripStop);
                Snackbar.Add($"Trip stop {tripStop.Stop?.Address} is deleted", Severity.Success);
            }, error => { Snackbar.Add($"Error on delete trip stop: {error.Code}: {error.Description}", Severity.Error); });
        }
    }

    private async Task SaveTripAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveTripDialog> { { x => x.Data, _trip } };

        var dialog = await DialogService.ShowAsync<SaveTripDialog>("Uredi putovanje", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            TripState.SetTrip((result.Data as TripDto)!);
            _trip = (result.Data as TripDto)!;
        }
    }

    private async Task CloneTripAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<CloneTripDialog> { { x => x.Data, _trip } };

        var dialog = await DialogService.ShowAsync<CloneTripDialog>("Kloniraj putovanje", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var target = (result.Data as TripDto)!;
            TripState.SetTrip(target);
            _trip = target;
            Nav.NavigateTo($"/trips/{target.Id}");
        }
    }

}