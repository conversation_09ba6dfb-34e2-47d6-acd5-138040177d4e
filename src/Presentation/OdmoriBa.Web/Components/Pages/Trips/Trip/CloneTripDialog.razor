@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudAlert Severity="Severity.Warning" Icon="@Icons.Material.Filled.Warning" Variant="Variant.Filled" Dense="true">
            Nakon kloniranja, provjerite da li su datumi putovanja i datumi stanica uredu!
        </MudAlert>
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">

                <MudDateRangePicker Variant="Variant.Outlined" Label="Novi datumi za polazak i povratak"
                                    Margin="Margin.Dense"
                                    DateRange="new DateRange(_request.StartDate, _request.EndDate)"
                                    For="@(() => _request.StartDate)"
                                    DateRangeChanged="@(dr =>
                                                      {
                                                          _request.StartDate = dr.Start;
                                                          _request.EndDate = dr.End;
                                                      })"/>

            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Spremi
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public TripDto? Data { get; set; }

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private readonly CloneTripRequest _request = new();

    private readonly CloneTripRequestValidator _validator = new();

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Putovanje {Data!.Title} je klonirano!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();
}