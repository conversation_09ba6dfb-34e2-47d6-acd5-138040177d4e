@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudAutocomplete Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true" T="Guid"
                                 Label="Stanica"
                                 For="@(() => _request.StopId)"
                                 @bind-Value="_request.StopId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchStops"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _stops.GetValueOrDefault(c)?.FullName!)"/>

                <MudStack Row>
                    <MudDatePicker Label="Od" ShowToolbar="false"
                                   For="@(() => _request.BeginAt)"
                                   @bind-Date="_request.BeginAt"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Placeholder="Odaberi datum"/>
                    <MudTimePicker Label="Vrijeme" 
                                   Time="_request.BeginAt?.TimeOfDay" 
                                   TimeChanged="@(t => _request.BeginAt = _request.BeginAt.SetTime(t))" />
                </MudStack>
                <MudStack Row>
                    <MudDatePicker Label="Do" ShowToolbar="false"
                                   For="@(() => _request.EndAt)"
                                   @bind-Date="_request.EndAt"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Placeholder="Odaberi datum"/>
                    <MudTimePicker Label="Vrijeme" 
                                   Time="_request.EndAt?.TimeOfDay" 
                                   TimeChanged="@(t => _request.EndAt = _request.EndAt.SetTime(t))" />
                </MudStack>
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Dense="true"
                           T="TripStopType?"
                           Label="Tip stanice"
                           @bind-Value="_request.Type"
                           For="@(() => _request.Type)">
                    <MudSelectItem T="TripStopType?" Value="TripStopType.Departure">
                        Polazak
                    </MudSelectItem>
                    <MudSelectItem T="TripStopType?" Value="TripStopType.Break">
                        Pauza
                    </MudSelectItem>
                    <MudSelectItem T="TripStopType?" Value="TripStopType.Return">
                        Povratak
                    </MudSelectItem>
                </MudSelect>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense"
                              @bind-Value="_request.Description"
                              Lines="5"
                              For="@(() => _request.Description)"
                              Immediate="true"
                              Label="Opis"/>

            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public TripStopDto? Data { get; set; }
    [Parameter] public TripDto? Trip { get; set; }
    [Parameter] public bool IsEdit { get; set; }

    private MudForm _form = null!;
    private bool _loading;
    private Error? _error;

    private Dictionary<Guid, StopDto> _stops = new();

    private readonly SaveTripStopRequest _request = new();
    private readonly SaveTripStopRequestValidator _validator = new();

    protected override async Task OnInitializedAsync()
    {
        if (Trip is not null)
        {
            _request.BeginAt = Trip.StartDate;
            _request.EndAt = Trip.StartDate;
        }

        if (Data is not null)
        {
            _request.StopId = Data!.StopId;
            _request.BeginAt = Data.BeginAt!.Value.DateTime;
            _request.EndAt = Data.EndAt!.Value.DateTime;
            _request.Type = Data.Type;
            _request.Description = Data.Description;
        }

        var stopsResult = await Mediator.Send(new GetStopsQuery());

        _stops = stopsResult.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                Snackbar.Add($"Error loading stops: {error.Code}: {error.Description}", Severity.Error);
                return new Dictionary<Guid, StopDto>();
            });
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            !IsEdit ? await Mediator.Send(_request.ToCreateCommand(Trip!.Id)) : await Mediator.Send(_request.ToUpdateCommand(Trip!.Id, Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Trip Stop is {(IsEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private Task<IEnumerable<Guid>> SearchStops(string searchTerm, CancellationToken cancellationToken)
    {
        var result = _stops.Values.Where(s =>
            string.IsNullOrWhiteSpace(searchTerm)
            || s.City!.Name.ToLower().Contains(searchTerm.ToLower()) || s.Address!.ToLower().Contains(searchTerm.ToLower()
            )).Select(s => s.Id);
        return Task.FromResult(result);
    }

    private void Cancel() => MudDialog.Cancel();
}