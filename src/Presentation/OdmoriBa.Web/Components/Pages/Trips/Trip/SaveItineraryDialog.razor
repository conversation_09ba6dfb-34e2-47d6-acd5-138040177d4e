@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Common.Errors
@using OdmoriBa.Presentation.Features.Trips.Mappers
@using OdmoriBa.Presentation.Features.Trips.Models
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudDialog>
    <TitleContent>
        @MudDialog.Title - @TripDestination?.Destination?.Name
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">

                <MudStack Row>
                    <MudDatePicker Label="Datum" ShowToolbar="false"
                                   For="@(() => _request.DateTime)"
                                   @bind-Date="_request.DateTime"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Placeholder="Odaberi datum"/>
                    <MudTimePicker Label="Vrijeme"
                                   Time="_request.DateTime?.TimeOfDay"
                                   TimeChanged="@(t => _request.DateTime = _request.DateTime.SetTime(t))"/>
                </MudStack>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense"
                              @bind-Value="_request.Title"
                              For="@(() => _request.Title)"
                              Immediate="true"
                              Label="Naslov"/>

                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense"
                              @bind-Value="_request.Description"
                              Lines="3"
                              For="@(() => _request.Description)"
                              Immediate="true"
                              Label="Opis"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Otkazi</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Spremi</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance MudDialog { get; set; }

    [Parameter] public ItineraryDto? Data { get; set; }
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public TripDestinationDto? TripDestination { get; set; }

    private MudForm _form;
    private bool _loading;
    private Error? _error;

    private SaveItineraryRequest _request = new();
    private readonly SaveItineraryRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _request = new SaveItineraryRequest
        {
            TripDestinationId = TripDestination!.Id,
            DateTime = TripDestination?.StartDate
        };

        if (Data is not null)
        {
            _request.DateTime = Data!.DateTime?.DateTime;
            _request.Title = Data.Title;
            _request.Description = Data.Description;
        }
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            !IsEdit ? await Mediator.Send(_request.ToCommand()) : await Mediator.Send(_request.ToCommand(Data!.Id));

        result.Switch(value =>
        {
            Snackbar.Add("Itinerary saved", Severity.Success);
            MudDialog.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog.Cancel();
}