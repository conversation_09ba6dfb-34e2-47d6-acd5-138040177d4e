using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Travelers.Queries;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Application.Features.Trips.Queries;
using OdmoriBa.Web.Common.State;

namespace OdmoriBa.Web.Components.Pages.Trips.Trip;

public sealed class TripState(IMediator mediator) : BaseState
{
    public Guid? TripId { get; set; }
    public TripDto? Trip { get; set; }
    public List<TravelPartyDto>? TravelerParties { get; set; }

    public async Task<TripDto> LoadTripAsync(Guid tripId)
    {
        if (TripId == tripId && Trip != null) return Trip;
        TripId = tripId;
        var result = await mediator.Send(new GetTripByIdQuery(tripId));

        result.Switch(
            value => Trip = value,
            SetError
        );

        NotifyStateChanged();
        return Trip!;
    }
    
    public async Task<List<TravelPartyDto>> LoadTravelPartiesAsync(Guid tripId)
    {
        var result = await mediator.Send(new GetTravelPartiesQuery(tripId));

        result.Switch(
            value => TravelerParties = value.Items,
            SetError
        );

        NotifyStateChanged();
        return TravelerParties!;
    }
    
    public void SetTrip(TripDto trip)
    {
        if (TripId != trip.Id) return;
        Trip = trip;
        NotifyStateChanged();
    }
}