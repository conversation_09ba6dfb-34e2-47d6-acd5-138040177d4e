@page "/destinations/{DestinationId:guid}"
@using OdmoriBa.Application.Features.Destinations.Commands
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Files.Commands
@using OdmoriBa.Core.Domains.Files.Entities

@implements IDisposable

@inject DestinationState DestinationState
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject IMediator Mediator

<PageTitle>Destinacija | Detalji</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Destinacije", href: "/destinations", icon: Icons.Material.Filled.ModeOfTravel),
                   new BreadcrumbItem(_destination?.Name!, href: $"/destinations/{_destination?.Id}", icon: Icons.Material.Filled.ModeOfTravel),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}

<MudGrid>
    <MudItem md="3">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Detalji</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack>
                    <div>
                        <MudText Typo="Typo.body2">Naziv</MudText>
                        <MudText Typo="Typo.body1">@_destination?.Name</MudText>
                    </div>
                </MudStack>
            </MudCardContent>
            <MudCardActions>
                <MudButton StartIcon="@Icons.Material.Filled.Edit" OnClick="() => SaveAsync()" Variant="Variant.Filled" Color="Color.Info">
                    Uredi
                </MudButton>
            </MudCardActions>
        </MudCard>
    </MudItem>
    <MudItem md="3">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Cover</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            @if (!string.IsNullOrEmpty(_destination?.CoverUrl))
            {
                <MudCardMedia Image="@_destination?.CoverUrl" Title="@_destination?.Name"/>
            }
            <MudCardContent>
                @if (string.IsNullOrEmpty(_destination?.CoverUrl))
                {
                    <MudText Align="Align.Center">Nema slike</MudText>
                }

                <MudFileUpload T="IBrowserFile" @ref="_coverUpload" Accept=".png, .jpg, .jpeg" FilesChanged="UploadFile">
                </MudFileUpload>
            </MudCardContent>
            <MudCardActions>
                <MudButton Color="Color.Primary" Class="mt-2" OnClick="@(async () => await _coverUpload.OpenFilePickerAsync())">
                    <MudIcon Icon="@Icons.Material.Filled.Upload"></MudIcon>
                    Odaberi sliku
                </MudButton>
            </MudCardActions>
        </MudCard>
    </MudItem>
</MudGrid>


@code {
    [Parameter] public Guid DestinationId { get; set; }

    private DestinationDto? _destination;
    
    private MudFileUpload<IBrowserFile> _coverUpload = null!;
    
    private bool _loading;

    protected override async Task OnParametersSetAsync()
    {
        _destination = await DestinationState.LoadDestinationAsync(DestinationId);
    }

    protected override void OnInitialized()
    {
        DestinationState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        DestinationState.OnChange -= StateHasChanged;
    }
    
    private async Task SaveAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveDestinationDialog> { { x => x.Data, DestinationState.Destination } };

        var dialog = await DialogService.ShowAsync<SaveDestinationDialog>("Uredi destinaciju", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            DestinationState.SetDestination((result.Data as DestinationDto)!);
        }
    }

    private async Task UploadFile(IBrowserFile? file)
    {
        if (file == null) return;

        _loading = true;
        
        var command = new CreateFileRecordCommand(
            FileRecordType.DestinationCover,
            file.Name,
            $"destinations/{DestinationId}/cover/{file.Name}",
            file.OpenReadStream(),
            file.ContentType,
            file.Size
        );

        var result = await Mediator.Send(command);
        
        await result.SwitchAsync(async fileRecord =>
        {
            var updateCommand = new UpdateDestinationCommand(_destination!.Id, _destination.Name!, fileRecord.Id);
            var updateResult = await Mediator.Send(updateCommand);
            
            updateResult.Switch(destination =>
            {
                _destination.CoverUrl = destination.CoverUrl;
                Snackbar.Add("Cover image uploaded", Severity.Success);
            }, error =>
            {
                Snackbar.Add($"Update destination {_destination.Name} failed: {error.Description}", Severity.Error);
            });
            
        }, error =>
        {
            Snackbar.Add($"Upload image {file.Name} failed: {error.Description}", Severity.Error);
            return Task.CompletedTask;
        });
        
        _loading = false;
    }

}