using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Application.Features.Destinations.Queries;
using OdmoriBa.Web.Common.State;

namespace OdmoriBa.Web.Components.Pages.Destinations.Destination;

public sealed class DestinationState(IMediator mediator) : BaseState
{
    public Guid? DestinationId { get; set; }
    public DestinationDto? Destination { get; set; }
    
    public async Task<DestinationDto> LoadDestinationAsync(Guid destinationId)
    {
        if (DestinationId == destinationId && Destination != null) return Destination;
        DestinationId = destinationId;
        var result = await mediator.Send(new GetDestinationByIdQuery(destinationId));

        result.Switch(
            value => Destination = value,
            SetError
        );

        NotifyStateChanged();
        return Destination!;
    }
    
    public void SetDestination(DestinationDto destination)
    {
        if (DestinationId != destination.Id) return;
        Destination = destination;
        NotifyStateChanged();
    }
}