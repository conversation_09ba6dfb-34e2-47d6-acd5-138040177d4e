@inherits LayoutComponentBase
@layout Layout.MainLayout
@implements IDisposable
@inject DestinationState DestinationState

@Body

<MudDrawer Style="top: 0" Class="mt-10 pt-4" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always"
           Variant="@DrawerVariant.Mini">
    <MudNavMenu>
        <MudNavLink Href="@($"destinations/{DestinationState.DestinationId}")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Home">Detalji
        </MudNavLink>
        <MudNavLink Href="@($"destinations/{DestinationState.DestinationId}/gallery")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Photo">Galerija
        </MudNavLink>
    </MudNavMenu>
    <MudSpacer/>
    <MudStack Row>
        <MudSpacer/>
        <MudToggleIconButton
            ToggledChanged="@(_ => _drawerOpen = !_drawerOpen)"
            Toggled="_drawerOpen"
            Size="Size.Small"
            Icon="@Icons.Material.Filled.ArrowForward"
            ToggledIcon="@Icons.Material.Filled.ArrowBack"
            Color="Color.Inherit" Edge="Edge.Start"/>
    </MudStack>
</MudDrawer>

@code {
    private bool _drawerOpen;

    protected override void OnInitialized()
    {
        DestinationState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        DestinationState.OnChange -= StateHasChanged;
    }

}