@page "/destinations"
@using OdmoriBa.Application.Features.Destinations.Models
@using OdmoriBa.Application.Features.Destinations.Queries
@using OdmoriBa.Application.StaticData
@inject IMediator Mediator
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager

<PageTitle>Destinacije</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Destinacije", href: "/destinations", icon: Icons.Material.Filled.ModeOfTravel),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(_ => SaveAsync())" Variant="Variant.Filled"
               Color="Color.Primary">
        Dodaj
    </MudButton>
</MudToolBar>

<MudDataGrid Items="@_elements" T="DestinationDto"
             Striped="true"
             RowClick="@(row => NavigationManager.NavigateTo($"destinations/{row.Item.Id}"))"
             QuickFilter="QuickFilter">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1" Variant="Variant.Outlined" Margin="Margin.Dense" @bind-Value="_searchTerm"
                          T="string" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.Name" Title="Naziv"/>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="DestinationDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    private List<DestinationDto> _elements = [];
    private string? _searchTerm;
    private Country? _country;

    protected override async Task OnInitializedAsync()
    {
        var result = await Mediator.Send(new GetDestinationsQuery());

        _elements = result.Match(
            value => value.Items,
            error =>
            {
                Snackbar.Add($"Error loading destinations: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    private Func<DestinationDto, bool> QuickFilter => x =>
        (string.IsNullOrEmpty(_searchTerm) ||
         x.Name!.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase));

    private async Task SaveAsync(DestinationDto? data = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveDestinationDialog> { { x => x.Data, data } };

        var dialog = await DialogService.ShowAsync<SaveDestinationDialog>($"{(data is null ? "Kreiraj" : "Uredi")} destinaciju", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            if (data is null)
            {
                _elements.Add((result.Data as DestinationDto)!);
            }
            else
            {
                _elements[_elements.FindIndex(s => s.Id == data.Id)] = (result.Data as DestinationDto)!;
            }
        }
    }

}