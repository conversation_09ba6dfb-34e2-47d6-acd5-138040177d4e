@page "/"
@using OdmoriBa.Application.Common.Extensions
@using OdmoriBa.Application.Features.Trips.Queries
@using OdmoriBa.Core.Domains.Trips.Entities

@inject IMediator Mediator
@inject ISnackbar Snackbar

<PageTitle>Odmori.Ba</PageTitle>

<MudCalendar DateRangeChanged="LoadTrips" ShowDay="false" ShowWeek="false" Items="_events">
    <CellTemplate>
        <MudPaper
            Class="@(((CustomCalendarItem)context).Status == TripStatus.Draft ? "mud-theme-info" : "mud-theme-success")"
            Style="text-align: center">
            @context.Text
            <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.ArrowRight"
                           Href="@($"/trips/{((CustomCalendarItem)context).TripId}")"></MudIconButton>
        </MudPaper>
    </CellTemplate>
</MudCalendar>

@code {

    private List<CustomCalendarItem> _events = [];

    private async Task LoadTrips(DateRange arg)
    {
        var result = await Mediator.Send(new GetTripCalendarQuery((DateOnly)arg.Start.ToDateOnly()!, (DateOnly)arg.End?.ToDateOnly()!));

        _events = result.Match(value => value.Items.Select(s =>
            new CustomCalendarItem
            {
                Start = (DateTime)s.StartDate?.ToDateTime(TimeOnly.MinValue)!,
                End = (DateTime)s.EndDate?.ToDateTime(TimeOnly.MinValue)!,
                Text = s.Title!,
                Status = s.Status!.Value,
                TripId = s.Id
            }).ToList(), error =>
        {
            Snackbar.Add($"Error loading trips: {error.Code}: {error.Description}", Severity.Error);
            return [];
        });
    }

    public class CustomCalendarItem : CalendarItem
    {
        public TripStatus Status { get; set; }
        public Guid TripId { get; set; }
    }

}