<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <base href="/"/>
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet"/>
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet"/>
    <link href="app.css" rel="stylesheet"/>
    <link rel="icon" type="image/ico" href="favicon.ico"/>
    <link href="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.css" rel="stylesheet"/>
    <link href="_content/Heron.MudCalendar/Heron.MudCalendar.min.css" rel="stylesheet"/>
    <HeadOutlet @rendermode="InteractiveServer"/>

    <style>
        #blazor-loading-ui {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #fff;
            z-index: 9999;
        }

        .loading-logo {
            width: 200px;
            height: 200px;
            margin-bottom: 20px;
        }

        .loading-progress {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        .loading-progress-text {
            font-family: 'Roboto', sans-serif;
            font-size: 1rem;
            color: #555;
        }

        .loading-progress-circular {
            width: 80px;
            height: 80px;
            animation: rotate 2s linear infinite;
        }

        .loading-progress-circular-path {
            stroke-dasharray: 89, 200;
            stroke-dashoffset: 0;
            stroke: #1976d2; /* MudBlazor primary color */
            stroke-linecap: round;
            animation: dash 1.5s ease-in-out infinite;
        }

        @@keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }

        @@keyframes dash {
            0% {
                stroke-dasharray: 1, 200;
                stroke-dashoffset: 0;
            }
            50% {
                stroke-dasharray: 89, 200;
                stroke-dashoffset: -35;
            }
            100% {
                stroke-dasharray: 89, 200;
                stroke-dashoffset: -124;
            }
        }
    </style>
</head>

<body>

<div id="blazor-loading-ui">
    <img src="/img/logo_circle.png" alt="Logo" class="loading-logo"/>
    <div class="loading-progress">
        <svg class="loading-progress-circular" viewBox="22 22 44 44">
            <circle class="loading-progress-circular-path" cx="44" cy="44" r="20" fill="none" stroke-width="4"></circle>
        </svg>
    </div>
    <div class="loading-progress-text">Loading...</div>
</div>

<Routes @rendermode="new InteractiveServerRenderMode(prerender: false)"/>
<script src="_framework/blazor.web.js"></script>
<script src="_content/MudBlazor/MudBlazor.min.js"></script>
<script src="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.js"></script>
<script type="module" src="_content/Heron.MudCalendar/Heron.MudCalendar.min.js"></script>

<script>
    // Hide splash screen when Blazor is fully loaded
    document.addEventListener("DOMContentLoaded", function () {
        // Try to hide the loading UI after a short delay
        setTimeout(function () {
            const loadingElement = document.getElementById('blazor-loading-ui');
            if (loadingElement) {
                // Check if Blazor is defined
                if (typeof Blazor !== 'undefined') {
                    try {
                        // For .NET 7+ with Blazor Web
                        if (Blazor._internal && Blazor._internal.navigationManager) {
                            hideLoadingUI(loadingElement);
                        } else {
                            // Set up a MutationObserver to detect when the app is rendered
                            setupMutationObserver(loadingElement);
                        }
                    } catch {
                        // Fallback: setup a mutation observer
                        setupMutationObserver(loadingElement);
                    }
                } else {
                    // Fallback: setup a mutation observer
                    setupMutationObserver(loadingElement);
                }
            }
        }, 500);
    });

    function setupMutationObserver(loadingElement) {
        // Check if the main app elements exist
        const appElement = document.querySelector('app') || document.querySelector('.mud-layout');

        if (appElement) {
            hideLoadingUI(loadingElement);
            return;
        }

        // Set up a mutation observer to detect when the app is rendered
        const observer = new MutationObserver(function (mutations) {
            for (let mutation of mutations) {
                if (mutation.addedNodes.length) {
                    const appRendered = document.querySelector('app') || document.querySelector('.mud-layout');
                    if (appRendered) {
                        hideLoadingUI(loadingElement);
                        observer.disconnect();
                        break;
                    }
                }
            }
        });

        // Start observing
        observer.observe(document.body, {childList: true, subtree: true});

        // Fallback: Hide loading UI after a maximum timeout (8 seconds)
        setTimeout(function () {
            hideLoadingUI(loadingElement);
            observer.disconnect();
        }, 8000);
    }

    function hideLoadingUI(element) {
        // Fade out and remove the loading UI
        element.style.transition = 'opacity 0.5s ease';
        element.style.opacity = '0';
        setTimeout(function () {
            element.remove();
        }, 500);
    }
</script>
</body>

</html>