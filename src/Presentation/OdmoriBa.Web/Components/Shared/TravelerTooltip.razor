@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Core.Domains.Travelers.Entities

<div class="traveler-tooltip">
    <div class="tooltip-header">
        <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2"/>
        <strong>@Traveler.Person?.FullName</strong>
        @if (Traveler.Person != null)
        {
            <span class="age-badge">(@GetAge() god.)</span>
        }
    </div>
    
    <div class="tooltip-content">
        @if (!string.IsNullOrEmpty(Traveler.Person?.City))
        {
            <div class="tooltip-row">
                <MudIcon Icon="@Icons.Material.Filled.LocationCity" Size="Size.Small" Class="mr-2"/>
                <span class="label">Grad:</span>
                <span class="value">@Traveler.Person.City</span>
            </div>
        }
        
        @if (TravelParty?.MainContact != null)
        {
            <div class="tooltip-row">
                <MudIcon Icon="@Icons.Material.Filled.Group" Size="Size.Small" Class="mr-2"/>
                <span class="label">Grupa:</span>
                <span class="value">@TravelParty.MainContact.FullName</span>
            </div>
        }
        
        @if (Traveler.DeparturePoint != null)
        {
            <div class="tooltip-row">
                <MudIcon Icon="@Icons.Material.Filled.FlightTakeoff" Size="Size.Small" Class="mr-2"/>
                <span class="label">Polazište:</span>
                <span class="value">@Traveler.DeparturePoint.Stop?.FullName</span>
            </div>
        }

        @if (Traveler.ReturnDeparturePoint != null)
        {
            <div class="tooltip-row">
                <MudIcon Icon="@Icons.Material.Filled.FlightLand" Size="Size.Small" Class="mr-2"/>
                <span class="label">Povratak:</span>
                <span class="value">@Traveler.ReturnDeparturePoint.Stop?.FullName</span>
            </div>
        }
        
        @if (!string.IsNullOrEmpty(Traveler.Note))
        {
            <div class="tooltip-row">
                <MudIcon Icon="@Icons.Material.Filled.Note" Size="Size.Small" Class="mr-2"/>
                <span class="label">Napomena:</span>
                <span class="value">@Traveler.Note</span>
            </div>
        }
        
        <div class="tooltip-row">
            <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Small" Class="mr-2"/>
            <span class="label">Cijena:</span>
            <span class="value">@Traveler.Price.ToString("C", System.Globalization.CultureInfo.GetCultureInfo("bs-BA"))</span>
        </div>
        
        <div class="tooltip-row">
            <MudIcon Icon="@GetStatusIcon()" Size="Size.Small" Class="mr-2" Color="@GetStatusColor()"/>
            <span class="label">Status:</span>
            <span class="value">@Traveler.Status.GetLocalized()</span>
        </div>
    </div>
</div>

<style>
    .traveler-tooltip {
        max-width: 300px;
        font-size: 12px;
    }
    
    .tooltip-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 4px;
        border-bottom: 1px solid #e0e0e0;
        font-weight: bold;
        gap: 8px;
    }

    .age-badge {
        font-size: 11px;
        font-weight: normal;
        color: #666;
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 10px;
        white-space: nowrap;
    }
    
    .tooltip-content {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    
    .tooltip-row {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .tooltip-row .label {
        font-weight: 500;
        min-width: 70px;
    }
    
    .tooltip-row .value {
        flex: 1;
        word-break: break-word;
    }
</style>

@code {
    [Parameter, EditorRequired] public TravelerDto Traveler { get; set; } = null!;
    [Parameter] public TravelPartyDto? TravelParty { get; set; }

    private string GetStatusIcon()
    {
        return Traveler.Status switch
        {
            TravelerStatus.Requested => Icons.Material.Filled.HourglassEmpty,
            TravelerStatus.Draft => Icons.Material.Filled.Edit,
            TravelerStatus.Confirmed => Icons.Material.Filled.CheckCircle,
            TravelerStatus.Cancelled => Icons.Material.Filled.Cancel,
            _ => Icons.Material.Filled.Help
        };
    }

    private Color GetStatusColor()
    {
        return Traveler.Status switch
        {
            TravelerStatus.Requested => Color.Warning,
            TravelerStatus.Draft => Color.Info,
            TravelerStatus.Confirmed => Color.Success,
            TravelerStatus.Cancelled => Color.Error,
            _ => Color.Default
        };
    }

    private int GetAge()
    {
        if (Traveler.Person?.BirthDate == null) return 0;

        var today = DateOnly.FromDateTime(DateTime.Today);
        var birthDate = DateOnly.FromDateTime(Traveler.Person.BirthDate);

        var age = today.Year - birthDate.Year;

        // Check if birthday hasn't occurred this year yet
        if (today < birthDate.AddYears(age))
        {
            age--;
        }

        return age;
    }


}
