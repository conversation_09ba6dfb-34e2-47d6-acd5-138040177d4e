using MudBlazor;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Web.Common.Extensions;

public static class TripExtensions
{
    public static Color GetColor(this TripStatus status) => status switch
    {
        TripStatus.Draft => Color.Default,
        TripStatus.Cancelled => Color.Error,
        TripStatus.Published => Color.Primary,
        TripStatus.Completed => Color.Success,
        _ => Color.Default
    };
    
    public static string GetIcon(this TripStatus status) => status switch
    {
        TripStatus.Draft => Icons.Material.Filled.Edit,
        TripStatus.Cancelled => Icons.Material.Filled.Cancel,
        TripStatus.Published => Icons.Material.Filled.Publish,
        TripStatus.Completed =>Icons.Material.Filled.CheckCircle,
        _ => null!
    };

    public static Color GetColor(this TravelerStatus status) => status switch
    {
        TravelerStatus.Requested => Color.Warning,
        TravelerStatus.Draft => Color.Primary,
        TravelerStatus.Cancelled => Color.Error,
        TravelerStatus.Confirmed => Color.Success,
        _ => Color.Default
    };
    
    public static string GetIcon(this TravelerStatus status) => status switch
    {
        TravelerStatus.Requested => Icons.Material.Filled.Pending,
        TravelerStatus.Draft => Icons.Material.Filled.Edit,
        TravelerStatus.Cancelled => Icons.Material.Filled.Cancel,
        TravelerStatus.Confirmed =>Icons.Material.Filled.CheckCircle,
        _ => null!
    };

}