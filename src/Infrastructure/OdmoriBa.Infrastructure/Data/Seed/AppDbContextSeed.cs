using Microsoft.EntityFrameworkCore;
using OdmoriBa.Core.Common;

namespace OdmoriBa.Infrastructure.Data.Seed;

public static class AppDbContextSeed
{
    public static void Seed(DbContext context)
    {
        AddData(context, DestinationSeed.Data);
        AddData(context, CitySeed.Data);
    }

    public static async Task SeedAsync(DbContext context, CancellationToken cancellationToken)
    {
        await AddDataAsync(context, DestinationSeed.Data, cancellationToken);
        await AddDataAsync(context, CitySeed.Data, cancellationToken);
    }

    private static void AddData<T>(DbContext context, IEnumerable<T> entity) where T : EntityBase
    {
        if (context.Set<T>().Any()) return;
        context.Set<T>().AddRange(entity);
        context.SaveChanges();
    }

    private static async Task AddDataAsync<T>(DbContext context, IEnumerable<T> entity,
        CancellationToken cancellationToken)
        where T : EntityBase
    {
        if (await context.Set<T>().AnyAsync(cancellationToken)) return;
        context.Set<T>().AddRange(entity);
        await context.SaveChangesAsync(cancellationToken);
    }
}