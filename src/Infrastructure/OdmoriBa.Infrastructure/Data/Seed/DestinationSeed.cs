using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Infrastructure.Data.Seed;

internal static class DestinationSeed
{
    public static readonly IEnumerable<Destination> Data =
    [
        Destination.Create("Bar - Šusanj").Value,
        Destination.Create("Be<PERSON>").Value,
        Destination.Create("Beograd").Value,
        Destination.Create("Dobre Vode").Value,
        Destination.Create("Istanbul").Value,
        Destination.Create("Novi Pazar").Value,
        Destination.Create("Sutomore").Value,
        Destination.Create("Ulcinj").Value,
        Destination.Create("Zlatibor").Value
    ];
}