using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Infrastructure.Data.Seed;

internal static class CitySeed
{
    public static readonly IEnumerable<City> Data =
    [
        City.Create("BA", "Banja Luka").Value,
        City.Create("BA", "Berkovići").Value,
        City.Create("BA", "Bih<PERSON>ć").Value,
        City.Create("BA", "Bijeljina").Value,
        City.Create("BA", "Bileća").Value,
        City.Create("BA", "Bosanska Krupa").Value,
        City.Create("BA", "Bosanski Petrovac").Value,
        City.Create("BA", "Bosansko Grahovo").Value,
        City.Create("BA", "Bratunac").Value,
        City.Create("BA", "Breza").Value,
        City.Create("BA", "Brčko").Value,
        City.Create("BA", "Bugojno").Value,
        City.Create("BA", "<PERSON>azin").Value,
        City.Create("BA", "Čajniče").Value,
        City.Create("BA", "<PERSON>apljina").Value,
        City.Create("BA", "<PERSON><PERSON><PERSON>").Value,
        City.Create("BA", "Derventa").Value,
        City.Create("BA", "Doboj").Value,
        City.Create("BA", "Doboj Istok").Value,
        City.Create("BA", "Doboj Jug").Value,
        City.Create("BA", "Donji Vakuf").Value,
        City.Create("BA", "Foča").Value,
        City.Create("BA", "Goražde").Value,
        City.Create("BA", "Gornji Vakuf-Uskoplje").Value,
        City.Create("BA", "Gradačac").Value,
        City.Create("BA", "Gradiška").Value,
        City.Create("BA", "Hadžići").Value,
        City.Create("BA", "Han Pijesak").Value,
        City.Create("BA", "Ilidža").Value,
        City.Create("BA", "Ilijaš").Value,
        City.Create("BA", "Istočno Sarajevo").Value,
        City.Create("BA", "Jajce").Value,
        City.Create("BA", "Kakanj").Value,
        City.Create("BA", "Kiseljak").Value,
        City.Create("BA", "Ključ").Value,
        City.Create("BA", "Konjic").Value,
        City.Create("BA", "Kotor Varoš").Value,
        City.Create("BA", "Kreševo").Value,
        City.Create("BA", "Kupres").Value,
        City.Create("BA", "Livno").Value,
        City.Create("BA", "Lopare").Value,
        City.Create("BA", "Lukavac").Value,
        City.Create("BA", "Maglaj").Value,
        City.Create("BA", "Milići").Value,
        City.Create("BA", "Modriča").Value,
        City.Create("BA", "Mostar").Value,
        City.Create("BA", "Mrkonjić Grad").Value,
        City.Create("BA", "Neum").Value,
        City.Create("BA", "Novi Travnik").Value,
        City.Create("BA", "Olovo").Value,
        City.Create("BA", "Pale").Value,
        City.Create("BA", "Petrovac").Value,
        City.Create("BA", "Prijedor").Value,
        City.Create("BA", "Prozor-Rama").Value,
        City.Create("BA", "Rogatica").Value,
        City.Create("BA", "Sanski Most").Value,
        City.Create("BA", "Sarajevo").Value,
        City.Create("BA", "Sokolac").Value,
        City.Create("BA", "Srebrenik").Value,
        City.Create("BA", "Stanari").Value,
        City.Create("BA", "Šamac").Value,
        City.Create("BA", "Široki Brijeg").Value,
        City.Create("BA", "Teslić").Value,
        City.Create("BA", "Tešanj").Value,
        City.Create("BA", "Tomislavgrad").Value,
        City.Create("BA", "Travnik").Value,
        City.Create("BA", "Trebinje").Value,
        City.Create("BA", "Trnovo").Value,
        City.Create("BA", "Tuzla").Value,
        City.Create("BA", "Ugljevik").Value,
        City.Create("BA", "Velika Kladuša").Value,
        City.Create("BA", "Visoko").Value,
        City.Create("BA", "Vitez").Value,
        City.Create("BA", "Vlasenica").Value,
        City.Create("BA", "Vogošća").Value,
        City.Create("BA", "Zavidovići").Value,
        City.Create("BA", "Zenica").Value,
        City.Create("BA", "Zvornik").Value
    ];
}