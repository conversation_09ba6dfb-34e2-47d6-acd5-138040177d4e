// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using OdmoriBa.Infrastructure.Data;

#nullable disable

namespace OdmoriBa.Infrastructure.Data.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250608113236_Initial9")]
    partial class Initial9
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("OdmoriBa.Core.Domains.Companies.Entities.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("address");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("contact_phone");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_companies");

                    b.ToTable("companies", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.DeviceBinding", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AppVersion")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("app_version");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("device_id");

                    b.Property<string>("DeviceModel")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("device_model");

                    b.Property<int>("DeviceType")
                        .HasColumnType("integer")
                        .HasColumnName("device_type");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("OsVersion")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("os_version");

                    b.Property<string>("PushNotificationToken")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("push_notification_token");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_device_bindings");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_device_bindings_user_id");

                    b.ToTable("device_bindings", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyCard", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<int>("TotalPoints")
                        .HasColumnType("integer")
                        .HasColumnName("total_points");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_loyalty_cards");

                    b.ToTable("loyalty_cards", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyPointTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("LoyaltyCardId")
                        .HasColumnType("uuid")
                        .HasColumnName("loyalty_card_id");

                    b.Property<Guid?>("TripDestinationId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_destination_id");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<int>("Value")
                        .HasColumnType("integer")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_loyalty_point_transactions");

                    b.HasIndex("LoyaltyCardId")
                        .HasDatabaseName("ix_loyalty_point_transactions_loyalty_card_id");

                    b.HasIndex("TripDestinationId")
                        .HasDatabaseName("ix_loyalty_point_transactions_trip_destination_id");

                    b.ToTable("loyalty_point_transactions", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.Person", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Address")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("address");

                    b.Property<DateOnly>("BirthDate")
                        .HasColumnType("date")
                        .HasColumnName("birth_date");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("city");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("country_code");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name");

                    b.Property<string>("IdDocument")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("id_document");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name");

                    b.Property<string>("Phone")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("phone");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_persons");

                    b.ToTable("persons", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<string>("IdentityId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("character varying(36)")
                        .HasColumnName("identity_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("phone");

                    b.Property<int>("Role")
                        .HasColumnType("integer")
                        .HasColumnName("role");

                    b.Property<string>("SignInProvider")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("sign_in_provider");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.HasIndex("IdentityId")
                        .IsUnique()
                        .HasDatabaseName("ix_users_identity_id")
                        .HasFilter("is_deleted = false");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.City", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)")
                        .HasColumnName("country_code");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_cities");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_cities_name");

                    b.ToTable("cities", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.Destination", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CoverId")
                        .HasColumnType("uuid")
                        .HasColumnName("cover_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_destinations");

                    b.HasIndex("CoverId")
                        .HasDatabaseName("ix_destinations_cover_id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_destinations_name");

                    b.ToTable("destinations", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.DestinationImage", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<Guid>("DestinationId")
                        .HasColumnType("uuid")
                        .HasColumnName("destination_id");

                    b.Property<Guid>("FileRecordId")
                        .HasColumnType("uuid")
                        .HasColumnName("file_record_id");

                    b.Property<Dictionary<int, string>>("ThumbnailUrls")
                        .HasColumnType("jsonb")
                        .HasColumnName("thumbnail_urls");

                    b.Property<string>("Title")
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_destination_images");

                    b.HasIndex("DestinationId")
                        .HasDatabaseName("ix_destination_images_destination_id");

                    b.HasIndex("FileRecordId")
                        .HasDatabaseName("ix_destination_images_file_record_id");

                    b.ToTable("destination_images", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.Stop", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("address");

                    b.Property<Guid>("CityId")
                        .HasColumnType("uuid")
                        .HasColumnName("city_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision")
                        .HasColumnName("latitude");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision")
                        .HasColumnName("longitude");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_stops");

                    b.HasIndex("CityId")
                        .HasDatabaseName("ix_stops_city_id");

                    b.ToTable("stops", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Files.Entities.FileRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("content_type");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<long>("Size")
                        .HasColumnType("bigint")
                        .HasColumnName("size");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("url");

                    b.HasKey("Id")
                        .HasName("pk_file_records");

                    b.ToTable("file_records", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Outbox.Entities.PushNotificationOutbox", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Dictionary<string, string>>("AdditionalData")
                        .HasColumnType("jsonb")
                        .HasColumnName("additional_data");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<int>("DeviceType")
                        .HasColumnType("integer")
                        .HasColumnName("device_type");

                    b.Property<string>("ErrorCode")
                        .HasColumnType("text")
                        .HasColumnName("error_code");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text")
                        .HasColumnName("error_message");

                    b.Property<int>("MaxRetryCount")
                        .HasColumnType("integer")
                        .HasColumnName("max_retry_count");

                    b.Property<string>("MessageId")
                        .HasColumnType("text")
                        .HasColumnName("message_id");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer")
                        .HasColumnName("retry_count");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("token");

                    b.Property<string>("Topic")
                        .HasColumnType("text")
                        .HasColumnName("topic");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_push_notification_outbox");

                    b.ToTable("push_notification_outbox", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Transportations.Entities.Bus", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Capacity")
                        .HasColumnType("integer")
                        .HasColumnName("capacity");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_buses");

                    b.HasIndex("CompanyId")
                        .HasDatabaseName("ix_buses_company_id");

                    b.ToTable("buses", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision")
                        .HasColumnName("amount");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<DateTime>("PaidAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("paid_at");

                    b.Property<Guid>("PaidByPersonId")
                        .HasColumnType("uuid")
                        .HasColumnName("paid_by_person_id");

                    b.Property<Guid>("TravelPartyId")
                        .HasColumnType("uuid")
                        .HasColumnName("travel_party_id");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_payments");

                    b.HasIndex("PaidByPersonId")
                        .HasDatabaseName("ix_payments_paid_by_person_id");

                    b.HasIndex("TravelPartyId")
                        .HasDatabaseName("ix_payments_travel_party_id");

                    b.ToTable("payments", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.TravelParty", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("MainContactId")
                        .HasColumnType("uuid")
                        .HasColumnName("main_contact_id");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<string>("RequestNote")
                        .HasColumnType("text")
                        .HasColumnName("request_note");

                    b.Property<Guid>("TripDestinationId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_destination_id");

                    b.Property<Guid>("TripId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_travel_parties");

                    b.HasIndex("MainContactId")
                        .HasDatabaseName("ix_travel_parties_main_contact_id");

                    b.HasIndex("TripDestinationId")
                        .HasDatabaseName("ix_travel_parties_trip_destination_id");

                    b.HasIndex("TripId")
                        .HasDatabaseName("ix_travel_parties_trip_id");

                    b.ToTable("travel_parties", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.Traveler", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeparturePointId")
                        .HasColumnType("uuid")
                        .HasColumnName("departure_point_id");

                    b.Property<int?>("DepartureSeatNumber")
                        .HasColumnType("integer")
                        .HasColumnName("departure_seat_number");

                    b.Property<Guid?>("DepartureTripBusId")
                        .HasColumnType("uuid")
                        .HasColumnName("departure_trip_bus_id");

                    b.Property<double>("Discount")
                        .HasColumnType("double precision")
                        .HasColumnName("discount");

                    b.Property<double>("InsurancePrice")
                        .HasColumnType("double precision")
                        .HasColumnName("insurance_price");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasColumnType("text")
                        .HasColumnName("note");

                    b.Property<Guid>("PersonId")
                        .HasColumnType("uuid")
                        .HasColumnName("person_id");

                    b.Property<double>("Price")
                        .HasColumnType("double precision")
                        .HasColumnName("price");

                    b.Property<Guid?>("ReturnDeparturePointId")
                        .HasColumnType("uuid")
                        .HasColumnName("return_departure_point_id");

                    b.Property<int?>("ReturnSeatNumber")
                        .HasColumnType("integer")
                        .HasColumnName("return_seat_number");

                    b.Property<Guid?>("ReturnTripBusId")
                        .HasColumnType("uuid")
                        .HasColumnName("return_trip_bus_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<double>("TaxPrice")
                        .HasColumnType("double precision")
                        .HasColumnName("tax_price");

                    b.Property<Guid>("TravelPartyId")
                        .HasColumnType("uuid")
                        .HasColumnName("travel_party_id");

                    b.Property<int>("TravelServiceType")
                        .HasColumnType("integer")
                        .HasColumnName("travel_service_type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_travelers");

                    b.HasIndex("DeparturePointId")
                        .HasDatabaseName("ix_travelers_departure_point_id");

                    b.HasIndex("DepartureTripBusId")
                        .HasDatabaseName("ix_travelers_departure_trip_bus_id");

                    b.HasIndex("PersonId")
                        .HasDatabaseName("ix_travelers_person_id");

                    b.HasIndex("ReturnDeparturePointId")
                        .HasDatabaseName("ix_travelers_return_departure_point_id");

                    b.HasIndex("ReturnTripBusId")
                        .HasDatabaseName("ix_travelers_return_trip_bus_id");

                    b.HasIndex("TravelPartyId")
                        .HasDatabaseName("ix_travelers_travel_party_id");

                    b.ToTable("travelers", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.Itinerary", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("DateTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_time");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<Guid>("TripDestinationId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_destination_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_itinerary");

                    b.HasIndex("TripDestinationId")
                        .HasDatabaseName("ix_itinerary_trip_destination_id");

                    b.ToTable("itinerary", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.Trip", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateOnly>("EndDate")
                        .HasColumnType("date")
                        .HasColumnName("end_date");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date")
                        .HasColumnName("start_date");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("title");

                    b.Property<int>("TransportationType")
                        .HasColumnType("integer")
                        .HasColumnName("transportation_type");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_trips");

                    b.ToTable("trips", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripBus", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("BusId")
                        .HasColumnType("uuid")
                        .HasColumnName("bus_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<int>("Direction")
                        .HasColumnType("integer")
                        .HasColumnName("direction");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.Property<Guid>("TripId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_trip_buses");

                    b.HasIndex("BusId")
                        .HasDatabaseName("ix_trip_buses_bus_id");

                    b.HasIndex("TripId")
                        .HasDatabaseName("ix_trip_buses_trip_id");

                    b.ToTable("trip_buses", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<Guid>("DestinationId")
                        .HasColumnType("uuid")
                        .HasColumnName("destination_id");

                    b.Property<DateOnly>("EndDate")
                        .HasColumnType("date")
                        .HasColumnName("end_date");

                    b.Property<bool>("Featured")
                        .HasColumnType("boolean")
                        .HasColumnName("featured");

                    b.Property<double>("InsurancePrice")
                        .HasColumnType("double precision")
                        .HasColumnName("insurance_price");

                    b.Property<double>("Price")
                        .HasColumnType("double precision")
                        .HasColumnName("price");

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("date")
                        .HasColumnName("start_date");

                    b.Property<double>("TaxPrice")
                        .HasColumnType("double precision")
                        .HasColumnName("tax_price");

                    b.Property<Guid>("TripId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_trip_destinations");

                    b.HasIndex("DestinationId")
                        .HasDatabaseName("ix_trip_destinations_destination_id");

                    b.HasIndex("TripId")
                        .HasDatabaseName("ix_trip_destinations_trip_id");

                    b.ToTable("trip_destinations", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripStop", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("BeginAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("begin_at");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTimeOffset>("EndAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("end_at");

                    b.Property<Guid>("StopId")
                        .HasColumnType("uuid")
                        .HasColumnName("stop_id");

                    b.Property<Guid>("TripId")
                        .HasColumnType("uuid")
                        .HasColumnName("trip_id");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_trip_stops");

                    b.HasIndex("StopId")
                        .HasDatabaseName("ix_trip_stops_stop_id");

                    b.HasIndex("TripId")
                        .HasDatabaseName("ix_trip_stops_trip_id");

                    b.ToTable("trip_stops", (string)null);
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.DeviceBinding", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.User", "User")
                        .WithMany("Devices")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_device_bindings_users_user_id");

                    b.Navigation("User");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyCard", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.Person", "Person")
                        .WithOne("LoyaltyCard")
                        .HasForeignKey("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyCard", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_loyalty_cards_persons_id");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyPointTransaction", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyCard", null)
                        .WithMany("PointTransactions")
                        .HasForeignKey("LoyaltyCardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_loyalty_point_transactions_loyalty_cards_loyalty_card_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", "TripDestination")
                        .WithMany()
                        .HasForeignKey("TripDestinationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_loyalty_point_transactions_trip_destinations_trip_destinati");

                    b.Navigation("TripDestination");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.User", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.Person", "Person")
                        .WithOne("User")
                        .HasForeignKey("OdmoriBa.Core.Domains.Customers.Entities.User", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_users_persons_id");

                    b.Navigation("Person");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.Destination", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Files.Entities.FileRecord", "Cover")
                        .WithMany()
                        .HasForeignKey("CoverId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_destinations_file_records_cover_id");

                    b.Navigation("Cover");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.DestinationImage", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Destinations.Entities.Destination", null)
                        .WithMany("Images")
                        .HasForeignKey("DestinationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_destination_images_destinations_destination_id");

                    b.HasOne("OdmoriBa.Core.Domains.Files.Entities.FileRecord", "FileRecord")
                        .WithMany()
                        .HasForeignKey("FileRecordId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_destination_images_file_records_file_record_id");

                    b.Navigation("FileRecord");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.Stop", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Destinations.Entities.City", "City")
                        .WithMany()
                        .HasForeignKey("CityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_stops_cities_city_id");

                    b.Navigation("City");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Transportations.Entities.Bus", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Companies.Entities.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_buses_companies_company_id");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.Payment", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.Person", "PaidByPerson")
                        .WithMany()
                        .HasForeignKey("PaidByPersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_payments_persons_paid_by_person_id");

                    b.HasOne("OdmoriBa.Core.Domains.Travelers.Entities.TravelParty", null)
                        .WithMany("Payments")
                        .HasForeignKey("TravelPartyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_payments_travel_parties_travel_party_id");

                    b.Navigation("PaidByPerson");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.TravelParty", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.Person", "MainContact")
                        .WithMany()
                        .HasForeignKey("MainContactId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_travel_parties_persons_main_contact_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", "TripDestination")
                        .WithMany()
                        .HasForeignKey("TripDestinationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_travel_parties_trip_destinations_trip_destination_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.Trip", "Trip")
                        .WithMany("TravelParties")
                        .HasForeignKey("TripId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_travel_parties_trips_trip_id");

                    b.Navigation("MainContact");

                    b.Navigation("Trip");

                    b.Navigation("TripDestination");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.Traveler", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripStop", "DeparturePoint")
                        .WithMany()
                        .HasForeignKey("DeparturePointId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_travelers_trip_stops_departure_point_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripBus", "DepartureTripBus")
                        .WithMany()
                        .HasForeignKey("DepartureTripBusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_travelers_trip_buses_departure_trip_bus_id");

                    b.HasOne("OdmoriBa.Core.Domains.Customers.Entities.Person", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_travelers_persons_person_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripStop", "ReturnDeparturePoint")
                        .WithMany()
                        .HasForeignKey("ReturnDeparturePointId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_travelers_trip_stops_return_departure_point_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripBus", "ReturnTripBus")
                        .WithMany()
                        .HasForeignKey("ReturnTripBusId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_travelers_trip_buses_return_trip_bus_id");

                    b.HasOne("OdmoriBa.Core.Domains.Travelers.Entities.TravelParty", "TravelParty")
                        .WithMany("Travelers")
                        .HasForeignKey("TravelPartyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_travelers_travel_parties_travel_party_id");

                    b.Navigation("DeparturePoint");

                    b.Navigation("DepartureTripBus");

                    b.Navigation("Person");

                    b.Navigation("ReturnDeparturePoint");

                    b.Navigation("ReturnTripBus");

                    b.Navigation("TravelParty");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.Itinerary", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", null)
                        .WithMany("Itineraries")
                        .HasForeignKey("TripDestinationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_itinerary_trip_destinations_trip_destination_id");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripBus", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Transportations.Entities.Bus", "Bus")
                        .WithMany()
                        .HasForeignKey("BusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_buses_buses_bus_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.Trip", null)
                        .WithMany("TripBuses")
                        .HasForeignKey("TripId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_buses_trips_trip_id");

                    b.Navigation("Bus");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Destinations.Entities.Destination", "Destination")
                        .WithMany()
                        .HasForeignKey("DestinationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_destinations_destinations_destination_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.Trip", null)
                        .WithMany("TripDestinations")
                        .HasForeignKey("TripId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_destinations_trips_trip_id");

                    b.OwnsOne("OdmoriBa.Core.Domains.Trips.Entities.TripDestinationLoyalty", "Loyalty", b1 =>
                        {
                            b1.Property<Guid>("TripDestinationId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<bool>("CanSpendPoints")
                                .HasColumnType("boolean")
                                .HasColumnName("loyalty_can_spend_points");

                            b1.Property<int>("MaximumPointsToSpend")
                                .HasColumnType("integer")
                                .HasColumnName("loyalty_maximum_points_to_spend");

                            b1.Property<int>("Points")
                                .HasColumnType("integer")
                                .HasColumnName("loyalty_points");

                            b1.HasKey("TripDestinationId");

                            b1.ToTable("trip_destinations");

                            b1.WithOwner()
                                .HasForeignKey("TripDestinationId")
                                .HasConstraintName("fk_trip_destinations_trip_destinations_id");
                        });

                    b.Navigation("Destination");

                    b.Navigation("Loyalty")
                        .IsRequired();
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripStop", b =>
                {
                    b.HasOne("OdmoriBa.Core.Domains.Destinations.Entities.Stop", "Stop")
                        .WithMany()
                        .HasForeignKey("StopId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_stops_stops_stop_id");

                    b.HasOne("OdmoriBa.Core.Domains.Trips.Entities.Trip", null)
                        .WithMany("TripStops")
                        .HasForeignKey("TripId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_trip_stops_trips_trip_id");

                    b.Navigation("Stop");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.LoyaltyCard", b =>
                {
                    b.Navigation("PointTransactions");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.Person", b =>
                {
                    b.Navigation("LoyaltyCard")
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Customers.Entities.User", b =>
                {
                    b.Navigation("Devices");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Destinations.Entities.Destination", b =>
                {
                    b.Navigation("Images");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Travelers.Entities.TravelParty", b =>
                {
                    b.Navigation("Payments");

                    b.Navigation("Travelers");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.Trip", b =>
                {
                    b.Navigation("TravelParties");

                    b.Navigation("TripBuses");

                    b.Navigation("TripDestinations");

                    b.Navigation("TripStops");
                });

            modelBuilder.Entity("OdmoriBa.Core.Domains.Trips.Entities.TripDestination", b =>
                {
                    b.Navigation("Itineraries");
                });
#pragma warning restore 612, 618
        }
    }
}
