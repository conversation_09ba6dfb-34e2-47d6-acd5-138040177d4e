using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using OdmoriBa.Core.Common;

namespace OdmoriBa.Infrastructure.Data.Interceptors;

public sealed class SoftDeleteInterceptor : SaveChangesInterceptor
{
    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        UpdateEntities(eventData.Context);

        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData,
        InterceptionResult<int> result, CancellationToken cancellationToken = default)
    {
        UpdateEntities(eventData.Context);

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void UpdateEntities(DbContext? context)
    {
        if (context is null) return;

        IEnumerable<EntityEntry<ISoftDelete>> entries = context
            .ChangeTracker
            .Entries<ISoftDelete>()
            .Where(e => e.State == EntityState.Deleted);

        foreach (EntityEntry<ISoftDelete> softDeletable in entries)
        {
            softDeletable.State = EntityState.Modified;
            softDeletable.Property(nameof(ISoftDelete.IsDeleted)).CurrentValue = true;
            softDeletable.Property(nameof(ISoftDelete.DeletedAt)).CurrentValue = DateTimeOffset.Now;

            // Handle owned navigations
            foreach (var navigation in softDeletable.Navigations)
            {
                if (!navigation.Metadata.TargetEntityType.IsOwned()) continue;

                if (navigation.CurrentValue is ISoftDelete owned)
                {
                    var ownedEntry = context.Entry(owned);
                    ownedEntry.State = EntityState.Modified;
                    ownedEntry.Property(nameof(ISoftDelete.IsDeleted)).CurrentValue = true;
                    ownedEntry.Property(nameof(ISoftDelete.DeletedAt)).CurrentValue = DateTimeOffset.UtcNow;
                }

                // Handle collections of owned types
                if (navigation.CurrentValue is IEnumerable<ISoftDelete> ownedCollection)
                {
                    foreach (var ownedItem in ownedCollection)
                    {
                        var ownedEntry = context.Entry(ownedItem);
                        ownedEntry.State = EntityState.Modified;
                        ownedEntry.Property(nameof(ISoftDelete.IsDeleted)).CurrentValue = true;
                        ownedEntry.Property(nameof(ISoftDelete.DeletedAt)).CurrentValue = DateTimeOffset.UtcNow;
                    }
                }
            }
        }
    }
}