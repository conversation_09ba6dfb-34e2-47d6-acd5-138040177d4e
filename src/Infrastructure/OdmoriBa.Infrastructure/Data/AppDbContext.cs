using System.Reflection;
using Microsoft.EntityFrameworkCore;
using OdmoriBa.Application.Interfaces.Data;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Files.Entities;
using OdmoriBa.Core.Domains.Outbox.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;
using OdmoriBa.Infrastructure.Data.Converters;

namespace OdmoriBa.Infrastructure.Data;

public sealed class AppDbContext(DbContextOptions options) : DbContext(options), IAppDbContext
{
    public DbSet<User> Users { get; set; }
    public DbSet<City> Cities { get; set; }
    public DbSet<Destination> Destinations { get; set; }
    public DbSet<DestinationImage> DestinationImages { get; set; }
    public DbSet<Stop> Stops { get; set; }

    public DbSet<Trip> Trips { get; set; }
    public DbSet<TripStop> TripStops { get; set; }
    public DbSet<TripDestination> TripDestinations { get; set; }
    public DbSet<Traveler> Travelers { get; set; }
    public DbSet<TravelParty> TravelParties { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Itinerary> Itinerary { get; set; }
    public DbSet<Person> Persons { get; set; }
    public DbSet<LoyaltyCard> LoyaltyCards { get; set; }
    public DbSet<LoyaltyPointTransaction> LoyaltyPointTransactions { get; set; }
    public DbSet<FileRecord> FileRecords { get; set; }

    public DbSet<Company> Companies { get; set; }
    public DbSet<Bus> Buses { get; set; }
    public DbSet<TripBus> TripBuses { get; set; }

    public DbSet<DeviceBinding> DeviceBindings { get; set; }
    public DbSet<PushNotificationOutbox> PushNotificationOutbox { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder
            .Properties<DateTimeOffset>()
            .HaveConversion<DateTimeOffsetConverter>();
    }
}