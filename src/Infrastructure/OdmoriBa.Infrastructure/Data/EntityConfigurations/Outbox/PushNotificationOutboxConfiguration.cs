using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Outbox.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Outbox;

internal class PushNotificationOutboxConfiguration : IEntityTypeConfiguration<PushNotificationOutbox>
{
    public void Configure(EntityTypeBuilder<PushNotificationOutbox> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.Type).IsRequired();
        builder.Property(p => p.DeviceType).IsRequired();
        builder.Property(p => p.Title).IsRequired();
        builder.Property(p => p.Body).IsRequired();
        builder.Property(p => p.AdditionalData).HasColumnType("jsonb");
    }
}