using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Customers;

internal sealed class LoyaltyCardConfiguration : IEntityTypeConfiguration<LoyaltyCard>
{
    public void Configure(EntityTypeBuilder<LoyaltyCard> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        
        builder.HasQueryFilter(p => !p.IsDeleted);

        builder.HasMany(p => p.PointTransactions)
            .WithOne()
            .HasForeignKey(p => p.LoyaltyCardId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}