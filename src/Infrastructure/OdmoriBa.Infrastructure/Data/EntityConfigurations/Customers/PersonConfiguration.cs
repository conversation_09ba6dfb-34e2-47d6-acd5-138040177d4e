using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Infrastructure.Data.Converters;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Customers;

internal sealed class PersonConfiguration : IEntityTypeConfiguration<Person>
{
    public void Configure(EntityTypeBuilder<Person> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.FirstName).HasMaxLength(100).IsRequired();
        builder.Property(p => p.LastName).HasMaxLength(100).IsRequired();
        builder.Property(p => p.BirthDate).IsRequired();
        builder.Property(p => p.Email).HasMaxLength(100);
        builder.Property(p => p.Phone).HasMaxLength(15);
        builder.Property(p => p.IdDocument).HasMaxLength(15);
        builder.Property(p => p.City).HasMaxLength(100);
        builder.Property(p => p.CountryCode).HasMaxLength(2);
        builder.Property(p => p.Address).HasMaxLength(100);
        builder.Property(p => p.Phone).HasConversion<PhoneConverter>();

        builder.Navigation(p => p.User).AutoInclude();
        builder.HasOne(p => p.User)
            .WithOne(u => u.Person)
            .HasForeignKey<User>(u => u.Id)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Navigation(p => p.LoyaltyCard).AutoInclude();
        builder.HasOne(p => p.LoyaltyCard)
            .WithOne(u => u.Person)
            .HasForeignKey<LoyaltyCard>(u => u.Id)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(p => !p.IsDeleted);
    }
}