using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Customers;

public class DeviceBindingConfiguration : IEntityTypeConfiguration<DeviceBinding>
{
    public void Configure(EntityTypeBuilder<DeviceBinding> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.DeviceType).IsRequired();
        builder.Property(p => p.AppVersion).IsRequired();
        builder.Property(p => p.DeviceId).IsRequired();
        builder.Property(p => p.DeviceModel).IsRequired();
        builder.Property(p => p.OsVersion).IsRequired();
        builder.Property(p => p.PushNotificationToken).IsRequired();
        
        builder.HasQueryFilter(p => !p.<PERSON>);
    }
}