using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Customers;

internal sealed class LoyaltyPointTransactionConfiguration : IEntityTypeConfiguration<LoyaltyPointTransaction>
{
    public void Configure(EntityTypeBuilder<LoyaltyPointTransaction> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.HasOne(p => p.TripDestination)
            .WithMany()
            .HasForeignKey(p => p.TripDestinationId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}