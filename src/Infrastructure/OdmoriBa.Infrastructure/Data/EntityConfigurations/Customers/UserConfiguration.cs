using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Infrastructure.Data.Converters;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Customers;

internal sealed class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.IdentityId).HasMaxLength(36).IsRequired();
        builder.HasIndex(p => p.IdentityId).IsUnique().HasFilter("is_deleted = false");
        builder.Property(p => p.Email).HasMaxLength(100);
        builder.Property(p => p.Phone).IsRequired().HasConversion<PhoneConverter>();
        builder.Property(p => p.SignInProvider).IsRequired();
        builder.Property(p => p.Role).IsRequired();
        builder.Property(p => p.Status).IsRequired();

        builder.HasMany(p => p.Devices)
            .WithOne(p => p.User)
            .HasForeignKey(p => p.UserId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasQueryFilter(p => !p.IsDeleted);
    }
}