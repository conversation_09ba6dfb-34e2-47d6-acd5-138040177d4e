using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class TravelerConfiguration : IEntityTypeConfiguration<Traveler>
{
    public void Configure(EntityTypeBuilder<Traveler> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.Price).IsRequired();
        builder.Property(p => p.Status).IsRequired();
        
        builder.Navigation(p => p.Person).AutoInclude();
        builder.HasOne(p => p.Person)
            .WithMany()
            .HasForeignKey(p => p.PersonId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Navigation(p => p.DeparturePoint).AutoInclude();
        builder.HasOne(p => p.DeparturePoint)
            .WithMany()
            .HasForeignKey(p => p.DeparturePointId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Navigation(p => p.ReturnDeparturePoint).AutoInclude();
        builder.HasOne(p => p.ReturnDeparturePoint)
            .WithMany()
            .HasForeignKey(p => p.ReturnDeparturePointId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Navigation(p => p.DepartureTripBus).AutoInclude();
        builder.HasOne(p => p.DepartureTripBus)
            .WithMany()
            .HasForeignKey(p => p.DepartureTripBusId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Navigation(p => p.ReturnTripBus).AutoInclude();
        builder.HasOne(p => p.ReturnTripBus)
            .WithMany()
            .HasForeignKey(p => p.ReturnTripBusId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasQueryFilter(p => !p.IsDeleted);
    }
}