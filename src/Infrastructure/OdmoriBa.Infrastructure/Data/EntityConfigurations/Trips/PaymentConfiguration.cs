using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class PaymentConfiguration : IEntityTypeConfiguration<Payment>
{
    public void Configure(EntityTypeBuilder<Payment> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.Amount).IsRequired();
        builder.Property(p => p.PaidAt).IsRequired();
        builder.Navigation(p => p.<PERSON>id<PERSON>y<PERSON>erson).AutoInclude();
        builder.Property(p => p.Type).IsRequired();
        
        builder.HasOne(p => p.PaidByPerson)
            .WithMany()
            .HasForeignKey(p => p.PaidByPersonId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasQueryFilter(p => !p.<PERSON>);
    }
}