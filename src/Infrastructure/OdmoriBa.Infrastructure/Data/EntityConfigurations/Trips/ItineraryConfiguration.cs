using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class ItineraryConfiguration : IEntityTypeConfiguration<Itinerary>
{
    public void Configure(EntityTypeBuilder<Itinerary> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.DateTime).IsRequired();
        builder.Property(p => p.Title).IsRequired();
        builder.Property(p => p.Description).IsRequired();
    }
}