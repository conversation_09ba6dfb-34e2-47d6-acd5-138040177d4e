using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class TravelPartyConfiguration : IEntityTypeConfiguration<TravelParty>
{
    public void Configure(EntityTypeBuilder<TravelParty> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Navigation(p => p.MainContact).AutoInclude();
        
        builder.Navigation(p => p.TripDestination).AutoInclude();
        builder.HasOne(p => p.TripDestination)
            .WithMany()
            .HasForeignKey(p => p.TripDestinationId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.MainContact)
            .WithMany()
            .HasForeignKey(p => p.MainContactId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasMany(p => p.Payments)
            .WithOne()
            .HasForeignKey(p => p.TravelPartyId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.Navigation(p => p.Travelers).AutoInclude();
        builder.HasMany(p => p.Travelers)
            .WithOne(x => x.TravelParty)
            .HasForeignKey(p => p.TravelPartyId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasQueryFilter(p => !p.IsDeleted);
    }
}