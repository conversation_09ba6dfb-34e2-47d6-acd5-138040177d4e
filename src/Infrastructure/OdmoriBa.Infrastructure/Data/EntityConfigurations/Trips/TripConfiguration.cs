using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

internal sealed class TripConfiguration : IEntityTypeConfiguration<Trip>
{
    public void Configure(EntityTypeBuilder<Trip> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.Title).HasMaxLength(100).IsRequired();
        builder.Property(p => p.StartDate).IsRequired();
        builder.Property(p => p.EndDate).IsRequired();
        builder.Property(p => p.Type).IsRequired();
        builder.Property(p => p.Status).IsRequired();
        builder.Property(p => p.TransportationType).IsRequired();
        builder.Property(p => p.TransportationType).IsRequired();
        
        builder.HasMany(p => p.TripDestinations)
            .WithOne()
            .HasForeignKey(p => p.TripId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasMany(p => p.TripBuses)
            .WithOne()
            .HasForeignKey(p => p.TripId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasMany(p => p.TravelParties)
            .WithOne(x => x.Trip)
            .HasForeignKey(p => p.TripId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasMany(p => p.TripStops)
            .WithOne()
            .HasForeignKey(p => p.TripId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}