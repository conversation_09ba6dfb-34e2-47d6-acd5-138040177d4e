using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class TripBusConfiguration : IEntityTypeConfiguration<TripBus>
{
    public void Configure(EntityTypeBuilder<TripBus> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.Name).HasMaxLength(50);
        builder.Property(p => p.Direction).IsRequired();
    }
}