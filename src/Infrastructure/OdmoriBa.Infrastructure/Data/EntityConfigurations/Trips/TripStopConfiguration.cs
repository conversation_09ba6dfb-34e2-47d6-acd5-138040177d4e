using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

public sealed class TripStopConfiguration : IEntityTypeConfiguration<TripStop>
{
    public void Configure(EntityTypeBuilder<TripStop> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Navigation(p => p.Stop).AutoInclude();
        builder.Property(p => p.BeginAt).IsRequired();
        builder.Property(p => p.EndAt).IsRequired();
        builder.Property(p => p.Type).IsRequired();
        builder.Property(p => p.Type).IsRequired();
    }
}