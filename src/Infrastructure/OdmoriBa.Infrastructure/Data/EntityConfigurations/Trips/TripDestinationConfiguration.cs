using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Trips;

internal sealed class TripDestinationConfiguration : IEntityTypeConfiguration<TripDestination>
{
    public void Configure(EntityTypeBuilder<TripDestination> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.StartDate).IsRequired();
        builder.Property(p => p.EndDate).IsRequired();
        builder.Navigation(p => p.Destination).AutoInclude();
        
        builder.HasMany(p => p.Itineraries)
            .WithOne()
            .HasForeignKey(p => p.TripDestinationId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.OwnsOne<TripDestinationLoyalty>(s => s.Loyalty);
    }
}