using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Destinations;

internal sealed class DestinationConfiguration : IEntityTypeConfiguration<Destination>
{
    public void Configure(EntityTypeBuilder<Destination> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.Name).HasMaxLength(100).IsRequired();
        builder.HasIndex(p => p.Name).IsUnique();
        builder.Navigation(p => p.Cover).AutoInclude();

        builder.HasMany(e => e.Images)
            .WithOne()
            .HasForeignKey(e => e.DestinationId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasOne(p => p.Cover)
            .WithMany()
            .HasForeignKey(p => p.CoverId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}