using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Destinations;

internal sealed class StopConfiguration : IEntityTypeConfiguration<Stop>
{
    public void Configure(EntityTypeBuilder<Stop> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Navigation(s => s.City).AutoInclude();
        
        builder.HasOne(p => p.City)
            .WithMany()
            .HasForeignKey(p => p.CityId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}