using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Destinations;

internal sealed class DestinationImageConfiguration : IEntityTypeConfiguration<DestinationImage>
{
    public void Configure(EntityTypeBuilder<DestinationImage> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        
        builder.Property(p => p.ThumbnailUrls).HasColumnType("jsonb");
        
        builder.HasOne(p => p.FileRecord)
            .WithMany()
            .HasForeignKey(p => p.FileRecordId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}