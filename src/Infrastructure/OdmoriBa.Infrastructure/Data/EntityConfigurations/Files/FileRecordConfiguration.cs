using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Files;

public sealed class FileRecordEntityConfiguration : IEntityTypeConfiguration<FileRecord>
{
    public void Configure(EntityTypeBuilder<FileRecord> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();

        builder.Property(p => p.Name).IsRequired();
        builder.Property(p => p.Url).IsRequired();
        builder.Property(p => p.ContentType).IsRequired();
        builder.Property(p => p.Size).IsRequired();
        builder.Property(p => p.Type).IsRequired();
    }
}