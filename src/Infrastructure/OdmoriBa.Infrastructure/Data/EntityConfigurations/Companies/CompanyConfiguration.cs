using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Infrastructure.Data.Converters;

namespace OdmoriBa.Infrastructure.Data.EntityConfigurations.Companies;

internal sealed class CompanyConfiguration : IEntityTypeConfiguration<Company>
{
    public void Configure(EntityTypeBuilder<Company> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        
        builder.Property(p => p.Name).HasMaxLength(100).IsRequired();
        builder.Property(p => p.Address).HasMaxLength(100);
        builder.Property(p => p.ContactPhone)
            .HasConversion<PhoneConverter>()
            .HasMaxLength(20);
        builder.Property(p => p.ContactEmail).HasMaxLength(100);
    }
}