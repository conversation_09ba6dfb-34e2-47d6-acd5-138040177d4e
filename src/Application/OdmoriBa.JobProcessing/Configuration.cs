using Microsoft.Extensions.DependencyInjection;
using OdmoriBa.JobProcessing.Jobs;
using Quartz;

namespace OdmoriBa.JobProcessing;

public static class Configuration
{
    public static IServiceCollection AddJobProcessing(this IServiceCollection services)
    {
        return services.AddQuartz(configure =>
        {
            configure
                .AddJob<PushNotificationsJob>(JobKeys.PushNotificationsJobKey)
                .AddTrigger(t =>
                    t.ForJob(JobKeys.PushNotificationsJobKey)
                        .WithSimpleSchedule(s => s.WithIntervalInSeconds(60).RepeatForever())
                );
        }).AddQuartzHostedService(o => { o.WaitForJobsToComplete = true; });
    }
}