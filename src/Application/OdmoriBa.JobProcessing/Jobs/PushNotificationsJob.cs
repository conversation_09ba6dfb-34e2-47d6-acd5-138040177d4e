using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using OdmoriBa.Application.Clients.Firebase;
using OdmoriBa.Application.Interfaces.Data;
using OdmoriBa.Core.Domains.Outbox.Entities;
using Quartz;

namespace OdmoriBa.JobProcessing.Jobs;

[DisallowConcurrentExecution]
public sealed class PushNotificationsJob(
    ILogger<PushNotificationsJob> logger,
    IAppDbContext dbContext,
    IFirebaseClient firebaseClient) : IJob
{
    public async Task Execute(IJobExecutionContext context)
    {
        var pendingNotifications = await dbContext.PushNotificationOutbox
            .Where(n => n.Status == OutboxStatus.Pending)
            .OrderBy(n => n.CreatedAt)
            .Take(50)
            .ToListAsync();

        if (pendingNotifications.Count == 0) return;

        logger.LogInformation("PushNotificationsJob | Found {count} pending push notifications to process",
            pendingNotifications.Count);

        foreach (var notification in pendingNotifications)
        {
            try
            {
                var result = await firebaseClient.SendPushNotificationAsync(
                    notification.Token,
                    notification.Topic,
                    notification.Title ?? string.Empty,
                    notification.Body ?? string.Empty,
                    notification.AdditionalData,
                    notification.DeviceType!.Value);

                if (result.IsOk)
                {
                    notification.MarkAsSent(result.Value);
                    logger.LogInformation("Successfully sent push notification {id}", notification.Id);
                }
                else
                {
                    notification.IncrementRetryCount();

                    if (notification.RetryCount >= notification.MaxRetryCount)
                    {
                        var error = result.Error;
                        notification.MarkAsFailed(error.Code, error.Description);

                        logger.LogError("Failed to send push notification {id}: {code} - {error}",
                            notification.Id, error.Code, error.Description);
                    }
                }
            }
            catch (Exception ex)
            {
                notification.MarkAsFailed("InternalServerError", ex.Message);
                logger.LogError(ex, "Error processing push notification {id}", notification.Id);
            }

            await dbContext.SaveChangesAsync(context.CancellationToken);
        }

        logger.LogInformation("Completed processing push notification outbox");
    }
}