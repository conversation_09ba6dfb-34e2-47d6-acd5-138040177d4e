using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OdmoriBa.Application.Clients.Azure;
using OdmoriBa.Application.Clients.Firebase;
using OdmoriBa.Application.Common.Mediator;
using OdmoriBa.Application.Common.Settings;

namespace OdmoriBa.Application;

public static class Configuration
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton(TimeProvider.System);
        
        services.AddSingleton<IAzureBlobStorageClient>(_ =>
            new AzureBlobStorageClient(new BlobServiceClient(configuration.GetConnectionString("AzureStorage"))));
        
        AzureStorageSettings.Instance.Initialize(configuration);
        
        return services
            .AddCache()
            .AddClients()
            .AddMediator();
    }

    private static IServiceCollection AddClients(this IServiceCollection services)
    {
        services.AddScoped<IFirebaseClient, FirebaseClient>();
        return services;
    }

    private static IServiceCollection AddCache(this IServiceCollection services)
    {
        return services.AddMemoryCache();
    }

    private static IServiceCollection AddMediator(this IServiceCollection services)
    {
        services.AddMediator(options =>
        {
            options.PipelineBehaviors = [typeof(LoggingBehavior<,>)];
            options.ServiceLifetime = ServiceLifetime.Transient;
        });

        return services;
    }
}