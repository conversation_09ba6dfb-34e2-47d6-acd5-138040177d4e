using Azure.Storage.Blobs;

namespace OdmoriBa.Application.Clients.Azure;

public interface IAzureBlobStorageClient
{
    Task<Result> UploadFileAsync(string containerName, string fileName, Stream fileStream,
        CancellationToken cancellationToken = default);
}

public sealed class AzureBlobStorageClient(BlobServiceClient blobClient) : IAzureBlobStorageClient
{
    public async Task<Result> UploadFileAsync(string containerName, string fileName, Stream fileStream,
        CancellationToken cancellationToken = default)
    {
        var response = await blobClient.GetBlobContainerClient(containerName)
            .GetBlobClient(fileName)
            .UploadAsync(fileStream, true, cancellationToken);

        if (!response.HasValue)
        {
            return Error.Failure("BlobStorage.UploadFailed", "The file could not be uploaded.");
        }

        return Result.Ok();
    }
}