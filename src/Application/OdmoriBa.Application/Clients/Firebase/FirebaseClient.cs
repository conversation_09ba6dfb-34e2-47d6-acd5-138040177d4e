using FirebaseAdmin.Auth;
using FirebaseAdmin.Messaging;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Clients.Firebase;

public interface IFirebaseClient
{
    Task SetCustomUserClaimsAsync(string uid, Dictionary<string, object> claims, CancellationToken cancellationToken);
    Task UpdateUserAsync(string uid, string displayName, CancellationToken cancellationToken);
    Task UpdateUserEmailAsync(string uid, string email, CancellationToken cancellationToken);
    Task UpdateUserPhoneNumberAsync(string uid, string phoneNumber, CancellationToken cancellationToken);
    Task DeleteUserAsync(string uid, CancellationToken cancellationToken);

    Task<Result<string>> SendPushNotificationAsync(string? deviceToken, string? topic,
        string title, string body,
        Dictionary<string, string>? additionalData, DeviceType deviceType);
}

public sealed class FirebaseClient(ILogger<FirebaseClient> logger) : IFirebaseClient
{
    public async Task SetCustomUserClaimsAsync(string uid, Dictionary<string, object> claims,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Firebase | Set custom claims to user {Uid}. Claims: {@claims}", uid, claims);
        await FirebaseAuth.DefaultInstance.SetCustomUserClaimsAsync(uid, claims, cancellationToken);
    }

    public async Task UpdateUserAsync(string uid, string displayName, CancellationToken cancellationToken)
    {
        logger.LogInformation("Firebase | Update user {Uid}", uid);
        await FirebaseAuth.DefaultInstance.UpdateUserAsync(new UserRecordArgs
        {
            Uid = uid,
            DisplayName = displayName,
        }, cancellationToken);
    }
    
    public async Task UpdateUserEmailAsync(string uid, string email, CancellationToken cancellationToken)
    {
        logger.LogInformation("Firebase | Update user email {Uid}", uid);
        await FirebaseAuth.DefaultInstance.UpdateUserAsync(new UserRecordArgs
        {
            Uid = uid,
            Email = email,
            EmailVerified = false
        }, cancellationToken);
    }
    
    public async Task UpdateUserPhoneNumberAsync(string uid, string phoneNumber, CancellationToken cancellationToken)
    {
        logger.LogInformation("Firebase | Update user phone number {Uid}", uid);
        await FirebaseAuth.DefaultInstance.UpdateUserAsync(new UserRecordArgs
        {
            Uid = uid,
            PhoneNumber = phoneNumber
        }, cancellationToken);
    }

    public async Task DeleteUserAsync(string uid, CancellationToken cancellationToken)
    {
        logger.LogInformation("Firebase | Delete user {Uid}", uid);
        try
        {
            await FirebaseAuth.DefaultInstance.DeleteUserAsync(uid, cancellationToken);
        }
        catch (FirebaseAuthException ex)
        {
            if (ex.AuthErrorCode == AuthErrorCode.UserNotFound)
            {
                logger.LogWarning("Firebase | Delete user {Uid} failed: User not found!", uid);
            }
            else
            {
                throw;
            }
        }
    }

    public async Task<Result<string>> SendPushNotificationAsync(string? deviceToken, string? topic,
        string title, string body,
        Dictionary<string, string>? additionalData, DeviceType deviceType)
    {
        var message = new Message
        {
            Token = deviceToken,
            Topic = topic,
            Notification = new Notification
            {
                Title = title,
                Body = body
            },
            Data = additionalData,
        };

        if (deviceType == DeviceType.Ios)
        {
            message.Apns = new ApnsConfig
            {
                Aps = new Aps
                {
                    Alert = new ApsAlert
                    {
                        Title = title,
                        Body = body,
                    },
                },
                CustomData = additionalData?.ToDictionary(pair => pair.Key, object (pair) => pair.Value)
            };
        }

        try
        {
            return await FirebaseMessaging.DefaultInstance.SendAsync(message);
        }
        catch (FirebaseMessagingException e)
        {
            logger.LogError(e, "Firebase Client | Error on send message: {message}", e.Message);
            return Error.Failure(e.MessagingErrorCode.ToString()!, e.Message);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unknown error on sending push notification to firebase");
            return Error.Failure("UnknownError", e.Message);
        }
    }
}