using Destructurama.Attributed;
using OdmoriBa.Application.Clients.Azure;
using OdmoriBa.Application.Features.Files.Helpers;
using OdmoriBa.Application.Features.Files.Mappers;
using OdmoriBa.Application.Features.Files.Models;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Files.Commands;

public sealed record CreateFileRecordCommand(
    FileRecordType Type,
    string Name,
    string Url,
    [property: LogMasked] Stream FileStream,
    string ContentType,
    long Size
) : ICommand<Result<FileRecordDto>>;

internal sealed class CreateFileRecordCommandHandler(
    IAppDbContext dbContext,
    IAzureBlobStorageClient azureBlobStorageClient)
    : ICommandHandler<CreateFileRecordCommand, Result<FileRecordDto>>
{
    public async ValueTask<Result<FileRecordDto>> Handle(CreateFileRecordCommand request,
        CancellationToken cancellationToken)
    {
        var containerName = StorageHelper.GetContainerName(request.Type);
        
        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        var result = FileRecord.Create(request.Name, request.Url, request.ContentType, request.Size, request.Type);
        
        if (result.IsError) return result.Error;

        dbContext.FileRecords.Add(result.Value);
        await dbContext.SaveChangesAsync(cancellationToken);

        var uploadResult = await azureBlobStorageClient.UploadFileAsync(containerName, result.Value.Url,
            request.FileStream,
            cancellationToken);

        if (uploadResult.IsError)
        {
            return uploadResult.Error;
        }

        await transaction.CommitAsync(cancellationToken);
        return result.Value.ToDto()!;
    }
}