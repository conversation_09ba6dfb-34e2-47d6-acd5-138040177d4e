using System.ComponentModel;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Files.Helpers;

public static class StorageHelper
{
    public static string GetContainerName(FileRecordType type) => type switch
    {
        FileRecordType.DestinationImage or FileRecordType.DestinationCover => "public",
        _ => throw new InvalidEnumArgumentException($"Invalid file record type: {type}"),
    };
}