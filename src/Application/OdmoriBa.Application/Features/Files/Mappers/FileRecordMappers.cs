using OdmoriBa.Application.Features.Files.Models;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Files.Mappers;

public static class FileRecordMappers
{
    public static FileRecordDto? ToDto(this FileRecord? entity) =>
        entity is null
            ? null
            : new FileRecordDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Type = entity.Type,
                ContentType = entity.ContentType,
                Url = entity.Url,
                Size = entity.Size
            };
}