using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Mappers;

public static class PersonMappers
{
    public static PersonDto? ToDto(this Person? entity, bool includeUser = true) =>
        entity is null
            ? null
            : new PersonDto
            {
                Id = entity.Id,
                FirstName = entity.FirstName,
                LastName = entity.LastName,
                Email = entity.Email,
                Phone = entity.Phone,
                Address = entity.Address,
                City = entity.City,
                IdDocument = entity.IdDocument,
                BirthDate = entity.BirthDate.ToDateTime(TimeOnly.MinValue),
                CountryCode = entity.CountryCode,
                User = includeUser ? entity.User.ToDto() : null,
            };
    
    public static LoyaltyCardDto? ToDto(this LoyaltyCard? entity) =>
        entity is null
            ? null
            : new LoyaltyCardDto
            {
                TotalPoints = entity.TotalPoints,
                PointTransactions = entity.PointTransactions.Select(x => x.ToDto()).ToList()
            };

    public static LoyaltyPointTransactionDto ToDto(this LoyaltyPointTransaction entity) =>
        new ()
        {
            Value = entity.Value,
            Type = entity.Type,
            TripDestinationId = entity.TripDestinationId,
            TripDestination = entity.TripDestination?.ToDto()
        };
}