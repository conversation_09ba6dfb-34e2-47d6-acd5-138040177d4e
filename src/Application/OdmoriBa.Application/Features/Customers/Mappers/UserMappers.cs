using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Mappers;

public static class UserMappers
{
    public static UserDto? ToDto(this User? entity, bool includePerson = false)
        => entity is null
            ? null
            : new UserDto
            {
                Id = entity.Id,
                IdentityId = entity.IdentityId,
                Email = entity.Email,
                Phone = entity.Phone,
                Role = entity.Role,
                Status = entity.Status,
                CreatedAt = entity.CreatedAt,
                SignInProvider = entity.SignInProvider,
                Person = includePerson ? entity.Person?.ToDto(false) : null,
            };
}