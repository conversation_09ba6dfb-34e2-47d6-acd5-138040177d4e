using OdmoriBa.Core.Domains.Outbox.Events;

namespace OdmoriBa.Application.Features.Customers.EventHandlers;

public sealed class UnregisterDeviceBindingEventHandler(
    ILogger<UnregisterDeviceBindingEventHandler> logger,
    IAppDbContext dbContext)
    : IDomainEventHandler<PushNotificationFailedDomainEvent>
{
    public async ValueTask Handle(PushNotificationFailedDomainEvent notification, CancellationToken cancellationToken)
    {
        if (notification.ErrorCode != "Unregistered") return;

        var device = await dbContext.DeviceBindings.FirstOrDefaultAsync(
            s => s.UserId == notification.UserId && s.PushNotificationToken == notification.PushNotificationToken,
            cancellationToken);
        if (device is not null)
        {
            logger.LogInformation("Device {DeviceId} unregistered for user: {UserId}", device.DeviceId, notification.UserId);
            dbContext.DeviceBindings.Remove(device);
            await dbContext.SaveChangesAsync(cancellationToken);
        }
    }
}