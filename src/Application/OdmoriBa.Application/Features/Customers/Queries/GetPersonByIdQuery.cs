using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Queries;

public sealed record GetPersonByIdQuery(Guid Id) : IQuery<Result<PersonDto>>;

internal sealed class GetPersonByIdQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetPersonByIdQuery, Result<PersonDto>>
{
    public async ValueTask<Result<PersonDto>> Handle(GetPersonByIdQuery request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Persons
            .Include(s => s.User)
            .FirstOrDefaultAsync(p => p.Id == request.Id, cancellationToken);

        if (entity == null) return PersonErrors.PersonNotFound(request.Id);

        return entity.ToDto()!;
    }
}