using OdmoriBa.Application.Common.Enums;
using OdmoriBa.Application.Features.Customers.Enums;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Queries;

public sealed record GetPersonsQuery
    : PaginatedFilterQuery<PersonSort>, IQuery<Result<PaginatedResultDto<PersonDto>>>;

internal sealed class GetPersonQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetPersonsQuery, Result<PaginatedResultDto<PersonDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<PersonDto>>> Handle(GetPersonsQuery request,
        CancellationToken cancellationToken)
    {
        var query = dbContext.Persons
            .Include(s => s.User)
            .AsNoTracking()
            .Where(x =>
                (string.IsNullOrEmpty(request.SearchTerm) ||
                 EF.Functions.ILike(x.FirstName, $"%{request.SearchTerm.ToLower()}%") ||
                 EF.Functions.ILike(x.LastName, $"%{request.SearchTerm.ToLower()}%") ||
                 EF.Functions.ILike(x.Email!, $"%{request.SearchTerm.ToLower()}%") ||
                 EF.Functions.ILike(x.Phone!, $"%{request.SearchTerm.ToLower()}%") ||
                 EF.Functions.ILike(x.IdDocument!, $"%{request.SearchTerm.ToLower()}%"))
            );

        var totalCount = await query.CountAsync(cancellationToken);

        query = SortBy(query, request.SortBy, request.SortDirection);

        var items = await query
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => s.ToDto(true)!)
            .ToListAsync(cancellationToken);

        return new PaginatedResultDto<PersonDto>(request.Page, request.PageSize, totalCount, items);
    }

    private static IQueryable<Person> SortBy(IQueryable<Person> query,
        PersonSort? sortBy,
        SortDirection orderDirection)
    {
        sortBy ??= PersonSort.CreatedAt;

        return sortBy switch
        {
            PersonSort.FirstName => query.ApplySort(s => s.FirstName, orderDirection),
            PersonSort.LastName => query.ApplySort(s => s.LastName, orderDirection),
            PersonSort.CreatedAt => query.ApplySort(s => s.CreatedAt, orderDirection),
            _ => query.ApplySort(s => s.CreatedAt, orderDirection)
        };
    }
}