using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Queries;

public sealed record GetLoyaltyCardQuery(Guid PersonId) : IQuery<Result<LoyaltyCardDto>>;

internal sealed class GetLoyaltyCardQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetLoyaltyCardQuery, Result<LoyaltyCardDto>>
{
    public async ValueTask<Result<LoyaltyCardDto>> Handle(GetLoyaltyCardQuery query,
        CancellationToken cancellationToken)
    {
        var entity = await dbContext.LoyaltyCards
            .Include(s => s.PointTransactions)
            .ThenInclude(s => s.TripDestination)
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.Id == query.PersonId, cancellationToken);

        if (entity is null) return LoyaltyCardErrors.NotFound(query.PersonId);

        return entity.ToDto()!;
    }
}