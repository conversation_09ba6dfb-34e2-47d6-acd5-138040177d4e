using OdmoriBa.Application.Common.Attributes;

namespace OdmoriBa.Application.Features.Customers.Models;

public sealed class PersonDto
{
    public Guid Id { get; set; }
    [MaskName]
    public string FirstName { get; set; } = null!;
    [MaskName]
    public string LastName { get; set; } = null!;
    [MaskDate]
    public DateTime BirthDate { get; set; }
    [MaskEmail]
    public string? Email { get; set; }
    [MaskPhone]
    public Phone? Phone { get; set; }
    [MaskIdDocument]
    public string? IdDocument { get; set; }
    public string? CountryCode { get; set; }
    public string? City { get; set; }
    public string? Address { get; set; }
    public UserDto? User { get; set; }

    public string FullName => $"{FirstName} {LastName}";
    public string FullNameAndCity => $"{FirstName} {LastName} | {City}";
}