using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Models;

public sealed class UserDto
{
    public Guid Id { get; set; }
    public UserRole Role { get; set; }
    public UserStatus Status { get; set; }
    public string? Email { get; set; }
    public Phone? Phone { get; set; }
    public string? IdentityId { get; set; }
    public PersonDto? Person { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public string? SignInProvider { get; set; }
    public bool EmailVerified { get; set; }
}