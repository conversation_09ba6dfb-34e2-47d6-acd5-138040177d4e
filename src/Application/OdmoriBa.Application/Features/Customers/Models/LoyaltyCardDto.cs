using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Models;

public sealed class LoyaltyCardDto
{
    public int TotalPoints { get; set; }
    public List<LoyaltyPointTransactionDto> PointTransactions { get; set; } = [];
}

public sealed class LoyaltyPointTransactionDto
{
    public int Value { get; set; }
    public LoyaltyPointTransactionType Type { get; set; }

    public Guid? TripDestinationId { get; set; }
    public TripDestinationDto? TripDestination { get; set; }
}