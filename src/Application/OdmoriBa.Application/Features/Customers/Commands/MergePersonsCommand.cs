using OdmoriBa.Application.Clients.Firebase;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record MergePersonsCommand(Guid SourcePersonId, Guid TargetPersonId) : ICommand<Result>;

internal sealed class MergePersonsCommandHandler(IAppDbContext dbContext, IFirebaseClient firebaseClient)
    : ICommandHandler<MergePersonsCommand, Result>
{
    public async ValueTask<Result> Handle(MergePersonsCommand request, CancellationToken cancellationToken)
    {
        var sourcePerson = await dbContext.Persons.FindAsync([request.SourcePersonId], cancellationToken);

        if (sourcePerson == null) return PersonErrors.PersonNotFound(request.SourcePersonId);

        var targetPerson = await dbContext.Persons.FindAsync([request.TargetPersonId], cancellationToken);

        if (targetPerson == null) return PersonErrors.PersonNotFound(request.TargetPersonId);

        var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        // Transfer all travelers to target person
        await dbContext.Travelers
            .Where(s => s.PersonId == request.SourcePersonId)
            .ExecuteUpdateAsync(s =>
                s.SetProperty(p => p.PersonId, request.TargetPersonId), cancellationToken);

        // Transfer all main contacts
        await dbContext.TravelParties
            .Where(s => s.MainContactId == request.SourcePersonId)
            .ExecuteUpdateAsync(s =>
                s.SetProperty(p => p.MainContactId, request.TargetPersonId), cancellationToken);

        // Transfer loyalty card
        await dbContext.LoyaltyPointTransactions
            .Where(s => s.Id == request.SourcePersonId)
            .ExecuteUpdateAsync(s =>
                s.SetProperty(p => p.Id, request.TargetPersonId), cancellationToken);
        await dbContext.LoyaltyCards
            .Where(s => s.Id == request.TargetPersonId)
            .ExecuteUpdateAsync(s =>
                s.SetProperty(p => p.TotalPoints,
                    sourcePerson.LoyaltyCard.TotalPoints + targetPerson.LoyaltyCard.TotalPoints), cancellationToken);

        // Delete source person
        await dbContext.Persons
            .Where(s => s.Id == request.SourcePersonId)
            .ExecuteDeleteAsync(cancellationToken);

        // Delete user data
        if (sourcePerson.User != null)
        {
            await dbContext.Users
                .Where(s => s.Id == request.SourcePersonId)
                .ExecuteDeleteAsync(cancellationToken);

            await firebaseClient.DeleteUserAsync(sourcePerson.User.IdentityId, cancellationToken);
        }

        await transaction.CommitAsync(cancellationToken);

        return Result.Ok();
    }
}