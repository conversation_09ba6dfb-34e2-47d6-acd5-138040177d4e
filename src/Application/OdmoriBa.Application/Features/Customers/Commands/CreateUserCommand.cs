using OdmoriBa.Application.Clients.Firebase;
using OdmoriBa.Application.Common.Attributes;
using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record CreateUserCommand(
    string IdentityId,
    [property: MaskName] string FirstName,
    [property: MaskName] string LastName,
    [property: MaskEmail] string Email,
    [property: MaskPhone] Phone Phone,
    [property: MaskIdDocument] string? IdDocument,
    DateOnly BirthDate,
    string? CountryCode,
    string? City,
    string? Address,
    string? SignInProvider) : ICommand<Result<UserDto>>;

internal sealed class CreateUserCommandHandler(IAppDbContext dbContext, IFirebaseClient firebaseClient)
    : ICommandHandler<CreateUserCommand, Result<UserDto>>
{
    public async ValueTask<Result<UserDto>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Users
            .Include(u => u.Person)
            .FirstOrDefaultAsync(s => s.IdentityId == request.IdentityId, cancellationToken);

        if (entity is not null) return entity.ToDto(true)!;

        var result = User.CreateWithPerson(request.IdentityId,
            UserRole.User,
            request.SignInProvider,
            request.FirstName,
            request.LastName,
            request.Email,
            request.Phone,
            request.BirthDate,
            request.IdDocument,
            request.CountryCode,
            request.City,
            request.Address
            );

        if (result.IsError) return result.Error;

        var user = result.Value;

        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        dbContext.Users.Add(result.Value);
        await dbContext.SaveChangesAsync(cancellationToken);

        await firebaseClient.SetCustomUserClaimsAsync(request.IdentityId, new Dictionary<string, object>
        {
            { CustomClaimTypes.SysId, user.Id.ToString() },
            { CustomClaimTypes.Status, user.Status.ToString() },
            { CustomClaimTypes.Role, user.Role.ToString() },
        }, cancellationToken);

        await firebaseClient.UpdateUserAsync(request.IdentityId,
            $"{user.Person?.FirstName} {user.Person?.LastName}", cancellationToken);

        await transaction.CommitAsync(cancellationToken);

        return user.ToDto(true)!;
    }
}