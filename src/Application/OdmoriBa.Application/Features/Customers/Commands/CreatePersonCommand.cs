using OdmoriBa.Application.Common.Attributes;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record CreatePersonCommand(
    [property: MaskName] string FirstName,
    [property: MaskName] string LastName,
    [property: MaskEmail] string? Email,
    [property: MaskPhone] Phone? Phone,
    [property: MaskIdDocument] string? IdDocument,
    [property: MaskDate] DateOnly BirthDate,
    string? CountryCode,
    string? City,
    string? Address
) : ICommand<Result<PersonDto>>;

internal sealed class CreatePersonCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreatePersonCommand, Result<PersonDto>>
{
    public async ValueTask<Result<PersonDto>> Handle(CreatePersonCommand request, CancellationToken cancellationToken)
    {
        var result = Person.Create(request.FirstName, request.LastName, request.Email, request.Phone,
            request.BirthDate, request.IdDocument, request.CountryCode, request.City, request.Address);

        if (result.IsError) return result.Error;

        dbContext.Persons.Add(result.Value);

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}