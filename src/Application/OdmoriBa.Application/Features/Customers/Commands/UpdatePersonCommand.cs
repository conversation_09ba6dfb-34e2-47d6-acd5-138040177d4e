using OdmoriBa.Application.Common.Attributes;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record UpdatePersonCommand(
    Guid Id,
    [property: MaskName] string FirstName,
    [property: MaskName] string LastName,
    [property: MaskEmail] string? Email,
    [property: MaskPhone] Phone? Phone,
    [property: MaskIdDocument] string? IdDocument,
    [property: MaskDate] DateOnly BirthDate,
    string? CountryCode,
    string? City,
    string? Address
) : ICommand<Result<PersonDto>>;

internal sealed class UpdatePersonCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdatePersonCommand, Result<PersonDto>>
{
    public async ValueTask<Result<PersonDto>> Handle(UpdatePersonCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Persons
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (entity is null) return PersonErrors.PersonNotFound(request.Id);

        entity.Update(request.FirstName, request.LastName, request.Email, request.Phone, request.BirthDate,
            request.IdDocument, request.CountryCode, request.City, request.Address); 

        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.ToDto()!;
    }
}