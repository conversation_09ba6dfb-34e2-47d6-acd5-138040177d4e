using OdmoriBa.Application.Clients.Firebase;
using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record UpdateUserCommand(
    Guid Id,
    string? Email,
    Phone? Phone) : ICommand<Result<UserDto>>;

internal sealed class UpdateUserCommandHandler(IAppDbContext dbContext, IFirebaseClient firebaseClient)
    : ICommandHandler<UpdateUserCommand, Result<UserDto>>
{
    public async ValueTask<Result<UserDto>> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Users
            .Include(s => s.Person)
            .FirstOrDefaultAsync(s => s.Id == request.Id,
                cancellationToken);

        if (entity is null) return UserErrors.UserNotFound(request.Id);

        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            if (await dbContext.Users.AnyAsync(s => s.Email == request.Email, cancellationToken))
                return UserErrors.EmailExists;

            var result = entity.UpdateEmail(request.Email);
            if (result.IsError) return result.Error;
        }

        if (!string.IsNullOrWhiteSpace(request.Phone))
        {
            if (await dbContext.Users.AnyAsync(s => s.Phone == request.Phone, cancellationToken))
                return UserErrors.PhoneExists;

            var result = entity.UpdatePhone(request.Phone);
            if (result.IsError) return result.Error;
        }

        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        await dbContext.SaveChangesAsync(cancellationToken);

        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            await firebaseClient.UpdateUserEmailAsync(entity.IdentityId, request.Email, cancellationToken);
        }

        if (!string.IsNullOrWhiteSpace(request.Phone))
        {
            await firebaseClient.UpdateUserPhoneNumberAsync(entity.IdentityId, request.Phone!, cancellationToken);
        }

        await transaction.CommitAsync(cancellationToken);

        return entity.ToDto()!;
    }
}