using Destructurama.Attributed;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Application.Features.Customers.Commands;

public sealed record SaveDeviceCommand(
    Guid UserId,
    [property: LogMasked(ShowLast = 3)] string DeviceId,
    DeviceType DeviceType,
    string AppVersion,
    string OsVersion,
    string DeviceModel,
    [property: LogMasked(ShowLast = 10)] string PushNotificationToken) : ICommand<Result>;

internal sealed class SaveDeviceCommandHandler(IAppDbContext dbContext) : ICommandHandler<SaveDeviceCommand, Result>
{
    public async ValueTask<Result> Handle(SaveDeviceCommand request, CancellationToken cancellationToken)
    {
        var user = await dbContext.Users
            .Include(s => s.Devices)
            .FirstOrDefaultAsync(s => s.Id == request.UserId, cancellationToken);

        if (user is null) return UserErrors.UserNotFound(request.UserId);

        user.AddOrUpdateDeviceBinding(request.DeviceId, request.DeviceType, request.AppVersion, request.OsVersion,
            request.DeviceModel, request.PushNotificationToken);

        await dbContext.SaveChangesAsync(cancellationToken);
        return Result.Ok();
    }
}