namespace OdmoriBa.Application.Features.Destinations.Models;

public sealed class StopDto
{
    public Guid Id { get; set; }
    public string? Address { get; set; }
    public Guid CityId { get; set; }
    public CityDto? City { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }
    public string? Description { get; set; }

    public string FullName => $"{City?.Name} - {Address}";
}