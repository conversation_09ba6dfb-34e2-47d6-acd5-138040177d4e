using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;

namespace OdmoriBa.Application.Features.Destinations.Queries;

public sealed record GetStopsQuery : IQuery<Result<ListResultDto<StopDto>>>;

internal sealed class GetStopsQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetStopsQuery, Result<ListResultDto<StopDto>>>
{
    public async ValueTask<Result<ListResultDto<StopDto>>> Handle(GetStopsQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Stops
            .AsNoTracking()
            .Select(s => s.ToDto(null)!).ToListAsync(cancellationToken);

        return new ListResultDto<StopDto>(items);
    }
}