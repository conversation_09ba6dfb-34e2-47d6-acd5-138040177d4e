using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;

namespace OdmoriBa.Application.Features.Destinations.Queries;

public sealed record GetDestinationsQuery : IQuery<Result<ListResultDto<DestinationDto>>>;

internal sealed class GetDestinationsQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetDestinationsQuery, Result<ListResultDto<DestinationDto>>>
{
    public async ValueTask<Result<ListResultDto<DestinationDto>>> Handle(GetDestinationsQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Destinations
            .AsNoTracking()
            .Select(s => s.ToDto(null)!)
            .ToListAsync(cancellationToken);

        return new ListResultDto<DestinationDto>(items);
    }
}