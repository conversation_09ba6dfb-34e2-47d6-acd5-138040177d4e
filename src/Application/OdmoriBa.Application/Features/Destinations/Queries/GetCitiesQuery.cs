using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;

namespace OdmoriBa.Application.Features.Destinations.Queries;

public sealed record GetCitiesQuery : IQuery<Result<ListResultDto<CityDto>>>;

internal sealed class GetCitiesQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetCitiesQuery, Result<ListResultDto<CityDto>>>
{
    public async ValueTask<Result<ListResultDto<CityDto>>> Handle(GetCitiesQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Cities
            .AsNoTracking()
            .Select(s => s.ToDto()!).ToListAsync(cancellationToken);

        return new ListResultDto<CityDto>(items);
    }
}