using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Queries;

public sealed record GetDestinationByIdQuery(Guid Id) : IQuery<Result<DestinationDto>>;

internal sealed class GetDestinationByIdQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetDestinationByIdQuery, Result<DestinationDto>>
{
    public async ValueTask<Result<DestinationDto>> <PERSON>le(GetDestinationByIdQuery request,
        CancellationToken cancellationToken)
    {
        var destination = await dbContext.Destinations
            .AsNoTracking()    
            .Include(s => s.Images)
            .ThenInclude(s => s.FileRecord)
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (destination == null) return DestinationErrors.DestinationNotFound(request.Id);

        return destination.ToDto()!;
    }
}