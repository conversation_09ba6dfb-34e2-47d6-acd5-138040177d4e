using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record CreateDestinationCommand(string Name) : ICommand<Result<DestinationDto>>;

internal sealed class CreateDestinationCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateDestinationCommand, Result<DestinationDto>>
{
    public async ValueTask<Result<DestinationDto>> Handle(CreateDestinationCommand request,
        CancellationToken cancellationToken)
    {
        var result = Destination.Create(request.Name);

        if (result.IsError) return result.Error;

        dbContext.Destinations.Add(result.Value);

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}