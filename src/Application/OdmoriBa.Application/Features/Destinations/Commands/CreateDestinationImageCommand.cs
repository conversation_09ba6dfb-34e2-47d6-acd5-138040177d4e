using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record CreateDestinationImageCommand(
    Guid DestinationId,
    Guid FileRecordId,
    string? Title,
    string? Description)
    : ICommand<Result<DestinationImageDto>>;

internal sealed class CreateDestinationImageCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateDestinationImageCommand, Result<DestinationImageDto>>
{
    public async ValueTask<Result<DestinationImageDto>> Handle(CreateDestinationImageCommand request,
        CancellationToken cancellationToken)
    {
        var fileRecord = await dbContext.FileRecords.FindAsync([request.FileRecordId], cancellationToken);

        if (fileRecord is null) return FileRecordErrors.FileRecordNotFound(request.FileRecordId);

        var destination = await dbContext.Destinations
            .Include(s => s.Images)
            .FirstOrDefaultAsync(s => s.Id == request.DestinationId, cancellationToken);

        if (destination is null) return DestinationErrors.DestinationNotFound(request.DestinationId);

        var result = destination.AddImage(request.Title, request.Description, request.FileRecordId);
        
        if (result.IsError) return result.Error;
        
        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}