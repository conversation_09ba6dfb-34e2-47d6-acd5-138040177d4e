using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record UpdateDestinationImageCommand(
    Guid DestinationId,
    Guid DestinationImageId,
    string? Title,
    string? Description)
    : ICommand<Result<DestinationImageDto>>;

internal sealed class UpdateDestinationImageCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateDestinationImageCommand, Result<DestinationImageDto>>
{
    public async ValueTask<Result<DestinationImageDto>> Handle(UpdateDestinationImageCommand request,
        CancellationToken cancellationToken)
    {
        var destination = await dbContext.Destinations
            .Include(s => s.Images)
            .FirstOrDefaultAsync(s => s.Id == request.DestinationId, cancellationToken);

        if (destination is null) return DestinationErrors.DestinationNotFound(request.DestinationId);

        var result = destination.UpdateImage(request.DestinationImageId, request.Title, request.Description);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}