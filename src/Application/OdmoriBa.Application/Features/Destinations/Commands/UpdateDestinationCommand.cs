using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record UpdateDestinationCommand(Guid Id, string Name, Guid? CoverId) : ICommand<Result<DestinationDto>>;

internal sealed class UpdateDestinationCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateDestinationCommand, Result<DestinationDto>>
{
    public async ValueTask<Result<DestinationDto>> Handle(UpdateDestinationCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await dbContext.Destinations
            .Include(s => s.Cover)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (entity is null) return DestinationErrors.DestinationNotFound(request.Id);

        entity.Update(request.Name);

        var cover = entity.Cover;

        if (entity.CoverId != request.CoverId && request.CoverId.HasValue)
        {
            cover = await dbContext.FileRecords.FindAsync([request.CoverId], cancellationToken);
            if (cover is null) return FileRecordErrors.FileRecordNotFound(request.CoverId.Value);

            entity.SetCover(request.CoverId.Value);
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.ToDto(cover)!;
    }
}