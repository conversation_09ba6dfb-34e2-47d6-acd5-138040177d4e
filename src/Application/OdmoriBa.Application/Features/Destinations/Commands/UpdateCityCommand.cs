using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record UpdateCityCommand(Guid Id, string Name, string CountryCode) : ICommand<Result<CityDto>>;

internal sealed class UpdateCityCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateCityCommand, Result<CityDto>>
{
    public async ValueTask<Result<CityDto>> Handle(UpdateCityCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Cities.FindAsync([request.Id], cancellationToken);

        if (entity is null) return CityErrors.CityNotFound(request.Id);

        var result = entity.Update(request.Name, request.CountryCode);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.ToDto()!;
    }
}