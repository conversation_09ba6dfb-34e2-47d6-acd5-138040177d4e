using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record UpdateStopCommand(
    Guid Id,
    string Address,
    Guid CityId,
    double? Longitude,
    double? Latitude,
    string? Description) : ICommand<Result<StopDto>>;

internal sealed class UpdateStopCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateStopCommand, Result<StopDto>>
{
    public async ValueTask<Result<StopDto>> Handle(UpdateStopCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Stops
            .FindAsync([request.Id], cancellationToken);

        if (entity == null) return StopErrors.StopNotFound(request.Id);

        var city = entity.City;

        if (entity.CityId != request.CityId)
        {
            city = await dbContext.Cities.FindAsync([request.CityId], cancellationToken);
            if (city == null) return CityErrors.CityNotFound(request.CityId);
        }

        entity.Update(request.Address, request.CityId, request.Longitude, request.Latitude, request.Description);

        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.ToDto(city)!;
    }
}