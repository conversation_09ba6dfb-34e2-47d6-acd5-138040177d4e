
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record RemoveDestinationImageCommand(Guid DestinationId, Guid Id) : ICommand<Result>;

internal sealed class RemoveDestinationImageCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<RemoveDestinationImageCommand, Result>
{
    public async ValueTask<Result> Handle(RemoveDestinationImageCommand request, CancellationToken cancellationToken)
    {
        var destination = await dbContext.Destinations
            .Include(s => s.Images)
            .FirstOrDefaultAsync(s => s.Id == request.DestinationId, cancellationToken);

        if (destination is null) return DestinationErrors.DestinationNotFound(request.DestinationId);

        var result = destination.RemoveImage(request.Id);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}