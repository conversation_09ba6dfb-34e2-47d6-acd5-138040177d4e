using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record CreateStopCommand(
    string Address,
    Guid CityId,
    double? Longitude,
    double? Latitude,
    string? Description) : ICommand<Result<StopDto>>;

internal sealed class CreateStopCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateStopCommand, Result<StopDto>>
{
    public async ValueTask<Result<StopDto>> Handle(CreateStopCommand request, CancellationToken cancellationToken)
    {
        var city = await dbContext.Cities.FindAsync([request.CityId], cancellationToken);
        if (city == null) return CityErrors.CityNotFound(request.CityId);

        var result = Stop.Create(request.Address, request.CityId, request.Longitude, request.Latitude,
            request.Description);

        if (result.IsError) return result.Error;

        dbContext.Stops.Add(result.Value);
        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}