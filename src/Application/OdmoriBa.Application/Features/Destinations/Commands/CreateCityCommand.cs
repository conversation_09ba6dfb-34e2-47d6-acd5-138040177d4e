using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Commands;

public sealed record CreateCityCommand(string Name, string CountryCode) : ICommand<Result<CityDto>>;

internal sealed class CreateCityCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateCityCommand, Result<CityDto>>
{
    public async ValueTask<Result<CityDto>> Handle(CreateCityCommand request, CancellationToken cancellationToken)
    {
        var result = City.Create(request.Name, request.CountryCode);

        if (result.IsError) return result.Error;

        dbContext.Cities.Add(result.Value);

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}