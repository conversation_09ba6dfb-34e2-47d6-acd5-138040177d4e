using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Mappers;

public static class StopMappers
{
    public static StopDto? ToDto(this Stop? entity, City? city = null) =>
        entity is null
            ? null
            : new StopDto
            {
                Id = entity.Id,
                Address = entity.Address,
                CityId = entity.CityId,
                City = (entity.City ?? city)?.ToDto(),
                Latitude = entity.Latitude,
                Longitude = entity.Longitude,
                Description = entity.Description,
            };
}