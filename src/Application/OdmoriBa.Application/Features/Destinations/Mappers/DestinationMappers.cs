using OdmoriBa.Application.Common.Settings;
using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Application.Features.Destinations.Mappers;

public static class DestinationMappers
{
    public static DestinationDto? ToDto(this Destination? entity, FileRecord? cover = null) => entity is null
        ? null
        : new DestinationDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Images = entity.Images.Select(s => s.ToDto()!).ToList(),
            CoverUrl = AzureStorageSettings.Instance.GetFullPublicUrl((entity.Cover ?? cover)?.Url)
        };

    public static DestinationImageDto? ToDto(this DestinationImage? entity, FileRecord? fileRecord = null) =>
        entity is null
            ? null
            : new DestinationImageDto
            {
                Id = entity.Id,
                Title = entity.Title,
                Description = entity.Description,
                Url = AzureStorageSettings.Instance.GetFullPublicUrl((entity.FileRecord ?? fileRecord)?.Url)
            };
}