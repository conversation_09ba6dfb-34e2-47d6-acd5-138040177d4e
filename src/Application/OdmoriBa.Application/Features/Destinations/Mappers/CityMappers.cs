using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Application.Features.Destinations.Mappers;

public static class CityMappers
{
    public static CityDto? ToDto(this City? entity) => entity is null
        ? null
        : new CityDto
        {
            Id = entity.Id,
            Name = entity.Name,
            CountryCode = entity.CountryCode
        };
}