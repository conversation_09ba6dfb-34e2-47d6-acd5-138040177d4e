using OdmoriBa.Application.Features.Companies.Models;
using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Application.Features.Transportations.Models;

public sealed class BusDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public int Capacity { get; set; }
    public BusStatus Status { get; set; }
    public Guid CompanyId { get; set; }
    public CompanyLightDto? Company { get; set; }
    
    public string FullName => $"{Company?.Name} - {Name} ({Capacity})";
}