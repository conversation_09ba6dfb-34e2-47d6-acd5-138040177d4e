using OdmoriBa.Application.Features.Companies.Mappers;
using OdmoriBa.Application.Features.Transportations.Models;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Application.Features.Transportations.Mappers;

public static class BusMappers
{
    public static BusDto? ToDto(this Bus? entity, Company? company = null) => entity is null
        ? null
        : new BusDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Capacity = entity.Capacity,
            Status = entity.Status,
            CompanyId = entity.CompanyId,
            Company = (company ?? entity.Company)?.ToLightDto()
        };
}