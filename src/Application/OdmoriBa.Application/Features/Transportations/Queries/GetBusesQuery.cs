using OdmoriBa.Application.Features.Transportations.Mappers;
using OdmoriBa.Application.Features.Transportations.Models;

namespace OdmoriBa.Application.Features.Transportations.Queries;

public sealed record GetBusesQuery : IQuery<Result<ListResultDto<BusDto>>>;

internal sealed class GetBusesQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetBusesQuery, Result<ListResultDto<BusDto>>>
{
    public async ValueTask<Result<ListResultDto<BusDto>>> Handle(GetBusesQuery request, CancellationToken cancellationToken)
    {
        var buses = await dbContext.Buses
            .Include(s => s.Company)
            .ToListAsync(cancellationToken);

        return new ListResultDto<BusDto>(buses.Select(s => s.ToDto()!).ToList());
    }
}