using OdmoriBa.Application.Features.Transportations.Mappers;
using OdmoriBa.Application.Features.Transportations.Models;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Application.Features.Transportations.Commands;

public sealed record CreateBusCommand(
    string Name,
    int Capacity,
    Guid CompanyId
) : ICommand<Result<BusDto>>;

internal sealed class CreateBusCommandHandler(IAppDbContext dbContext) : ICommandHandler<CreateBusCommand, Result<BusDto>>
{
    public async ValueTask<Result<BusDto>> Handle(CreateBusCommand request, CancellationToken cancellationToken)
    {
        var company = await dbContext.Companies.FindAsync([request.CompanyId], cancellationToken);

        if (company == null) return CompanyErrors.CompanyNotFound(request.CompanyId);

        var result = Bus.Create(request.Name, request.CompanyId, request.Capacity, BusStatus.Active);

        if (result.IsError) return result.Error;

        dbContext.Buses.Add(result.Value);
        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto(company)!;
    }
}