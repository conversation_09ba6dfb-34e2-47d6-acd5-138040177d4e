using OdmoriBa.Application.Features.Transportations.Mappers;
using OdmoriBa.Application.Features.Transportations.Models;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Application.Features.Transportations.Commands;

public sealed record UpdateBusCommand(
    Guid Id,
    string Name,
    Guid CompanyId,
    int Capacity,
    BusStatus Status
) : ICommand<Result<BusDto>>;

internal sealed class UpdateBusCommandHandler(IAppDbContext dbContext) : ICommandHandler<UpdateBusCommand, Result<BusDto>>
{
    public async ValueTask<Result<BusDto>> Handle(UpdateBusCommand request, CancellationToken cancellationToken)
    {
        var bus = await dbContext.Buses
            .Include(s => s.Company)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);
        if (bus is null) return BusErrors.NotFound(request.Id);

        var company = bus.Company;

        if (request.CompanyId != company!.Id)
        {
            company = await dbContext.Companies.FindAsync([request.CompanyId], cancellationToken);
            if (company is null) return CompanyErrors.CompanyNotFound(request.CompanyId);
        }

        var result = bus.Update(request.Name, request.CompanyId, request.Capacity, request.Status);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return bus.ToDto(company)!;
    }
}