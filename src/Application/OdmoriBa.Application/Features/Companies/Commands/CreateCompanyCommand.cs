using OdmoriBa.Application.Features.Companies.Mappers;
using OdmoriBa.Application.Features.Companies.Models;
using OdmoriBa.Core.Domains.Companies.Entities;

namespace OdmoriBa.Application.Features.Companies.Commands;

public sealed record CreateCompanyCommand(
    string Name,
    string? Address,
    Phone? ContactPhone,
    string? ContactEmail) : ICommand<Result<CompanyDto>>;

internal sealed class CreateCompanyCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateCompanyCommand, Result<CompanyDto>>
{
    public async ValueTask<Result<CompanyDto>> Handle(CreateCompanyCommand request, CancellationToken cancellationToken)
    {
        var result = Company.Create(request.Name, request.Address, request.ContactPhone, request.ContactEmail);

        if (result.IsError) return result.Error;

        dbContext.Companies.Add(result.Value);

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}