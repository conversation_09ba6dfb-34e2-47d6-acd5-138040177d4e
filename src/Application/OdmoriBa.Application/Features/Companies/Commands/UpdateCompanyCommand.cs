using OdmoriBa.Application.Features.Companies.Mappers;
using OdmoriBa.Application.Features.Companies.Models;
using OdmoriBa.Core.Domains.Companies.Entities;

namespace OdmoriBa.Application.Features.Companies.Commands;

public sealed record UpdateCompanyCommand(
    Guid Id,
    string Name,
    string? Address,
    Phone? ContactPhone,
    string? ContactEmail) : ICommand<Result<CompanyDto>>;

internal sealed class UpdateCompanyCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateCompanyCommand, Result<CompanyDto>>
{
    public async ValueTask<Result<CompanyDto>> Handle(UpdateCompanyCommand request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Companies.FindAsync([request.Id], cancellationToken);

        if (entity == null) return CompanyErrors.CompanyNotFound(request.Id);

        var result = entity.Update(request.Name, request.Address, request.ContactPhone, request.ContactEmail);

        if (result.IsError) return result.Error;
        
        await dbContext.SaveChangesAsync(cancellationToken);

        return entity.ToDto()!;
    }
}