using OdmoriBa.Application.Features.Companies.Models;
using OdmoriBa.Core.Domains.Companies.Entities;

namespace OdmoriBa.Application.Features.Companies.Mappers;

public static class CompanyMappers
{
    public static CompanyDto? ToDto(this Company? entity) => entity is null
        ? null
        : new CompanyDto
        {
            Id = entity.Id,
            Name = entity.Name,
            Address = entity.Address,
            ContactPhone = entity.ContactPhone,
            ContactEmail = entity.ContactEmail,
        };
    
    public static CompanyLightDto? ToLightDto(this Company? entity) => entity is null
        ? null
        : new CompanyLightDto
        {
            Id = entity.Id,
            Name = entity.Name
        };
}