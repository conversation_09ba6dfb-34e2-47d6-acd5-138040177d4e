using OdmoriBa.Application.Features.Companies.Mappers;
using OdmoriBa.Application.Features.Companies.Models;

namespace OdmoriBa.Application.Features.Companies.Queries;

public sealed record GetCompaniesQuery : IQuery<Result<ListResultDto<CompanyDto>>>;

internal sealed class GetCitiesQueryHandler(IAppDbContext dbContext)
    : IQueryHandler<GetCompaniesQuery, Result<ListResultDto<CompanyDto>>>
{
    public async ValueTask<Result<ListResultDto<CompanyDto>>> Handle(GetCompaniesQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Companies
            .AsNoTracking()
            .Select(s => s.ToDto()!).ToListAsync(cancellationToken);

        return new ListResultDto<CompanyDto>(items);
    }
}