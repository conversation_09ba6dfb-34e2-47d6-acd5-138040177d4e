using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record TravelerSeatAssignment(
    Guid TravelerId,
    Guid TravelPartyId,
    Guid? DepartureTripBusId,
    int? DepartureSeatNumber,
    Guid? ReturnTripBusId,
    int? ReturnSeatNumber);

public sealed record UpdateTravelersSeatAssignmentsCommand(
    List<TravelerSeatAssignment> Assignments) : ICommand<Result>;

internal sealed class UpdateTravelersSeatAssignmentsCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelersSeatAssignmentsCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTravelersSeatAssignmentsCommand request, CancellationToken cancellationToken)
    {
        var travelPartyIds = request.Assignments.Select(a => a.TravelPartyId).Distinct().ToList();
        
        var travelParties = await dbContext.TravelParties
            .Where(tp => travelPartyIds.Contains(tp.Id))
            .ToListAsync(cancellationToken);

        foreach (var assignment in request.Assignments)
        {
            var travelParty = travelParties.FirstOrDefault(tp => tp.Id == assignment.TravelPartyId);
            if (travelParty is null) return TripErrors.TravelPartyNotFound(assignment.TravelPartyId);

            var result = travelParty.UpdateBusAndSeat(
                assignment.TravelerId,
                assignment.DepartureTripBusId,
                assignment.DepartureSeatNumber,
                assignment.ReturnTripBusId,
                assignment.ReturnSeatNumber);

            if (result.IsError) return result.Error;
        }

        await dbContext.SaveChangesAsync(cancellationToken);
        return Result.Ok();
    }
}
