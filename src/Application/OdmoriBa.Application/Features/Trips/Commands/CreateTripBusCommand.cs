using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Transportations.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record CreateTripBusCommand(
    Guid TripId,
    Guid BusId,
    string Name,
    TripBusDirection Direction) : ICommand<Result<TripBusDto>>;

internal sealed class CreateTripBusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateTripBusCommand, Result<TripBusDto>>
{
    public async ValueTask<Result<TripBusDto>> Handle(CreateTripBusCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripBuses)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var bus = await dbContext.Buses.FindAsync([request.BusId], cancellationToken);
        if (bus is null) return BusErrors.NotFound(request.BusId);

        var tripBusResult = trip.AddBus(request.BusId, request.Name, request.Direction);
        if (tripBusResult.IsError) return tripBusResult.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return tripBusResult.Value.ToDto(bus)!;
    }
}