using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeleteTripStopCommand(Guid TripId, Guid Id) : ICommand<Result>;

internal sealed class DeleteTripStopCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeleteTripStopCommand, Result>
{
    public async ValueTask<Result> Handle(DeleteTripStopCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripStops)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var result = trip.RemoveStop(request.Id);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}