using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeleteTravelerCommand(Guid TravelPartyId, Guid Id) : ICommand<Result>;

internal sealed class DeleteTravelerCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeleteTravelerCommand, Result>
{
    public async ValueTask<Result> Handle(DeleteTravelerCommand request, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .FindAsync([request.TravelPartyId], cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        var result = travelParty.RemoveTraveler(request.Id);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}