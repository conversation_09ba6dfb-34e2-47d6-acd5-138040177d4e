using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTravelerTripBusCommand(
    Guid TravelPartyId,
    Guid Id,
    Guid? DepartureTripBusId,
    int DepartureSeatNumber,
    Guid? ReturnTripBusId,
    int ReturnSeatNumber) : ICommand<Result>;

internal sealed class UpdateTravelerTripBusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelerTripBusCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTravelerTripBusCommand request, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties.FindAsync([request.TravelPartyId], cancellationToken);
        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        var result = travelParty.UpdateBusAndSeat(request.Id, request.DepartureTripBusId, request.DepartureSeatNumber,
            request.ReturnTripBusId, request.ReturnSeatNumber);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}