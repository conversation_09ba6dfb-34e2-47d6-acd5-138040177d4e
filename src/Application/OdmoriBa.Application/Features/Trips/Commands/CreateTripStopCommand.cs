using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record CreateTripStopCommand(
    Guid TripId,
    Guid StopId,
    DateTimeOffset BeginAt,
    DateTimeOffset EndAt,
    TripStopType Type,
    string? Description
) : ICommand<Result<TripStopDto>>;

internal sealed class CreateTripStopCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateTripStopCommand, Result<TripStopDto>>
{
    public async ValueTask<Result<TripStopDto>> Handle(CreateTripStopCommand request,
        CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripStops)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var stop = await dbContext.Stops
            .FindAsync([request.StopId], cancellationToken);

        if (stop is null) return StopErrors.StopNotFound(request.StopId);

        var result = trip.AddStop(request.StopId, request.BeginAt, request.EndAt, request.Type, request.Description);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}