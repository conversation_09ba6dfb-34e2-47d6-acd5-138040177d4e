using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTripStatusCommand(Guid Id, TripStatus NewStatus) : ICommand<Result>;

internal sealed class UpdateTripStatusCommandHandler(IAppDbContext dbContext) : ICommandHandler<UpdateTripStatusCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTripStatusCommand comand, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TravelParties)
            .ThenInclude(s => s.Travelers)
            .FirstOrDefaultAsync(s => s.Id == comand.Id, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(comand.Id);
        
        var result = trip.UpdateStatus(comand.NewStatus);
        
        if (result.IsError) return result.Error;
        
        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}