using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Transportations.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTripBusCommand(
    Guid TripId,
    Guid Id,
    Guid BusId,
    string Name,
    TripBusDirection Direction) : ICommand<Result<TripBusDto>>;

internal sealed class UpdateTripBusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTripBusCommand, Result<TripBusDto>>
{
    public async ValueTask<Result<TripBusDto>> Handle(UpdateTripBusCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripBuses)
            .ThenInclude(s => s.Bus)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var tripBus = trip.TripBuses.FirstOrDefault(s => s.Id == request.Id);
        var bus = tripBus?.Bus;
        if (tripBus?.BusId != request.BusId)
        {
            bus = await dbContext.Buses.FindAsync([request.BusId], cancellationToken);
            if (bus is null) return BusErrors.NotFound(request.BusId);
        }

        var result = trip.UpdateBus(request.Id, request.BusId, request.Name, request.Direction);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto(bus)!;
    }
}