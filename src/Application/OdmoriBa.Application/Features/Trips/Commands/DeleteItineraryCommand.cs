using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeleteItineraryCommand(Guid TripDestinationId, Guid Id) : ICommand<Result>;

internal sealed class DeleteItineraryCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeleteItineraryCommand, Result>
{
    public async ValueTask<Result> Handle(DeleteItineraryCommand request, CancellationToken cancellationToken)
    {
        var tripDestination = await dbContext.TripDestinations
            .Include(s => s.Itineraries)
            .FirstOrDefaultAsync(s => s.Id == request.TripDestinationId, cancellationToken);

        if (tripDestination is null) return TripErrors.TripDestinationNotFound(request.TripDestinationId);

        tripDestination.RemoveItinerary(request.Id);

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}