using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTripCommand(
    Guid Id,
    string Title,
    string? Description,
    TripType Type,
    DateOnly StartDate,
    DateOnly EndDate,
    TransportationType TransportationType
) : ICommand<Result<TripDto>>;

internal sealed class UpdateTripCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTripCommand, Result<TripDto>>
{
    public async ValueTask<Result<TripDto>> Handle(UpdateTripCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripDestinations)
            .Include(s => s.TripStops)
            .Include(s => s.TripBuses)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.Id);

        var result = trip.Update(request.Title, request.Description, request.Type, request.StartDate, request.EndDate,
            request.TransportationType);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return trip.ToDto()!;
    }
}