using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTripDestinationCommand(
    Guid TripId,
    Guid Id,
    Guid DestinationId,
    DateOnly StartDate,
    DateOnly EndDate,
    double Price,
    double InsurancePrice,
    double TaxPrice,
    TripDestinationLoyalty Loyalty,
    bool Featured
) : ICommand<Result<TripDestinationDto>>;

internal sealed class UpdateTripDestinationCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTripDestinationCommand, Result<TripDestinationDto>>
{
    public async ValueTask<Result<TripDestinationDto>> Handle(UpdateTripDestinationCommand request,
        CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripDestinations)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var destination = await dbContext.Destinations.FindAsync([request.DestinationId], cancellationToken);
        if (destination is null) return DestinationErrors.DestinationNotFound(request.DestinationId);

        var result = trip.UpdateDestination(request.Id, request.DestinationId, request.StartDate, request.EndDate,
            request.Price, request.InsurancePrice, request.TaxPrice, request.Loyalty, request.Featured);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto(destination)!;
    }
}