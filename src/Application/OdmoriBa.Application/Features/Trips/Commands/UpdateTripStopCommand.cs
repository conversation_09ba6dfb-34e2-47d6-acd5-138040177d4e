using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record UpdateTripStopCommand(
    Guid TripId,
    Guid Id,
    DateTimeOffset BeginAt,
    DateTimeOffset EndAt,
    TripStopType Type,
    string? Description
) : ICommand<Result<TripStopDto>>;

internal sealed class UpdateTripStopCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTripStopCommand, Result<TripStopDto>>
{
    public async ValueTask<Result<TripStopDto>> Handle(UpdateTripStopCommand request,
        CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripStops)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);
        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var result = trip.UpdateStop(request.Id, request.BeginAt, request.EndAt, request.Type, request.Description);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}