using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeleteTripDestinationCommand(Guid TripId, Guid Id) : ICommand<Result>;

internal sealed class DeleteTripDestinationCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeleteTripDestinationCommand, Result>
{
    public async ValueTask<Result> Handle(DeleteTripDestinationCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripDestinations)
            .Include(s => s.TravelParties)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var result = trip.RemoveDestination(request.Id);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}