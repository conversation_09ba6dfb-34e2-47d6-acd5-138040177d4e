using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record CloneTripCommand(Guid Id, DateOnly StartDate, DateOnly EndDate) : ICommand<Result<TripDto>>;

internal sealed class CloneTripCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CloneTripCommand, Result<TripDto>>
{
    public async ValueTask<Result<TripDto>> Handle(CloneTripCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripDestinations)
            .ThenInclude(d => d.Itineraries)
            .Include(s => s.TripStops)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (trip == null) return TripErrors.TripNotFound(request.Id);

        var newTripResult = Trip.Create(trip.Title, trip.Description, trip.Type, trip.StartDate, trip.EndDate,
            trip.TransportationType);

        if (newTripResult.IsError) return newTripResult.Error;

        var newTrip = newTripResult.Value;

        foreach (var tripStop in trip.TripStops)
        {
            var beginAt = new DateTimeOffset(
                tripStop.Type is TripStopType.Departure or TripStopType.Break
                    ? request.StartDate
                    : request.EndDate,
                TimeOnly.FromTimeSpan(tripStop.BeginAt.DateTime.TimeOfDay),
                TimeSpan.Zero);

            var endAt = new DateTimeOffset(
                tripStop.Type is TripStopType.Departure or TripStopType.Break
                    ? request.StartDate
                    : request.EndDate,
                TimeOnly.FromTimeSpan(tripStop.EndAt.DateTime.TimeOfDay),
                TimeSpan.Zero);

            var tripStopResult = newTrip.AddStop(tripStop.StopId, beginAt, endAt, tripStop.Type, tripStop.Description);
            if (tripStopResult.IsError) return tripStopResult.Error;
        }

        foreach (var tripDestination in trip.TripDestinations)
        {
            var tripDestinationResult = newTrip.AddDestination(tripDestination.DestinationId, request.StartDate,
                request.EndDate, tripDestination.Price, tripDestination.TaxPrice, tripDestination.InsurancePrice,
                tripDestination.Loyalty, false);
            if (tripDestinationResult.IsError) return tripDestinationResult.Error;

            var newTripDestination = tripDestinationResult.Value;

            foreach (var itinerary in tripDestination.Itineraries)
            {
                newTripDestination.AddItinerary(itinerary.DateTime, itinerary.Title, itinerary.Description);
            }
        }

        dbContext.Trips.Add(newTrip);
        await dbContext.SaveChangesAsync(cancellationToken);

        return newTrip.ToDto()!;
    }
}