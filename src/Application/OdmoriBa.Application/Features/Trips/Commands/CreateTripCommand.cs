using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record CreateTripCommand(
    string Title,
    string? Description,
    TripType Type,
    DateOnly StartDate,
    DateOnly EndDate,
    TransportationType TransportationType
) : ICommand<Result<TripDto>>;

internal sealed class CreateTripCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateTripCommand, Result<TripDto>>
{
    public async ValueTask<Result<TripDto>> Handle(CreateTripCommand request, CancellationToken cancellationToken)
    {
        var result = Trip.Create(request.Title, request.Description, request.Type, request.StartDate, request.EndDate,
            request.TransportationType);

        if (result.IsError) return result.Error;

        dbContext.Trips.Add(result.Value);
        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}