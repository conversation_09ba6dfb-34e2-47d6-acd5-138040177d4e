using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record CreateItineraryCommand(
    Guid TripDestinationId,
    DateTimeOffset DateTime,
    string Title,
    string Description
) : ICommand<Result<ItineraryDto>>;

internal sealed class CreateItineraryCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateItineraryCommand, Result<ItineraryDto>>
{
    public async ValueTask<Result<ItineraryDto>> Handle(CreateItineraryCommand request, CancellationToken cancellationToken)
    {
        var tripDestination = await dbContext.TripDestinations
            .Include(s => s.Itineraries)
            .FirstOrDefaultAsync(s => s.Id == request.TripDestinationId, cancellationToken);

        if (tripDestination is null) return TripErrors.TripDestinationNotFound(request.TripDestinationId);

        var result = tripDestination.AddItinerary(request.DateTime, request.Title, request.Description);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}