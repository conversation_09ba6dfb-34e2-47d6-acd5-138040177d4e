using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeletePaymentCommand(Guid TravelPartyId, Guid Id) : ICommand<Result>;

internal sealed class DeletePaymentCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeletePaymentCommand, Result>
{
    public async ValueTask<Result> Handle(DeletePaymentCommand request, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Payments)
            .FirstOrDefaultAsync(s => s.Id == request.TravelPartyId, cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        travelParty.RemovePayment(request.Id);

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}