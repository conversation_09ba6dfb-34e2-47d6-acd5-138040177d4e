using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Commands;

public sealed record DeleteTripBusCommand(Guid TripId, Guid Id) : ICommand<Result>;

internal sealed class DeleteTripBusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<DeleteTripBusCommand, Result>
{
    public async ValueTask<Result> Handle(DeleteTripBusCommand request, CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips.Include(s => s.TripBuses)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var result = trip.RemoveBus(request.Id);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}