using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Models;

public sealed class TripListItemDto
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public TripType Type { get; set; }
    public TripStatus Status { get; set; }

    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }

    public TransportationType? TransportationType { get; set; }
    public List<string>? Destinations { get; set; }
    public int NumberOfTravelers { get; set; }
    public int NumberOfTravelerRequests { get; set; }
}