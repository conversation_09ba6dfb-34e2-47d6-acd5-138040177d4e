using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Models;

public sealed class UserTripListItemDto
{
    public Guid TravelerId { get; set; }
    public TransportationType? TransportationType { get; set; }
    public TripStatus TripStatus { get; set; }
    public TravelerStatus? Status { get; set; }
    public TripDestinationDto? TripDestination { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public double TotalDue { get; set; }
}