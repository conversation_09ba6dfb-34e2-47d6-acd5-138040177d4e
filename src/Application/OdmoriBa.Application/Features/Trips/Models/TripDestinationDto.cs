using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Models;

public sealed class TripDestinationDto
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid DestinationId { get; set; }
    public DestinationDto? Destination { get; set; }

    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public double Price { get; set; }
    public double InsurancePrice { get; set; }
    public double TaxPrice { get; set; }
    public bool Featured { get; set; }
    public List<ItineraryDto>? Itinerary { get; set; }
    public TripDestinationLoyalty Loyalty { get; set; } = null!;
}