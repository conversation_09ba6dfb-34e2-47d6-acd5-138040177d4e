using OdmoriBa.Application.Features.Destinations.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Models;

public sealed class TripStopDto
{
    public Guid Id { get; set; }
    public Guid TripId { get; set; }
    public Guid StopId { get; set; }
    public StopDto? Stop { get; set; }
    public DateTimeOffset? BeginAt { get; set; }
    public DateTimeOffset? EndAt { get; set; }
    public TripStopType? Type { get; set; }
    public string? Description { get; set; }
}