using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Models;

public class TripDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = null!;
    public string? Description { get; set; }
    public TripType? Type { get; set; }
    public TripStatus Status { get; set; }

    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    
    public string DateRange => $"{StartDate:dd.MM.} - {EndDate:dd.MM.}"; 

    public TransportationType? TransportationType { get; set; }
    public List<TripDestinationDto> TripDestinations { get; set; } = [];

    public List<TripStopDto> TripStops { get; set; } = [];
    public List<TripBusDto> TripBuses { get; set; } = [];
}