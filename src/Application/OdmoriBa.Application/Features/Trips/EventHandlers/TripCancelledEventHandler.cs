using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Events;

namespace OdmoriBa.Application.Features.Trips.EventHandlers;

internal sealed class TripCancelledEventHandler(IAppDbContext dbContext) : IDomainEventHandler<TripCompletedEvent>
{
    public async ValueTask Handle(TripCompletedEvent @event, CancellationToken cancellationToken)
    {
        var travelParties = await dbContext.TravelParties
            .Include(s => s.Travelers)
            .ToListAsync(cancellationToken);

        foreach (var travelParty in travelParties)
        {
            travelParty.UpdateStatus(TravelerStatus.Cancelled);
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }
}