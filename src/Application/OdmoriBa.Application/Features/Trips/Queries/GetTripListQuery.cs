using OdmoriBa.Application.Common.Enums;
using OdmoriBa.Application.Features.Trips.Enums;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetTripListQuery : PaginatedFilterQuery<TripSort>,
    ICommand<Result<PaginatedResultDto<TripListItemDto>>>
{
    public IEnumerable<Guid>? DestinationId { get; set; }
    public IEnumerable<TripStatus>? Status { get; set; }
    public DateOnly? FromDate { get; set; }
    public DateOnly? ToDate { get; set; }
}

internal sealed class GetTripListQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetTripListQuery, Result<PaginatedResultDto<TripListItemDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<TripListItemDto>>> Handle(GetTripListQuery request,
        CancellationToken cancellationToken)
    {
        var query = dbContext.Trips
            .Where(x =>
                (string.IsNullOrEmpty(request.SearchTerm) ||
                 x.Title.ToLower().Contains(request.SearchTerm.ToLower())) &&
                (request.Status == null || !request.Status.Any() || request.Status.Contains(x.Status)) &&
                (!request.FromDate.HasValue || x.StartDate >= request.FromDate) &&
                (!request.ToDate.HasValue || x.EndDate <= request.ToDate) &&
                (request.DestinationId == null || !request.DestinationId.Any() ||
                 x.TripDestinations.Any(s => request.DestinationId.Contains(s.DestinationId)))
            );

        var totalCount = await query.CountAsync(cancellationToken);

        query = SortBy(query, request.SortBy, request.SortDirection);

        var items = await query
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => new TripListItemDto
            {
                Id = s.Id,
                Title = s.Title,
                Type = s.Type,
                Status = s.Status,
                StartDate = s.StartDate,
                EndDate = s.EndDate,
                TransportationType = s.TransportationType,
                Destinations = s.TripDestinations.Select(td => td.Destination!.Name).ToList(),
                NumberOfTravelers = s.TravelParties.Sum(t => t.Travelers.Count),
                NumberOfTravelerRequests = s.TravelParties.Sum(t => t.Travelers.Count(traveler => traveler.Status == TravelerStatus.Requested))
            })
            .ToListAsync(cancellationToken);


        return new PaginatedResultDto<TripListItemDto>(
            request.Page,
            request.PageSize,
            totalCount,
            items
        );
    }

    private static IQueryable<Trip> SortBy(IQueryable<Trip> query,
        TripSort? sortBy,
        SortDirection orderDirection)
    {
        sortBy ??= TripSort.StartDate;

        return sortBy switch
        {
            TripSort.StartDate => query.ApplySort(s => s.StartDate, orderDirection),
            TripSort.EndDate => query.ApplySort(s => s.EndDate, orderDirection),
            TripSort.Status => query.ApplySort(s => s.Status, orderDirection),
            _ => query.ApplySort(s => s.CreatedAt, orderDirection)
        };
    }
}