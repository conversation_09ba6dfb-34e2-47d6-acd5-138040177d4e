using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetTripCalendarQuery(DateOnly StartDate, DateOnly EndDate)
    : ICommand<Result<ListResultDto<TripCalendarItemDto>>>;

internal sealed class GetTripCalendarQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetTripCalendarQuery, Result<ListResultDto<TripCalendarItemDto>>>
{
    public async ValueTask<Result<ListResultDto<TripCalendarItemDto>>> Handle(GetTripCalendarQuery request,
        CancellationToken cancellationToken)
    {
        var trips = await dbContext.Trips
            .Where(s => s.Status != TripStatus.Cancelled)
            .Where(s => s.StartDate >= request.StartDate || s.EndDate <= request.EndDate)
            .Select(s => new TripCalendarItemDto
            {
                Id = s.Id,
                Title = s.Title,
                StartDate = s.StartDate,
                EndDate = s.EndDate,
                Status = s.Status
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<TripCalendarItemDto>(trips);
    }
}