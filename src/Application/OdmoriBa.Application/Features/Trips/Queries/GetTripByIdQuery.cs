using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetTripByIdQuery(Guid Id) : ICommand<Result<TripDto>>;

internal sealed class GetTripByIdQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetTripByIdQuery, Result<TripDto>>
{
    public async ValueTask<Result<TripDto>> Handle(GetTripByIdQuery request, CancellationToken cancellationToken)
    {
        var entity = await dbContext.Trips
            .Include(s => s.TripDestinations)
            .ThenInclude(d => d.Destination!.Images)
            .ThenInclude(d => d.FileRecord)
            .Include(s => s.TripDestinations)
            .ThenInclude(d => d.Destination!.Cover)
            .Include(s => s.TripDestinations)
            .ThenInclude(s => s.Itineraries)
            .Include(s => s.TripStops)
            .Include(s => s.TripBuses)
            .ThenInclude(s => s.Bus!.Company)
            .AsSplitQuery()
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (entity is null) return TripErrors.TripNotFound(request.Id);

        return entity.ToDto()!;
    }
}