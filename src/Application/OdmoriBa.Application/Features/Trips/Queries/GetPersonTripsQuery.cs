using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Trips.Models;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetPersonTripsQuery(Guid PersonId) : ICommand<Result<ListResultDto<PersonTripDto>>>;

internal sealed class GetPersonTripsQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetPersonTripsQuery, Result<ListResultDto<PersonTripDto>>>
{
    public async ValueTask<Result<ListResultDto<PersonTripDto>>> Handle(GetPersonTripsQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Travelers
            .Include(s => s.Person)
            .Include(s => s.TravelParty)
            .AsSplitQuery()
            .AsNoTracking()
            .Where(x => x.PersonId == request.PersonId)
            .OrderByDescending(s => s.TravelParty!.TripDestination.StartDate)
            .Select(s => new PersonTripDto
            {
                Id = s.PersonId,
                FirstName = s.Person.FirstName,
                LastName = s.Person.LastName,
                City = s.Person.City,
                TravelerId = s.Id,
                TravelPartyId = s.TravelPartyId,
                Status = s.Status,
                Trip = new TripLightDto
                {
                    Id = s.TravelParty!.Trip!.Id,
                    Title = s.TravelParty.Trip.Title,
                    Status = s.TravelParty.Trip.Status,
                    StartDate = s.TravelParty.Trip.StartDate.ToDateTime(TimeOnly.MinValue),
                    EndDate = s.TravelParty.Trip.EndDate.ToDateTime(TimeOnly.MinValue)
                },
                TripDestination = new TripDestinationDto
                {
                    Id = s.TravelParty.TripDestinationId,
                    DestinationId = s.TravelParty.TripDestination.DestinationId,
                    Destination = s.TravelParty.TripDestination.Destination.ToDto(null),
                    StartDate = s.TravelParty.TripDestination.StartDate.ToDateTime(TimeOnly.MinValue),
                    EndDate = s.TravelParty.TripDestination.EndDate.ToDateTime(TimeOnly.MinValue)
                }
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<PersonTripDto>(items);
    }
}