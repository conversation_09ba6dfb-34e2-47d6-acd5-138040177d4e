using OdmoriBa.Application.Common.Enums;
using OdmoriBa.Application.Features.Trips.Enums;
using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Queries;

public sealed record GetTripsQuery : PaginatedFilterQuery<TripSort>, ICommand<Result<PaginatedResultDto<TripDto>>>
{
    public IEnumerable<Guid>? DestinationId { get; set; }
    public TripStatus? Status { get; set; }
    public DateOnly? FromDate { get; set; }
}

internal sealed class GetTripsQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetTripsQuery, Result<PaginatedResultDto<TripDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<TripDto>>> Handle(GetTripsQuery request,
        CancellationToken cancellationToken)
    {
        var query = dbContext.Trips
            .Include(s => s.TripDestinations)
            .ThenInclude(d => d.Destination!.Cover!)
            .AsSplitQuery()
            .AsNoTracking()
            .Where(x =>
                (string.IsNullOrEmpty(request.SearchTerm) ||
                 x.Title.ToLower().Contains(request.SearchTerm.ToLower())) &&
                (!request.Status.HasValue || x.Status == request.Status) &&
                (!request.FromDate.HasValue || x.StartDate >= request.FromDate) &&
                (request.DestinationId == null || !request.DestinationId.Any() ||
                 x.TripDestinations.Any(s => request.DestinationId.Contains(s.DestinationId)))
            );

        var totalCount = await query.CountAsync(cancellationToken);

        query = SortBy(query, request.SortBy, request.SortDirection);

        var items = await query
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(s => s.ToDto()!)
            .ToListAsync(cancellationToken);

        return new PaginatedResultDto<TripDto>(request.Page, request.PageSize, totalCount, items);
    }

    private static IQueryable<Trip> SortBy(IQueryable<Trip> query,
        TripSort? sortBy,
        SortDirection orderDirection)
    {
        sortBy ??= TripSort.StartDate;

        return sortBy switch
        {
            TripSort.StartDate => query.ApplySort(s => s.StartDate, orderDirection),
            TripSort.EndDate => query.ApplySort(s => s.EndDate, orderDirection),
            TripSort.Status => query.ApplySort(s => s.Status, orderDirection),
            _ => query.ApplySort(s => s.CreatedAt, orderDirection)
        };
    }
}