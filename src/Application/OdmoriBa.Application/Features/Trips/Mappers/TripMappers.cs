using OdmoriBa.Application.Features.Destinations.Mappers;
using OdmoriBa.Application.Features.Transportations.Mappers;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Trips.Mappers;

public static class TripMappers
{
    public static TripDto? ToDto(this Trip? entity) => entity is null
        ? null
        : new TripDto
        {
            Id = entity.Id,
            Title = entity.Title,
            Description = entity.Description,
            Type = entity.Type,
            StartDate = entity.StartDate.ToDateTime(TimeOnly.MinValue),
            EndDate = entity.EndDate.ToDateTime(TimeOnly.MinValue),
            Status = entity.Status,
            TransportationType = entity.TransportationType,
            TripDestinations = entity.TripDestinations.Select(s => s.ToDto()!).ToList(),
            TripStops = entity.TripStops.Select(s => s.ToDto()!).ToList(),
            TripBuses = entity.TripBuses.Select(s => s.ToDto()!).ToList(),
        };
    
    public static TripDestinationDto? ToDto(this TripDestination? entity, Destination? destination = null) => entity is null
        ? null
        : new TripDestinationDto
        {
            Id = entity.Id,
            TripId = entity.TripId,
            DestinationId = entity.DestinationId,
            Destination = (entity.Destination ?? destination)?.ToDto(),
            StartDate = entity.StartDate.ToDateTime(TimeOnly.MinValue),
            EndDate = entity.EndDate.ToDateTime(TimeOnly.MinValue),
            Price = entity.Price,
            InsurancePrice = entity.InsurancePrice,
            TaxPrice = entity.TaxPrice,
            Featured = entity.Featured,
            Itinerary = entity.Itineraries.Select(s => s.ToDto()!).ToList(),
            Loyalty = entity.Loyalty
        };

    public static TripStopDto? ToDto(this TripStop? entity) => entity is null
        ? null
        : new TripStopDto
        {
            Id = entity.Id,
            TripId = entity.TripId,
            BeginAt = entity.BeginAt,
            EndAt = entity.EndAt,
            Type = entity.Type,
            StopId = entity.StopId,
            Stop = entity.Stop?.ToDto(),
            Description = entity.Description,
        };
    
    public static TripBusDto? ToDto(this TripBus? entity, Bus? bus = null) => entity is null
        ? null
        : new TripBusDto
        {
            Id = entity.Id,
            BusId = entity.BusId,
            Bus = (entity.Bus ?? bus)?.ToDto(),
            Name = entity.Name,
            Direction = entity.Direction
        };

    public static ItineraryDto? ToDto(this Itinerary? entity) => entity is null
        ? null
        : new ItineraryDto
        {
            Id = entity.Id,
            TripDestinationId = entity.TripDestinationId,
            DateTime = entity.DateTime,
            Title = entity.Title,
            Description = entity.Description,
        };
}