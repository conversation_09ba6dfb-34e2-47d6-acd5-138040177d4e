using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record UpdateTravelPartyCommand(
    Guid Id,
    Guid MainContactId,
    string? Note
) : ICommand<Result<TravelPartyDto>>;

internal sealed class UpdateTravelPartyCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelPartyCommand, Result<TravelPartyDto>>
{
    public async ValueTask<Result<TravelPartyDto>> Handle(UpdateTravelPartyCommand request,
        CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Travelers)
            .Include(s => s.MainContact)
            .Include(s=> s.Payments)
            .FirstOrDefaultAsync(s => s.Id == request.Id, cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.Id);
        
        var mainContact = await dbContext.Persons.FindAsync([request.MainContactId], cancellationToken);
        if (mainContact is null) return PersonErrors.PersonNotFound(request.MainContactId);

        var result = travelParty.Update(request.MainContactId, request.Note);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return travelParty.ToDto(mainContact)!;
    }
}