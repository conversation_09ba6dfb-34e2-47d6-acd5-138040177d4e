using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record ApplyLoyaltyDiscountCommand(Guid TravelPartyId, Guid PersonId, int Points) : ICommand<Result>;

internal sealed class ApplyLoyaltyDiscountCommandCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<ApplyLoyaltyDiscountCommand, Result>
{
    public async ValueTask<Result> Handle(ApplyLoyaltyDiscountCommand command,
        CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Travelers)
            .ThenInclude(s => s.Person.LoyaltyCard.PointTransactions)
            .FirstOrDefaultAsync(s => s.Id == command.TravelPartyId, cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(command.TravelPartyId);

        var result = travelParty.ApplyLoyaltyPointsDiscount(command.PersonId, command.Points);
        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}