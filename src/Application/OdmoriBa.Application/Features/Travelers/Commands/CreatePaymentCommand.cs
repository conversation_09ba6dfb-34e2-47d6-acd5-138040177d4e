using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record CreatePaymentCommand(
    Guid TravelPartyId,
    Guid PaidByPersonId,
    double Amount,
    DateTime PaidAt,
    PaymentType Type,
    string? Note) : ICommand<Result<PaymentDto>>;

internal sealed class CreatePaymentCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreatePaymentCommand, Result<PaymentDto>>
{
    public async ValueTask<Result<PaymentDto>> Handle(CreatePaymentCommand request, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Payments)
            .FirstOrDefaultAsync(s => s.Id == request.TravelPartyId, cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        var person = await dbContext.Persons.FindAsync([request.PaidByPersonId], cancellationToken);
        if (person is null) return PersonErrors.PersonNotFound(request.PaidByPersonId);

        var result = travelParty.AddPayment(request.PaidByPersonId, request.Amount, request.Type, request.PaidAt,
            request.Note);
        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}