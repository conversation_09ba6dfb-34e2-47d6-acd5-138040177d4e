using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record UpdateTravelerCommand(
    Guid TravelPartyId,
    Guid Id,
    double Price,
    double InsurancePrice,
    double TaxPrice,
    double Discount,
    string? Note,
    Guid? DeparturePointId,
    Guid? ReturnDeparturePointId) : ICommand<Result<TravelerDto>>;

internal sealed class UpdateTravelerCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelerCommand, Result<TravelerDto>>
{
    public async ValueTask<Result<TravelerDto>> Handle(UpdateTravelerCommand request, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties.FindAsync([request.TravelPartyId], cancellationToken);

        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        var result = travelParty.UpdateTraveler(request.Id, request.Price, request.InsurancePrice,
            request.TaxPrice, request.Discount, request.Note, request.DeparturePointId,
            request.ReturnDeparturePointId);
        
        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}