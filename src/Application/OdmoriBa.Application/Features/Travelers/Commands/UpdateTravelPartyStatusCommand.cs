using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record UpdateTravelPartyStatusCommand(Guid TravelPartyId, TravelerStatus NewStatus)
    : ICommand<Result>;

internal sealed class UpdateTravelPartyStatusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelPartyStatusCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTravelPartyStatusCommand command, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Travelers)
            .FirstOrDefaultAsync(s => s.Id == command.TravelPartyId, cancellationToken);
        if (travelParty is null) return TripErrors.TravelPartyNotFound(command.TravelPartyId);

        var result = travelParty.UpdateStatus(command.NewStatus);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Ok();
    }
}