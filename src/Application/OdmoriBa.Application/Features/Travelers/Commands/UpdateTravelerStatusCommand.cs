using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record UpdateTravelerStatusCommand(Guid TravelPartyId, Guid TravelerId, TravelerStatus NewStatus)
    : ICommand<Result>;
    
internal sealed class UpdateTravelerStatusCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<UpdateTravelerStatusCommand, Result>
{
    public async ValueTask<Result> Handle(UpdateTravelerStatusCommand command, CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Travelers)
            .FirstOrDefaultAsync(s => s.Id == command.TravelPartyId, cancellationToken);
        if (travelParty is null) return TripErrors.TravelPartyNotFound(command.TravelPartyId);
        
        var result = travelParty.UpdateStatus(command.TravelerId, command.NewStatus);
        
        if (result.IsError) return result.Error;
        
        await dbContext.SaveChangesAsync(cancellationToken);
        
        return Result.Ok();
    }
}    