using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record CreateTravelerCommand(
    Guid TravelPartyId,
    Guid PersonId,
    double Discount,
    Guid? DeparturePointId,
    Guid? ReturnDeparturePointId,
    string? Note) : ICommand<Result<TravelerDto>>;

internal sealed class CreateTravelerCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateTravelerCommand, Result<TravelerDto>>
{
    public async ValueTask<Result<TravelerDto>> Handle(CreateTravelerCommand request,
        CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(tp => tp.TripDestination)
            .FirstOrDefaultAsync(tp => tp.Id == request.TravelPartyId, cancellationToken);
        if (travelParty is null) return TripErrors.TravelPartyNotFound(request.TravelPartyId);

        var person = await dbContext.Persons.FirstOrDefaultAsync(p => p.Id == request.PersonId, cancellationToken);
        if (person is null) return PersonErrors.PersonNotFound(request.PersonId);

        if (travelParty.Travelers.Any(t => t.PersonId == request.PersonId))
        {
            return TripErrors.PersonExistsInTrip(request.PersonId);
        }

        // Check for date overlap with other trips for this person
        var hasOverlappingTrip = await dbContext.Travelers
            .AnyAsync(t =>
                    t.PersonId == request.PersonId && t.TravelPartyId != travelParty.Id &&
                    t.TravelParty!.TripDestination.StartDate <= travelParty.TripDestination.EndDate &&
                    t.TravelParty.TripDestination.EndDate >= travelParty.TripDestination.StartDate,
                cancellationToken);

        if (hasOverlappingTrip)
        {
            return TripErrors.PersonHasOverlappingTrip(request.PersonId);
        }

        var result = travelParty.AddTraveler(
            request.PersonId,
            travelParty.TripDestination.Price,
            travelParty.TripDestination.InsurancePrice,
            travelParty.TripDestination.TaxPrice,
            request.Discount, request.Note,
            request.DeparturePointId, request.ReturnDeparturePointId);

        if (result.IsError) return result.Error;

        await dbContext.SaveChangesAsync(cancellationToken);

        return result.Value.ToDto()!;
    }
}