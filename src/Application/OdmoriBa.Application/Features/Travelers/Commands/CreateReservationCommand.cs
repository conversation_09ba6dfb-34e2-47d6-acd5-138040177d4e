using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record CreateReservationCommand(
    Guid MainContactId,
    Guid TripId,
    Guid TripDestinationId,
    string? RequestNote,
    Guid? DeparturePointId,
    List<PersonDto> AdditionalPersons,
    int? AppliedPoints) : ICommand<Result<UserTravelPartyDto>>;

internal sealed class CreateReservationCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateReservationCommand, Result<UserTravelPartyDto>>
{
    public async ValueTask<Result<UserTravelPartyDto>> Handle(CreateReservationCommand request,
        CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripDestinations)
            .Include(s => s.TravelParties)
            .ThenInclude(s => s.Travelers)
            .Include(s => s.TripStops)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);

        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var mainContact = await dbContext.Persons
            .Include(s => s.LoyaltyCard.PointTransactions)
            .FirstOrDefaultAsync(p => p.Id == request.MainContactId, cancellationToken);
        if (mainContact == null) return PersonErrors.PersonNotFound(request.MainContactId);

        var travelPartyResult = TravelParty.Create(request.TripId, request.TripDestinationId, request.MainContactId,
            null, request.RequestNote);
        if (travelPartyResult.IsError) return travelPartyResult.Error;
        var travelParty = travelPartyResult.Value;

        var tripDestination = await dbContext.TripDestinations
            .FirstOrDefaultAsync(s => s.Id == request.TripDestinationId, cancellationToken);
        if (tripDestination == null) return TripErrors.TripDestinationNotFound(request.TripDestinationId);

        if (tripDestination.StartDate <= DateTime.UtcNow.ToDateOnly())
        {
            return TripErrors.ReservationTimeExpired(tripDestination.TripId);
        }

        var persons = new List<Person> { mainContact };

        foreach (var person in request.AdditionalPersons)
        {
            if (person.Id != Guid.Empty)
            {
                var existingPerson = await dbContext.Persons.FindAsync([person.Id], cancellationToken);
                if (existingPerson == null) return PersonErrors.PersonNotFound(person.Id);
                persons.Add(existingPerson);
            }
            else
            {
                var newPersonResult = Person.Create(person.FirstName, person.LastName, person.Email, person.Phone,
                    person.BirthDate.ToDateOnly(), person.IdDocument, person.CountryCode, person.City, person.Address);
                if (newPersonResult.IsError) return newPersonResult.Error;

                dbContext.Persons.Add(newPersonResult.Value);
                persons.Add(newPersonResult.Value);
            }
        }

        if (request.AppliedPoints.HasValue)
        {
            if (!tripDestination.Loyalty.CanSpendPoints)
            {
                return TripErrors.CannotSpendPoints;
            }

            if (tripDestination.Loyalty.MaximumPointsToSpend < request.AppliedPoints)
            {
                return TripErrors.MaximumPointsToSpend(tripDestination.Loyalty.MaximumPointsToSpend);
            }

            if (mainContact.LoyaltyCard.TotalPoints < request.AppliedPoints)
            {
                return TripErrors.NotEnoughPoints;
            }

            mainContact.RedeemPoints(request.AppliedPoints.Value, LoyaltyPointTransactionType.Discount,
                tripDestination.Id);
        }

        foreach (var person in persons)
        {
            var hasOverlappingTrip = await dbContext.Travelers
                .AnyAsync(t =>
                        t.PersonId == person.Id &&
                        t.TravelParty!.TripDestination.StartDate <= tripDestination.EndDate &&
                        t.TravelParty.TripDestination.EndDate >= tripDestination.StartDate,
                    cancellationToken);

            if (hasOverlappingTrip)
            {
                return TripErrors.PersonHasOverlappingTrip(person.Id);
            }

            var travelerResult = travelParty.AddTraveler(
                person.Id,
                tripDestination.Price,
                tripDestination.InsurancePrice,
                tripDestination.TaxPrice,
                person.Id == mainContact.Id ? request.AppliedPoints ?? 0 : 0,
                null,
                request.DeparturePointId,
                null,
                TravelerStatus.Requested);

            if (travelerResult.IsError) return travelerResult.Error;
        }


        dbContext.TravelParties.Add(travelParty);

        await dbContext.SaveChangesAsync(cancellationToken);

        return new UserTravelPartyDto
        {
            TravelerId = travelParty.Travelers.First(s => s.PersonId == request.MainContactId).Id,
            TravelParty = travelParty.ToDto()!
        };
    }
}