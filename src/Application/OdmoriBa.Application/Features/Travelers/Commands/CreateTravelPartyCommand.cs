using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Commands;

public sealed record CreateTravelPartyCommand(
    Guid TripId,
    Guid TripDestinationId,
    Guid MainContactId,
    List<TravelPartyTravelerCreateDto> Travelers,
    string Note
) : ICommand<Result<TravelPartyDto>>;

internal sealed class CreateTravelPartyCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<CreateTravelPartyCommand, Result<TravelPartyDto>>
{
    public async ValueTask<Result<TravelPartyDto>> Handle(CreateTravelPartyCommand request,
        CancellationToken cancellationToken)
    {
        var trip = await dbContext.Trips
            .Include(s => s.TripDestinations)
            .FirstOrDefaultAsync(s => s.Id == request.TripId, cancellationToken);
        if (trip is null) return TripErrors.TripNotFound(request.TripId);

        var tripDestination = trip.TripDestinations.FirstOrDefault(s => s.Id == request.TripDestinationId);
        if (tripDestination is null) return TripErrors.TripDestinationNotFound(request.TripDestinationId);

        var travelPartyResult =
            TravelParty.Create(request.TripId, request.TripDestinationId, request.MainContactId, request.Note, null);
        if (travelPartyResult.IsError) return travelPartyResult.Error;
        var travelParty = travelPartyResult.Value;

        var persons = await dbContext.Persons
            .Where(s => request.Travelers.Select(p => p.PersonId).Contains(s.Id))
            .ToListAsync(cancellationToken);

        // Validate persons and trip destinations
        foreach (var requestTraveler in request.Travelers)
        {
            if (persons.All(s => s.Id != requestTraveler.PersonId))
            {
                return PersonErrors.PersonNotFound(requestTraveler.PersonId);
            }
            
            var hasOverlappingTrip = await dbContext.Travelers
                .AnyAsync(t =>
                        t.PersonId == requestTraveler.PersonId &&
                        t.TravelParty!.TripDestination.StartDate <= tripDestination.EndDate &&
                        t.TravelParty.TripDestination.EndDate >= tripDestination.StartDate,
                    cancellationToken);

            if (hasOverlappingTrip)
            {
                return TripErrors.PersonHasOverlappingTrip(requestTraveler.PersonId);
            }

            var travelerResult = travelParty.AddTraveler(requestTraveler.PersonId,
                tripDestination.Price,
                tripDestination.InsurancePrice,
                tripDestination.TaxPrice,
                requestTraveler.Discount,
                requestTraveler.Note,
                requestTraveler.DeparturePointId,
                null);

            if (travelerResult.IsError) return travelerResult.Error;
        }
        
        dbContext.TravelParties.Add(travelParty);

        await dbContext.SaveChangesAsync(cancellationToken);

        return travelParty.ToDto()!;
    }
}