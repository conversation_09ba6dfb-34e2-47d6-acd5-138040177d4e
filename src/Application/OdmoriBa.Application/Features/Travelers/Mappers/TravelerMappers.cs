using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Travelers.Mappers;

public static class TravelerMappers
{
    public static TravelerDto? ToDto(this Traveler? entity) =>
        entity is null
            ? null
            : new TravelerDto
            {
                Id = entity.Id,
                TravelPartyId = entity.TravelPartyId,
                TripDestinationId = entity.TravelParty?.TripDestinationId,
                Discount = entity.Discount,
                Price = entity.Price,
                InsurancePrice = entity.InsurancePrice,
                TaxPrice = entity.TaxPrice,
                PersonId = entity.PersonId,
                Person = entity.Person.ToDto(),
                Status = entity.Status,
                Note = entity.Note,
                DeparturePointId = entity.DeparturePointId,
                DeparturePoint = entity.DeparturePoint.ToDto(),
                ReturnDeparturePointId = entity.ReturnDeparturePointId,
                ReturnDeparturePoint = entity.ReturnDeparturePoint.ToDto(),
            };
    
}