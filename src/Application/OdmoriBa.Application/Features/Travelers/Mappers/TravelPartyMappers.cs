using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Travelers.Mappers;

public static class TravelPartyMappers
{
    public static PaymentDto? ToDto(this Payment? entity, Person? paidByPerson = null) => entity is null
        ? null
        : new PaymentDto
        {
            Id = entity.Id,
            Amount = entity.Amount,
            Type = entity.Type,
            PaidAt = entity.PaidAt,
            Note = entity.Note,
            PaidByPersonId = entity.PaidByPersonId,
            PaidByPerson = (entity.PaidByPerson ?? paidByPerson)?.ToDto(false)
        };

    public static TravelPartyDto? ToDto(this TravelParty? entity, Person? mainContact = null) => entity is null
        ? null
        : new TravelPartyDto
        {
            Id = entity.Id,
            MainContactId = entity.MainContactId,
            MainContact = (entity.MainContact ?? mainContact)?.ToDto(),
            TripDestinationId = entity.TripDestinationId,
            TripDestination = entity.TripDestination?.ToDto(),
            TransportationType = entity.Trip?.TransportationType,
            TripStatus = entity.Trip?.Status,
            Travelers = entity.Travelers.Select(s => s.ToDto()!).ToList(),
            Note = entity.Note,
            RequestNote = entity.RequestNote,
            Payments = entity.Payments.Select(s => s.ToDto()!).ToList()
        };
}