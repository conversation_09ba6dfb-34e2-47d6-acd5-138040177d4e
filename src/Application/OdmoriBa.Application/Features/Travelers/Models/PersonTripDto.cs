using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Travelers.Models;

public sealed class PersonTripDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? City { get; set; } = null!;
    public TripLightDto? Trip { get; set; }
    public TripDestinationDto? TripDestination { get; set; }
    public Guid TravelPartyId { get; set; }
    public Guid TravelerId { get; set; }
    public TravelerStatus Status { get; set; }
}