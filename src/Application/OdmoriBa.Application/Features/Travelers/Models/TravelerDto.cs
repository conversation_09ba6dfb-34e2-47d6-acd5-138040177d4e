using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Travelers.Models;

public class TravelerDto
{
    public Guid Id { get; set; }
    public Guid TravelPartyId { get; set; }
    public Guid PersonId { get; set; }
    public Guid? TripDestinationId { get; set; }
    public PersonDto? Person { get; set; }
    public TravelerStatus Status { get; set; }
    public double Price { get; set; }
    public double InsurancePrice { get; set; }
    public double TaxPrice { get; set; }
    public double Discount { get; set; }
    public string? Note { get; set; }
    public Guid? DeparturePointId { get; set; }
    public TripStopDto? DeparturePoint { get; set; }
    public Guid? ReturnDeparturePointId { get; set; }
    public TripStopDto? ReturnDeparturePoint { get; set; }
    
    public Guid? DepartureTripBusId { get; set; }
    public TripBusDto? DepartureTripBus { get; set; }
    public int? DepartureSeatNumber { get; set; }
    public Guid? ReturnTripBusId { get; set; }
    public TripBusDto? ReturnTripBus { get; set; }
    public int? ReturnSeatNumber { get; set; }
}