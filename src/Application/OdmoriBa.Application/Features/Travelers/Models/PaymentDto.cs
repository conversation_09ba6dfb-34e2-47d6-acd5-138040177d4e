using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.Application.Features.Travelers.Models;

public sealed class PaymentDto
{
    public Guid Id { get; set; }

    public double Amount { get; set; }
    public PaymentType? Type { get; set; }
    public DateTime PaidAt { get; set; }
    public string? Note { get; set; }
    public Guid PaidByPersonId { get; set; }
    public PersonDto? PaidByPerson { get; set; }
}