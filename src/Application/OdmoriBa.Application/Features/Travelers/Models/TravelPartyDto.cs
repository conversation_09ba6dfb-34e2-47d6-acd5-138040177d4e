using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Models;

public sealed class TravelPartyDto
{
    public Guid Id { get; set; }
    public TransportationType? TransportationType { get; set; }
    public TripStatus? TripStatus { get; set; }
    public Guid TripDestinationId { get; set; }
    public TripDestinationDto? TripDestination { get; set; }
    public Guid MainContactId { get; set; } // PersonId
    public PersonDto? MainContact { get; set; }
    public List<TravelerDto> Travelers { get; set; } = [];
    public string? Note { get; set; }
    public string? RequestNote { get; set; }
    public List<PaymentDto> Payments { get; set; } = [];
    public double TotalPrice => Travelers.Sum(x => x.Price + x.InsurancePrice + x.TaxPrice - x.Discount);
    public double TotalPaid => Payments.Sum(x => x.Amount);
    public double TotalDue => TotalPrice - TotalPaid;
    public double TotalDiscount => Travelers.Sum(x => x.Discount);

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(this, obj))
            return true;

        if (obj is null || obj.GetType() != GetType())
            return false;

        var other = (TravelPartyDto)obj;
        return Id.Equals(other.Id);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }
}