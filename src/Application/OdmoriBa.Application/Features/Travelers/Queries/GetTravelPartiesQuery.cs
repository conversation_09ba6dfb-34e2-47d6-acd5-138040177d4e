using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;

namespace OdmoriBa.Application.Features.Travelers.Queries;

public sealed record GetTravelPartiesQuery(Guid TripId) : ICommand<Result<ListResultDto<TravelPartyDto>>>;

internal sealed class GetTravelPartiesQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetTravelPartiesQuery, Result<ListResultDto<TravelPartyDto>>>
{
    public async ValueTask<Result<ListResultDto<TravelPartyDto>>> Handle(GetTravelPartiesQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.TravelParties
            .AsNoTracking()
            .Include(t => t.Travelers)
            .Include(s => s.Payments)
            .AsSplitQuery()
            .Where(s => s.TripId == request.TripId)
            .ToListAsync(cancellationToken);

        return new ListResultDto<TravelPartyDto>(items.Select(s => s.ToDto()!).ToList());
    }
}