using OdmoriBa.Application.Features.Customers.Mappers;
using OdmoriBa.Application.Features.Customers.Models;

namespace OdmoriBa.Application.Features.Travelers.Queries;

public sealed record GetUserPartyPersonsQuery(Guid UserId) : ICommand<Result<ListResultDto<PersonDto>>>;

internal sealed class GetUserPartyPersonsQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetUserPartyPersonsQuery, Result<ListResultDto<PersonDto>>>
{
    public async ValueTask<Result<ListResultDto<PersonDto>>> Handle(GetUserPartyPersonsQuery request,
        CancellationToken cancellationToken)
    {
        var travelParties = await dbContext.TravelParties
            .Where(s => s.Travelers.Any(t => t.PersonId == request.UserId))
            .SelectMany(s => s.Travelers.Where(t => t.PersonId != request.UserId))
            .Select(s => s.Person.ToDto(true))
            .Distinct()
            .ToListAsync(cancellationToken);

        return new ListResultDto<PersonDto>(travelParties!);
    }
}