using OdmoriBa.Application.Features.Trips.Mappers;
using OdmoriBa.Application.Features.Trips.Models;

namespace OdmoriBa.Application.Features.Travelers.Queries;

public sealed record GetUserTripsQuery(Guid UserId) : ICommand<Result<ListResultDto<UserTripListItemDto>>>;

internal sealed class GetUserTripsQueryHandler(IAppDbContext dbContext)
    : ICommandHandler<GetUserTripsQuery, Result<ListResultDto<UserTripListItemDto>>>
{
    public async ValueTask<Result<ListResultDto<UserTripListItemDto>>> Handle(GetUserTripsQuery request,
        CancellationToken cancellationToken)
    {
        var items = await dbContext.Travelers
            .Include(s => s.TravelParty!.Trip)
            .Include(s => s.TravelParty!)
            .ThenInclude(s => s.Payments)
            .Include(s => s.TravelParty!)
            .ThenInclude(s => s.Travelers)
            .AsSplitQuery()
            .AsNoTracking()
            .Where(x => x.PersonId == request.UserId)
            .OrderByDescending(s => s.TravelParty!.TripDestination.StartDate)
            .Select(s => new UserTripListItemDto
            {
                TravelerId = s.Id,
                TransportationType = s.TravelParty!.Trip!.TransportationType,
                TripStatus = s.TravelParty.Trip!.Status,
                TotalDue = s.TravelParty!.TotalDue,
                TripDestination = s.TravelParty.TripDestination.ToDto(null)!,
                Status = s.Status,
                CreatedAt = s.CreatedAt
            })
            .ToListAsync(cancellationToken);

        return new ListResultDto<UserTripListItemDto>(items);
    }
}