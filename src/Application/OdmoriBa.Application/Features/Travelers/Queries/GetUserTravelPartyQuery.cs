using OdmoriBa.Application.Features.Travelers.Mappers;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Features.Travelers.Queries;

public sealed record GetUserTravelPartyQuery(Guid UserId, Guid TravelerId) : ICommand<Result<TravelPartyDto>>;

internal sealed class GetTravelPartyByTravelerIdCommandHandler(IAppDbContext dbContext)
    : ICommandHandler<GetUserTravelPartyQuery, Result<TravelPartyDto>>
{
    public async ValueTask<Result<TravelPartyDto>> Handle(GetUserTravelPartyQuery request,
        CancellationToken cancellationToken)
    {
        var travelParty = await dbContext.TravelParties
            .Include(s => s.Trip)
            .Include(s => s.Travelers)
            .Include(s => s.Payments)
            .Include(s => s.MainContact)
            .FirstOrDefaultAsync(s => s.Travelers.Any(t => t.PersonId == request.UserId && t.Id == request.TravelerId),
                cancellationToken);

        if (travelParty is null) return TripErrors.TravelerNotFound(request.TravelerId);

        return travelParty.ToDto()!;
    }
}