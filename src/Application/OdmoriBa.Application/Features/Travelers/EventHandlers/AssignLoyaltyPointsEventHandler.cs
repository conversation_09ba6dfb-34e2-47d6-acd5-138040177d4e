using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Trips.Events;

namespace OdmoriBa.Application.Features.Travelers.EventHandlers;

internal sealed class AssignLoyaltyPointsEventHandler(IAppDbContext dbContext) : IDomainEventHandler<TripCompletedEvent>
{
    public async ValueTask Handle(TripCompletedEvent @event, CancellationToken cancellationToken)
    {
        var travelParties = await dbContext.TravelParties
            .Include(s => s.TripDestination)
            .Include(s => s.Travelers)
            .ThenInclude(s => s.Person.LoyaltyCard)
            .ThenInclude(s => s.PointTransactions)
            .ToListAsync(cancellationToken);

        foreach (var travelParty in travelParties)
        {
            if (travelParty.TripDestination.Loyalty.Points <= 0) continue;
            foreach (var traveler in travelParty.Travelers)
            {
                traveler.Person.EarnPoints(
                    travelParty.TripDestination.Loyalty.Points, 
                    LoyaltyPointTransactionType.Trip,
                    travelParty.TripDestinationId);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }
}