using OdmoriBa.Core.Domains.Notifications.Entities;
using OdmoriBa.Core.Domains.Outbox.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Travelers.Events;

namespace OdmoriBa.Application.Features.Travelers.EventHandlers;

internal sealed class TravelerStatusChangedEventHandler(IAppDbContext dbContext)
    : IDomainEventHandler<TravelerStatusChangedDomainEvent>
{
    public async ValueTask Handle(TravelerStatusChangedDomainEvent notification, CancellationToken cancellationToken)
    {
        var traveler = await dbContext.Travelers
            .Include(s => s.TravelParty)
            .FirstOrDefaultAsync(s => s.Id == notification.TravelerId, cancellationToken);

        if (traveler is null) return;

        var devices = await dbContext.DeviceBindings
            .AsNoTracking()
            .Where(s => s.UserId == traveler.PersonId)
            .Select(s => new { s.PushNotificationToken, s.DeviceType })
            .ToListAsync(cancellationToken);

        if (devices.Count == 0) return;

        PushNotificationType? type = notification.NewStatus switch
        {
            TravelerStatus.Draft => PushNotificationType.TravelerStatusDrafted,
            TravelerStatus.Confirmed => PushNotificationType.TravelerStatusConfirmed,
            TravelerStatus.Cancelled => PushNotificationType.TravelerStatusCanceled,
            _ => null
        };

        if (type is null) return;

        var (title, body) = GetNotificationTitleAndDescription(traveler, notification.NewStatus);

        if (string.IsNullOrWhiteSpace(title) || string.IsNullOrWhiteSpace(body)) return;
        
        var additionalData = new Dictionary<string, string>
        {
            { "type", type.ToString()! },
            { "travelerId", notification.TravelerId.ToString() },
        };

        foreach (var device in devices)
        {
            var pushNotification = PushNotificationOutbox.Create(
                type,
                traveler.PersonId,
                device.PushNotificationToken,
                null,
                title,
                body,
                device.DeviceType,
                additionalData
            );

            dbContext.PushNotificationOutbox.Add(pushNotification);
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }

    private static (string title, string body) GetNotificationTitleAndDescription(Traveler traveler, TravelerStatus newStatus)
    {
        var destination = traveler.TravelParty!.TripDestination?.Destination?.Name;
        var dateFrom = traveler.TravelParty!.TripDestination?.StartDate.ToString("dd.MM");
        var dateTo = traveler.TravelParty!.TripDestination?.EndDate.ToString("dd.MM");

        return newStatus switch
        {
            TravelerStatus.Draft => ("Rezervacija u obradi",
                $"Vaš zahtjev za putovanje ({destination} {dateFrom}-{dateTo}) je u obradi"),
            TravelerStatus.Confirmed => ("Rezervacija potvrđena",
                $"Vaš zahtjev za putovanje ({destination} {dateFrom}-{dateTo}) je potvrđen"),
            TravelerStatus.Cancelled => ("Rezervacija otkazana",
                $"Vaš zahtjev za putovanje ({destination} {dateFrom}-{dateTo}) je otkazan"),
            _ => ("", "")
        };
    }
}