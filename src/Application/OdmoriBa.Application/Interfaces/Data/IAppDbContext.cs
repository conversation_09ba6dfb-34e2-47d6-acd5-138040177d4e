using Microsoft.EntityFrameworkCore.Infrastructure;
using OdmoriBa.Core.Domains.Companies.Entities;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Destinations.Entities;
using OdmoriBa.Core.Domains.Files.Entities;
using OdmoriBa.Core.Domains.Outbox.Entities;
using OdmoriBa.Core.Domains.Transportations.Entities;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Application.Interfaces.Data;

public interface IAppDbContext
{
    DbSet<User> Users { get; }
    DbSet<City> Cities { get; }
    DbSet<Destination> Destinations { get; }
    DbSet<DestinationImage> DestinationImages { get; }
    DbSet<Stop> Stops { get; }
    DbSet<Trip> Trips { get; }
    DbSet<TripStop> TripStops { get; }
    DbSet<TripDestination> TripDestinations { get; }
    DbSet<Traveler> Travelers { get; }
    DbSet<TravelParty> TravelParties { get; }
    DbSet<Payment> Payments { get; }
    DbSet<Itinerary> Itinerary { get; }
    DbSet<Person> Persons { get; }
    DbSet<LoyaltyCard> LoyaltyCards { get; }
    DbSet<LoyaltyPointTransaction> LoyaltyPointTransactions { get; }
    DbSet<FileRecord> FileRecords { get; }

    DbSet<Company> Companies { get; }
    DbSet<Bus> Buses { get; }
    DbSet<TripBus> TripBuses { get; }

    DbSet<DeviceBinding> DeviceBindings { get; }
    DbSet<PushNotificationOutbox> PushNotificationOutbox { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    DatabaseFacade Database { get; }
}