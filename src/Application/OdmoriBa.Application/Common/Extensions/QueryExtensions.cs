using System.Linq.Expressions;
using OdmoriBa.Application.Common.Enums;

namespace OdmoriBa.Application.Common.Extensions;

public static class QueryExtensions
{
    public static IQueryable<T> ApplySort<T>(this IQueryable<T> query, Expression<Func<T, object?>> orderBy,
        SortDirection sortDirection)
    {
        return sortDirection == SortDirection.Desc ? query.OrderByDescending(orderBy) : query.OrderBy(orderBy);
    }
}