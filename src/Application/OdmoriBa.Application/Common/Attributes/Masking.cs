using Destructurama.Attributed;

namespace OdmoriBa.Application.Common.Attributes;

public sealed class MaskName : LogMaskedAttribute
{
    public MaskName()
    {
        ShowFirst = 1;
    }
}

public sealed class MaskPhone : LogMaskedAttribute
{
    public MaskPhone()
    {
        ShowFirst = 4;
        ShowLast = 2;
    }
}

public sealed class MaskEmail : LogMaskedAttribute
{
    public MaskEmail()
    {
        ShowFirst = 1;
        ShowLast = 6;
    }
}

public sealed class MaskIdDocument : LogMaskedAttribute
{
    public MaskIdDocument()
    {
        ShowFirst = 1;
        ShowLast = 2;
    }
}

public sealed class MaskDate : LogMaskedAttribute
{
    public MaskDate()
    {
        ShowFirst = 2;
        ShowLast = 2;
    }
}