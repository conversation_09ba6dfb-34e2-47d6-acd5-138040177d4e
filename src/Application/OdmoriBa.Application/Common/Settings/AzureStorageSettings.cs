using Microsoft.Extensions.Configuration;

namespace OdmoriBa.Application.Common.Settings;

public sealed class AzureStorageSettings
{
    private static readonly Lazy<AzureStorageSettings> _instance = new(() => new AzureStorageSettings());

    public static AzureStorageSettings Instance => _instance.Value;

    public string PublicContainerUrl { get; private set; }

    private AzureStorageSettings()
    {
    }

    public void Initialize(IConfiguration configuration)
    {
        PublicContainerUrl = configuration["AzureStorage:PublicContainerUrl"]!;
    }

    public string? GetFullPublicUrl(string? url) =>
        string.IsNullOrEmpty(url) ? null : Path.Combine(PublicContainerUrl, url);
}