<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
        <PackageReference Include="Destructurama.Attributed" Version="5.1.0" />
        <PackageReference Include="FirebaseAdmin" Version="3.3.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.7" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Mediator.Abstractions" Version="3.0.*-*" />
        <PackageReference Include="Mediator.SourceGenerator" Version="3.0.*-*">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\..\Domain\OdmoriBa.Core\OdmoriBa.Core.csproj" />
    </ItemGroup>

</Project>
