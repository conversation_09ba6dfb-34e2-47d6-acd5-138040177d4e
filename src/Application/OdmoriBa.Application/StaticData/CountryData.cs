namespace OdmoriBa.Application.StaticData;

public sealed record Country(string Name, string Code, string PhoneCode, string Flag);

public static class CountryData
{
    public static List<Country> All =
    [
        new("Albanija", "AL", "+355", "🇦🇱"),
        new("<PERSON><PERSON>", "AD", "+376", "🇦🇩"),
        new("Armeni<PERSON>", "AM", "+374", "🇦🇲"),
        new("Austrija", "AT", "+43", "🇦🇹"),
        new("Azerbejdžan", "AZ", "+994", "🇦🇿"),
        new("Bjelorusija", "BY", "+375", "🇧🇾"),
        new("Belgija", "BE", "+32", "🇧🇪"),
        new("Bosna i Hercegovina", "BA", "+387", "🇧🇦"),
        new("Bugarska", "BG", "+359", "🇧🇬"),
        new("Hrvatska", "HR", "+385", "🇭🇷"),
        new("<PERSON><PERSON>", "CY", "+357", "🇨🇾"),
        new("<PERSON><PERSON><PERSON><PERSON>", "CZ", "+420", "🇨🇿"),
        new("<PERSON><PERSON>", "DK", "+45", "🇩🇰"),
        new("<PERSON><PERSON><PERSON>", "EE", "+372", "🇪🇪"),
        new("<PERSON><PERSON>", "FI", "+358", "🇫🇮"),
        new("Francuska", "FR", "+33", "🇫🇷"),
        new("Gruzija", "GE", "+995", "🇬🇪"),
        new("Njema<PERSON>ka", "DE", "+49", "🇩🇪"),
        new("Grčka", "GR", "+30", "🇬🇷"),
        new("Mađarska", "HU", "+36", "🇭🇺"),
        new("Island", "IS", "+354", "🇮🇸"),
        new("Irska", "IE", "+353", "🇮🇪"),
        new("Italija", "IT", "+39", "🇮🇹"),
        new("Kazahstan", "KZ", "+7", "🇰🇿"),
        new("Kosovo", "XK", "+383", "🇽🇰"),
        new("Latvija", "LV", "+371", "🇱🇻"),
        new("Lihtenštajn", "LI", "+423", "🇱🇮"),
        new("Litvanija", "LT", "+370", "🇱🇹"),
        new("Luksemburg", "LU", "+352", "🇱🇺"),
        new("Malta", "MT", "+356", "🇲🇹"),
        new("Moldavija", "MD", "+373", "🇲🇩"),
        new("Monako", "MC", "+377", "🇲🇨"),
        new("Crna Gora", "ME", "+382", "🇲🇪"),
        new("Nizozemska", "NL", "+31", "🇳🇱"),
        new("Sjeverna Makedonija", "MK", "+389", "🇲🇰"),
        new("Norveška", "NO", "+47", "🇳🇴"),
        new("Poljska", "PL", "+48", "🇵🇱"),
        new("Portugal", "PT", "+351", "🇵🇹"),
        new("Rumunija", "RO", "+40", "🇷🇴"),
        new("Rusija", "RU", "+7", "🇷🇺"),
        new("San Marino", "SM", "+378", "🇸🇲"),
        new("Srbija", "RS", "+381", "🇷🇸"),
        new("Slovačka", "SK", "+421", "🇸🇰"),
        new("Slovenija", "SI", "+386", "🇸🇮"),
        new("Španija", "ES", "+34", "🇪🇸"),
        new("Švedska", "SE", "+46", "🇸🇪"),
        new("Švicarska", "CH", "+41", "🇨🇭"),
        new("Turska", "TR", "+90", "🇹🇷"),
        new("Ukrajina", "UA", "+380", "🇺🇦"),
        new("Ujedinjeno Kraljevstvo", "GB", "+44", "🇬🇧"),
        new("Vatikan", "VA", "+379", "🇻🇦"),
        new("Australija", "AU", "+61", "🇦🇺"),
        new("Kanada", "CA", "+1", "🇨🇦"),
        new("Sjedinjene Američke Države", "US", "+1", "🇺🇸")
    ];
    
    public static readonly Dictionary<string, Country> Countries = All.ToDictionary(x => x.Code);
    
    public static string? GetNameWithFlag(string? code) => string.IsNullOrEmpty(code) ? null : Countries[code].Flag + " " + Countries[code].Name;
    public static string? GetName(string? code) => string.IsNullOrEmpty(code) ? null : Countries[code].Name;
}
