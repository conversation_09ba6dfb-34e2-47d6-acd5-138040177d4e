using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Core.Domains.Destinations.Entities;

public sealed class DestinationImage : EntityBase<Guid>
{
    public Guid DestinationId { get; private set; }
    public string? Title { get; private set; }
    public string? Description { get; private set; }

    public Guid FileRecordId { get; private set; }
    public FileRecord? FileRecord { get; init; }
    public Dictionary<int, string>? ThumbnailUrls { get; private set; }

    private DestinationImage()
    {
    }

    internal static Result<DestinationImage> Create(Guid destinationId, string? title, string? description,
        Guid fileRecordId)
    {
        if (fileRecordId == Guid.Empty) return DestinationImageErrors.InvalidFileRecordId(fileRecordId);

        return new DestinationImage
        {
            Id = Guid.CreateVersion7(),
            DestinationId = destinationId,
            Title = title,
            Description = description,
            FileRecordId = fileRecordId
        };
    }

    internal Result Update(string? title, string? description)
    {
        Title = title;
        Description = description;

        return Result.Ok();
    }
}

public static class DestinationImageErrors
{
    public static Error InvalidFileRecordId(Guid fileRecordId) =>
        Error.Validation("DestinationImage.InvalidFileRecordId", $"Invalid file record id: {fileRecordId}");
}