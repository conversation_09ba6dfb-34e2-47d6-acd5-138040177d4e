namespace OdmoriBa.Core.Domains.Destinations.Entities;

public sealed class City : EntityBase<Guid>
{
    public string Name { get; private set; } = null!;
    public string CountryCode { get; private set; } = null!;

    private City()
    {
    }

    public static Result<City> Create(string countryCode, string name)
    {
        if (string.IsNullOrWhiteSpace(name)) return CityErrors.InvalidName(name);
        if (string.IsNullOrWhiteSpace(countryCode)) return CityErrors.InvalidCountryCode(countryCode);

        return new City
        {
            Id = Guid.CreateVersion7(),
            CountryCode = countryCode,
            Name = name,
        };
    }

    public Result Update(string countryCode, string name)
    {
        if (string.IsNullOrWhiteSpace(name)) return CityErrors.InvalidName(name);
        if (string.IsNullOrWhiteSpace(countryCode)) return CityErrors.InvalidCountryCode(countryCode);

        CountryCode = countryCode;
        Name = name;

        return Result.Ok();
    }
}

public static class CityErrors
{
    public static Error InvalidId(Guid id) =>
        Error.Validation("City.InvalidId", $"Invalid city ID: {id}");
    public static Error InvalidName(string name) =>
        Error.Validation("City.InvalidName", $"Invalid city name: {name}");

    public static Error InvalidCountryCode(string countryCode) =>
        Error.Validation("City.InvalidCountryCode", $"Invalid country code: {countryCode}");

    public static Error CityNotFound(Guid cityId) =>
        Error.Validation("City.CityNotFound", $"City: {cityId} not found");
}