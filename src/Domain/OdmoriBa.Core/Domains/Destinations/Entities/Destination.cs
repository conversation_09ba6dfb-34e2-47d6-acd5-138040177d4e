using OdmoriBa.Core.Domains.Files.Entities;

namespace OdmoriBa.Core.Domains.Destinations.Entities;

public sealed class Destination : EntityBase<Guid>, IAggregateRoot
{
    public string Name { get; private set; } = null!;
    public Guid? CoverId { get; private set; }
    public FileRecord? Cover { get; init; }

    private readonly List<DestinationImage> _images = [];
    public IReadOnlyCollection<DestinationImage> Images => _images.AsReadOnly();

    private Destination()
    {
    }

    public static Result<Destination> Create(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) return DestinationErrors.InvalidName(name);

        return new Destination
        {
            Id = Guid.CreateVersion7(),
            Name = name
        };
    }

    public Result Update(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) return DestinationErrors.InvalidName(name);

        Name = name;

        return Result.Ok();
    }

    public Result<DestinationImage> AddImage(string? title, string? description, Guid fileRecordId)
    {
        var result = DestinationImage.Create(Id, title, description, fileRecordId);
        if (result.IsError) return result.Error;

        _images.Add(result.Value);

        return result.Value;
    }

    public Result RemoveImage(Guid destinationImageId)
    {
        var image = _images.FirstOrDefault(s => s.Id == destinationImageId);

        if (image is null) return DestinationErrors.DestinationImageNotFound(destinationImageId);

        _images.Remove(image);

        return Result.Ok();
    }

    public Result<DestinationImage> UpdateImage(Guid destinationImageId, string? title, string? description)
    {
        var image = _images.FirstOrDefault(s => s.Id == destinationImageId);

        if (image is null) return DestinationErrors.DestinationImageNotFound(destinationImageId);

        var result = image.Update(title, description);

        if (result.IsError) return result.Error;

        return image;
    }

    public Result SetCover(Guid coverFileRecordId)
    {
        if (coverFileRecordId == Guid.Empty) return DestinationErrors.InvalidCoverId(coverFileRecordId);

        CoverId = coverFileRecordId;

        return Result.Ok();
    }
}

public static class DestinationErrors
{
    public static Error InvalidName(string name) =>
        Error.Validation("Destination.InvalidName", $"Invalid destination name: {name}");
    public static Error InvalidCoverId(Guid coverId) =>
        Error.Validation("Destination.InvalidCoverId", $"Invalid cover id: {coverId}");
    public static Error DestinationImageNotFound(Guid destinationImageId) =>
        Error.NotFound("Destination.DestinationImageNotFound", $"Destination image: {destinationImageId} not found");

    public static Error DestinationNotFound(Guid destinationId) =>
        Error.NotFound("Destination.DestinationNotFound", $"Destination: {destinationId} not found");
}