namespace OdmoriBa.Core.Domains.Destinations.Entities;

public sealed class Stop : EntityBase<Guid>
{
    public string Address { get; private set; } = null!;
    public Guid CityId { get; private set; }
    public City? City { get; init; }
    public double? Longitude { get; private set; }
    public double? Latitude { get; private set; }
    public string? Description { get; private set; }

    private Stop()
    {
    }

    public static Result<Stop> Create(string address, Guid cityId, double? longitude, double? latitude,
        string? description)
    {
        if (string.IsNullOrWhiteSpace(address)) return StopErrors.InvalidAddress(address);
        if (cityId == Guid.Empty) return CityErrors.InvalidId(cityId);

        return new Stop
        {
            Id = Guid.CreateVersion7(),
            Address = address,
            CityId = cityId,
            Longitude = longitude,
            Latitude = latitude,
            Description = description,
        };
    }

    public Result Update(string address, Guid cityId, double? longitude, double? latitude, string? description)
    {
        if (string.IsNullOrWhiteSpace(address)) return StopErrors.InvalidAddress(address);
        if (cityId == Guid.Empty) return CityErrors.InvalidId(cityId);

        Address = address;
        CityId = cityId;
        Longitude = longitude;
        Latitude = latitude;
        Description = description;

        return Result.Ok();
    }
}

public static class StopErrors
{
    public static Error InvalidAddress(string address) =>
        Error.Validation("Stop.InvalidAddress", $"Invalid stop address: {address}");

    public static Error StopNotFound(Guid stopId) => Error.NotFound("Stop.NotFound", $"Stop: {stopId} not found");
}