
using System.ComponentModel;
using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Events;
using OdmoriBa.Core.Domains.Trips.Extensions;

namespace OdmoriBa.Core.Domains.Trips.Entities;

public sealed class Trip : EntityBase<Guid>, IAggregateRoot
{
    public string Title { get; private set; } = null!;
    public string? Description { get; private set; }
    public TripType Type { get; private set; }
    public DateOnly StartDate { get; private set; }
    public DateOnly EndDate { get; private set; }
    public TripStatus Status { get; private set; }
    public TransportationType TransportationType { get; private set; }

    private readonly List<TripDestination> _tripDestinations = [];
    public IReadOnlyCollection<TripDestination> TripDestinations => _tripDestinations.AsReadOnly();

    private readonly List<TripStop> _tripStops = [];
    public IReadOnlyCollection<TripStop> TripStops => _tripStops.AsReadOnly();

    private readonly List<TripBus> _tripBuses = [];
    public IReadOnlyCollection<TripBus> TripBuses => _tripBuses.AsReadOnly();

    public IReadOnlyCollection<TravelParty> TravelParties { get; init; } = null!;

    private Trip()
    {
    }

    public static Result<Trip> Create(string title, string? description, TripType type, DateOnly startDate,
        DateOnly endDate, TransportationType transportationType)
    {
        if (string.IsNullOrWhiteSpace(title)) return TripErrors.InvalidTitle(title);
        if (startDate == default) return TripErrors.StartRequired;
        if (endDate == default) return TripErrors.EndDateRequired;
        if (startDate >= endDate) return TripErrors.StartDateMustBeBeforeOrEqualToEndDate(startDate, endDate);

        return new Trip
        {
            Id = Guid.CreateVersion7(),
            Title = title,
            Description = description,
            Type = type,
            StartDate = startDate,
            EndDate = endDate,
            TransportationType = transportationType,
            Status = TripStatus.Draft
        };
    }

    public Result Update(string title, string? description, TripType type, DateOnly startDate,
        DateOnly endDate, TransportationType transportationType)
    {
        if (string.IsNullOrWhiteSpace(title)) return TripErrors.InvalidTitle(title);
        if (startDate == default) return TripErrors.StartRequired;
        if (endDate == default) return TripErrors.EndDateRequired;
        if (startDate >= endDate) return TripErrors.StartDateMustBeBeforeOrEqualToEndDate(startDate, endDate);

        Title = title;
        Description = description;
        Type = type;
        StartDate = startDate;
        EndDate = endDate;
        TransportationType = transportationType;

        return Result.Ok();
    }

    public Result<TripDestination> AddDestination(Guid destinationId, DateOnly startDate, DateOnly endDate,
        double price, double insurancePrice, double taxPrice, TripDestinationLoyalty loyalty, bool featured)
    {
        var result = TripDestination.Create(Id, destinationId, startDate, endDate, price, insurancePrice, taxPrice,
            loyalty, featured);

        if (result.IsError) return result.Error;

        _tripDestinations.Add(result.Value);

        return result.Value;
    }

    public Result<TripDestination> UpdateDestination(Guid tripDestinationId, Guid destinationId, DateOnly startDate,
        DateOnly endDate, double price, double insurancePrice, double taxPrice, TripDestinationLoyalty loyalty,
        bool featured)
    {
        var tripDestination = _tripDestinations.FirstOrDefault(x => x.Id == tripDestinationId);
        if (tripDestination is null) return TripErrors.TripDestinationNotFound(tripDestinationId);

        var result =
            tripDestination.Update(destinationId, startDate, endDate, price, insurancePrice, taxPrice, loyalty,
                featured);
        if (result.IsError) return result.Error;

        return tripDestination;
    }

    public Result RemoveDestination(Guid tripDestinationId)
    {
        var destination = _tripDestinations.FirstOrDefault(x => x.Id == tripDestinationId);
        if (destination is null) return TripErrors.TripDestinationNotFound(tripDestinationId);

        _tripDestinations.Remove(destination);

        return Result.Ok();
    }

    public Result<TripStop> AddStop(Guid stopId,
        DateTimeOffset beginAt,
        DateTimeOffset endAt,
        TripStopType type,
        string? description)
    {
        var result = TripStop.Create(Id, stopId, beginAt, endAt, type, description);
        if (result.IsError) return result.Error;

        _tripStops.Add(result.Value);

        return result.Value;
    }

    public Result<TripStop> UpdateStop(Guid tripStopId,
        DateTimeOffset beginAt,
        DateTimeOffset endAt,
        TripStopType type,
        string? description)
    {
        var stop = _tripStops.FirstOrDefault(x => x.Id == tripStopId);
        if (stop is null) return TripErrors.TripStopNotFound(tripStopId);

        var result = stop.Update(beginAt, endAt, type, description);
        if (result.IsError) return result.Error;

        return stop;
    }

    public Result RemoveStop(Guid tripStopId)
    {
        var stop = _tripStops.FirstOrDefault(x => x.Id == tripStopId);
        if (stop is null) return TripErrors.TripStopNotFound(tripStopId);

        _tripStops.Remove(stop);

        return Result.Ok();
    }

    public Result<TripBus> AddBus(Guid busId, string name, TripBusDirection direction)
    {
        var result = TripBus.Create(Id, busId, name, direction);
        if (result.IsError) return result.Error;

        if (_tripBuses.Any(x => x.BusId == busId && x.Direction == direction))
            return TripErrors.BusExistsInTrip(busId, direction);

        _tripBuses.Add(result.Value);

        return result.Value;
    }

    public Result<TripBus> UpdateBus(Guid tripBusId, Guid busId, string name, TripBusDirection direction)
    {
        var bus = _tripBuses.FirstOrDefault(x => x.Id == tripBusId);
        if (bus is null) return TripErrors.TripBusNotFound(tripBusId);

        if (bus.Direction != direction && _tripBuses.Any(x => x.BusId == busId && x.Direction == direction))
            return TripErrors.BusExistsInTrip(busId, direction);

        var result = bus.Update(busId, name, direction);
        if (result.IsError) return result.Error;

        return bus;
    }

    public Result RemoveBus(Guid tripBusId)
    {
        var bus = _tripBuses.FirstOrDefault(x => x.Id == tripBusId);
        if (bus is null) return TripErrors.TripBusNotFound(tripBusId);

        _tripBuses.Remove(bus);

        return Result.Ok();
    }
    
    public Result UpdateStatus(TripStatus status)
    {
        if (!Status.CanTransitionTo(status))
        {
            return TripErrors.UpdateStatus(Status, status);
        }

        Status = status;
        if (status == TripStatus.Completed)
        {
            AddDomainEvent(new TripCompletedEvent(Id));
        }

        if (status == TripStatus.Cancelled)
        {
            AddDomainEvent(new TripCancelledEvent(Id));
        }

        return Result.Ok();
    }
}

public enum TripType
{
    [Description("Jedna destinacija")] OneDestination,

    [Description("Različite destinacije")] MultipleDestinations
}

public enum TransportationType
{
    [Description("Autobus")] Bus,
    [Description("Avion")] Airplane
}

public enum TripStatus
{
    [Description("U pripremi")] Draft,
    [Description("Objavljen")] Published,
    [Description("Otkazan")] Cancelled,
    [Description("Završen")] Completed
}

public static class TripErrors
{
    public static Error TravelPartyNotFound(Guid travelPartyId) =>
        Error.NotFound("Trip.TravelPartyNotFound", $"Travel party: {travelPartyId} not found");

    public static Error TravelerNotFound(Guid travelerId) =>
        Error.NotFound("Trip.TravelerNotFound", $"Traveler: {travelerId} not found");

    public static Error PersonTravelerNotFound(Guid personId) =>
        Error.NotFound("Trip.PersonTravelerNotFound", $"Person: {personId} not found in trip");

    public static Error TripDestinationNotFound(Guid tripDestinationId) =>
        Error.NotFound("Trip.TripDestinationNotFound", $"Trip Destination: {tripDestinationId} not found");
    
    public static Error TripDestinationHasTravelers(Guid tripDestinationId) =>
        Error.NotFound("Trip.TripDestinationHasTravelers", $"Trip Destination: {tripDestinationId} has travelers");

    public static Error TripStopNotFound(Guid tripStopId) =>
        Error.NotFound("Trip.TripStopNotFound", $"Trip Stop: {tripStopId} not found");

    public static Error TripBusNotFound(Guid tripBusId) =>
        Error.NotFound("Trip.TripBusNotFound", $"Trip Bus: {tripBusId} not found");

    public static Error PersonExistsInTrip(Guid personId) =>
        Error.Conflict("Trip.PersonExistsInTrip", $"Person: {personId} exists in trip");

    public static Error PersonHasOverlappingTrip(Guid personId) =>
        Error.Conflict("Trip.PersonHasOverlappingTrip", 
            $"Person: {personId} already has a trip that overlaps with dates");

    public static Error BusExistsInTrip(Guid busId, TripBusDirection direction) =>
        Error.Conflict("Trip.BusExistsInTrip", $"Bus: {busId} wtih direction: {direction} exists in trip ");

    public static Error InvalidTitle(string title) =>
        Error.Validation("Trip.InvalidTitle", $"Invalid title: {title}");

    public static Error UpdateStatus(TripStatus oldStatus, TripStatus newStatus) =>
        Error.Validation("Trip.UpdateStatus",
            $"Cannot change status from {oldStatus.ToString()} to: {newStatus.ToString()}");

    public static Error StartRequired =>
        Error.Validation("Trip.StartDateRequired", "Start date is required");

    public static Error EndDateRequired =>
        Error.Validation("Trip.EndDateRequired", "End date is required");

    public static Error StartDateMustBeBeforeOrEqualToEndDate(DateOnly startDate, DateOnly endDate) =>
        Error.Validation("Trip.StartDateMustBeBeforeOrEqualToEndDate",
            $"Start date: {startDate} must be before or equal to end date: {endDate}");

    public static Error TripNotFound(Guid tripId) => Error.NotFound("Trip.NotFound", $"Trip: {tripId} not found");

    public static Error ReservationTimeExpired(Guid tripId) =>
        Error.NotFound("Trip.ReservationTimeExpired", $"Time for reservation is expired for Trip: {tripId}");

    public static Error CannotSpendPoints =>
        Error.Problem("Trip.CannotSpendPoints", "Cannot spend points on this trip");

    public static Error MaximumPointsToSpend(int maxPointsToSpend) => Error.Problem("Trip.MaximumPointsToSpend",
        $"Cannot apply more than {maxPointsToSpend} points");

    public static Error NotEnoughPoints => Error.Problem("Trip.NotEnoughPoints",
        "Not enough points in loyalty card");
}