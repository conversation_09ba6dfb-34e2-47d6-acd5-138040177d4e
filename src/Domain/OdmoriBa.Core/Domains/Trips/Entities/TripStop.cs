using System.ComponentModel;
using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Core.Domains.Trips.Entities;

public sealed class TripStop : EntityBase<Guid>
{
    public Guid TripId { get; private set; }
    public Guid StopId { get; private set; }
    public Stop? Stop { get; init; }
    public DateTimeOffset BeginAt { get; private set; }
    public DateTimeOffset EndAt { get; private set; }
    public TripStopType Type { get; private set; }
    public string? Description { get; private set; }

    private TripStop()
    {
    }

    internal static Result<TripStop> Create(
        Guid tripId,
        Guid stopId,
        DateTimeOffset beginAt,
        DateTimeOffset endAt,
        TripStopType type,
        string? description)
    {
        if (tripId == Guid.Empty) return TripStopErrors.InvalidTripId(tripId);
        if (stopId == Guid.Empty) return TripStopErrors.InvalidStopId(stopId);
        if (beginAt >= endAt) return TripStopErrors.InvalidPeriod(beginAt, endAt);

        return new TripStop
        {
            Id = Guid.CreateVersion7(),
            TripId = tripId,
            StopId = stopId,
            BeginAt = beginAt,
            EndAt = endAt,
            Type = type,
            Description = description
        };
    }

    internal Result Update(
        DateTimeOffset beginAt,
        DateTimeOffset endAt,
        TripStopType type,
        string? description)
    {
        if (beginAt >= endAt) return TripStopErrors.InvalidPeriod(beginAt, endAt);

        BeginAt = beginAt;
        EndAt = endAt;
        Type = type;
        Description = description;

        return Result.Ok();
    }
}

public static class TripStopErrors
{
    public static Error InvalidTripId(Guid tripId) =>
        Error.Validation("TripStop.InvalidTripId", $"Invalid trip ID: {tripId}");
    
    public static Error InvalidStopId(Guid stopId) =>
        Error.Validation("TripStop.InvalidStopId", $"Invalid stop ID: {stopId}");
        
    public static Error InvalidPeriod(DateTimeOffset beginAt, DateTimeOffset endAt) =>
        Error.Validation("TripStop.InvalidPeriod", $"End time {endAt} must be after begin time {beginAt}");
}

public enum TripStopType
{
    [Description("Polazak")] Departure,
    [Description("Pauza")] Break,
    [Description("Povratak")] Return
}