using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.Core.Domains.Trips.Entities;

public sealed class TripDestination : EntityBase<Guid>
{
    public Guid TripId { get; private set; }

    public Guid DestinationId { get; private set; }
    public Destination? Destination { get; init; }

    public DateOnly StartDate { get; private set; }
    public DateOnly EndDate { get; private set; }
    public double Price { get; private set; }
    public double InsurancePrice { get; private set; }
    public double TaxPrice { get; private set; }
    public bool Featured { get; set; }

    public TripDestinationLoyalty Loyalty { get; private set; } = new();

    private readonly List<Itinerary> _itineraries = [];
    public IReadOnlyCollection<Itinerary> Itineraries => _itineraries.AsReadOnly();

    private TripDestination()
    {
    }

    internal static Result<TripDestination> Create(Guid tripId, Guid destinationId, DateOnly startDate,
        DateOnly endDate, double price, double insurancePrice, double taxPrice, TripDestinationLoyalty loyalty, bool featured)
    {
        if (tripId == Guid.Empty) return TripDestinationErrors.InvalidTripId(tripId);
        if (startDate == default) return TripDestinationErrors.InvalidStartDate(startDate);
        if (endDate == default) return TripDestinationErrors.InvalidEndDate(endDate);
        if (startDate > endDate) return TripDestinationErrors.InvalidDateRange(startDate, endDate);
        if (destinationId == Guid.Empty) return TripDestinationErrors.InvalidDestinationId(destinationId);
        if (price < 0) return TripDestinationErrors.InvalidPrice(price);
        if (insurancePrice < 0) return TripDestinationErrors.InvalidInsurancePrice(insurancePrice);
        if (taxPrice < 0) return TripDestinationErrors.InvalidTaxPrice(taxPrice);
        if (loyalty == null!) return TripDestinationErrors.LoyaltyRequired;
        if (loyalty.Points < 0) return TripDestinationErrors.InvalidLoyaltyPoints(loyalty.Points);
        if (loyalty.MaximumPointsToSpend < 0) return TripDestinationErrors.InvalidLoyaltyMaximumPointsToSpend(loyalty.MaximumPointsToSpend);

        return new TripDestination
        {
            Id = Guid.CreateVersion7(),
            TripId = tripId,
            DestinationId = destinationId,
            StartDate = startDate,
            EndDate = endDate,
            Price = price,
            InsurancePrice = insurancePrice,
            TaxPrice = taxPrice,
            Loyalty = loyalty,
            Featured = featured
        };
    }

    internal Result<TripDestination> Update(Guid destinationId, DateOnly startDate, DateOnly endDate, double price,
        double insurancePrice, double taxPrice, TripDestinationLoyalty loyalty, bool featured)
    {
        if (destinationId == Guid.Empty) return TripDestinationErrors.InvalidDestinationId(destinationId);
        if (startDate == default) return TripDestinationErrors.InvalidStartDate(startDate);
        if (endDate == default) return TripDestinationErrors.InvalidEndDate(endDate);
        if (startDate > endDate) return TripDestinationErrors.InvalidDateRange(startDate, endDate);
        if (price < 0) return TripDestinationErrors.InvalidPrice(price);
        if (insurancePrice < 0) return TripDestinationErrors.InvalidInsurancePrice(insurancePrice);
        if (taxPrice < 0) return TripDestinationErrors.InvalidTaxPrice(taxPrice);
        if (loyalty == null!) return TripDestinationErrors.LoyaltyRequired;
        if (loyalty.Points < 0) return TripDestinationErrors.InvalidLoyaltyPoints(loyalty.Points);
        if (loyalty.MaximumPointsToSpend < 0) return TripDestinationErrors.InvalidLoyaltyMaximumPointsToSpend(loyalty.MaximumPointsToSpend);

        DestinationId = destinationId;
        StartDate = startDate;
        EndDate = endDate;
        Price = price;
        InsurancePrice = insurancePrice;
        TaxPrice = taxPrice;
        Loyalty = loyalty;
        Featured = featured;

        return this;
    }

    public Result<Itinerary> AddItinerary(DateTimeOffset dateTime, string title, string description)
    {
        var result = Itinerary.Create(Id, dateTime, title, description);
        if (result.IsError) return result.Error;

        _itineraries.Add(result.Value);

        return result.Value;
    }

    public Result<Itinerary> UpdateItinerary(Guid itineraryId, DateTimeOffset dateTime, string title,
        string description)
    {
        var itinerary = _itineraries.FirstOrDefault(x => x.Id == itineraryId);
        if (itinerary is null) return TripDestinationErrors.ItineraryNotFound(itineraryId);

        var result = itinerary.Update(dateTime, title, description);
        if (result.IsError) return result.Error;

        return itinerary;
    }

    public Result RemoveItinerary(Guid itineraryId)
    {
        var itinerary = _itineraries.FirstOrDefault(x => x.Id == itineraryId);
        if (itinerary is null) return TripDestinationErrors.ItineraryNotFound(itineraryId);

        _itineraries.Remove(itinerary);

        return Result.Ok();
    }
}

public sealed class TripDestinationLoyalty
{
    public int Points { get; set; }
    public bool CanSpendPoints { get; set; }
    public int MaximumPointsToSpend { get; set; }
}

public static class TripDestinationErrors
{
    public static Error InvalidTripId(Guid tripId) =>
        Error.Validation("TripDestination.InvalidTripId", $"Invalid trip ID: {tripId}");

    public static Error InvalidDestinationId(Guid destinationId) =>
        Error.Validation("TripDestination.InvalidDestinationId", $"Invalid destination ID: {destinationId}");

    public static Error InvalidStartDate(DateOnly date) =>
        Error.Validation("TripDestination.InvalidStartDate", $"Invalid start date: {date}");

    public static Error InvalidEndDate(DateOnly date) =>
        Error.Validation("TripDestination.InvalidEndDate", $"Invalid end date: {date}");

    public static Error InvalidDateRange(DateOnly startDate, DateOnly endDate) =>
        Error.Validation("TripDestination.InvalidDateRange",
            $"Start date {startDate} cannot be after end date {endDate}");

    public static Error InvalidPrice(double price) =>
        Error.Validation("TripDestination.InvalidPrice", $"Price cannot be negative: {price}");

    public static Error LoyaltyRequired =>
        Error.Validation("TripDestination.LoyaltyRequired", "Loyalty is required");
    
    public static Error InvalidLoyaltyPoints(int points) =>
        Error.Validation("TripDestination.Loyalty.InvalidLoyaltyPoints",
            $"Loyalty points cannot be negative: {points}");

    public static Error InvalidLoyaltyMaximumPointsToSpend(int points) =>
        Error.Validation("TripDestination.Loyalty.InvalidLoyaltyMaximumPointsToSpend",
            $"Loyalty MaximumPointsToSpend cannot be negative: {points}");

    public static Error InvalidInsurancePrice(double price) =>
        Error.Validation("TripDestination.InvalidInsurancePrice", $"Insurance price cannot be negative: {price}");

    public static Error InvalidTaxPrice(double price) =>
        Error.Validation("TripDestination.InvalidTaxPrice", $"Tax price cannot be negative: {price}");

    public static Error ItineraryNotFound(Guid itineraryId) =>
        Error.NotFound("TripDestination.ItineraryNotFound", $"Itinerary not found with ID: {itineraryId}");
}