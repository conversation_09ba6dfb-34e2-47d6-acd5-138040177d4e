using OdmoriBa.Core.Domains.Transportations.Entities;

namespace OdmoriBa.Core.Domains.Trips.Entities;

public sealed class TripBus : EntityBase<Guid>
{
    public string Name { get; private set; } = null!;
    public Guid TripId { get; private set; }
    public Guid BusId { get; private set; }
    public Bus? Bus { get; init; }
    public TripBusDirection Direction { get; private set; }

    private TripBus()
    {
    }

    public static Result<TripBus> Create(Guid tripId, Guid busId, string name, TripBusDirection direction)
    {
        if (string.IsNullOrWhiteSpace(name)) return TripBusErrors.InvalidName(name);
        if (tripId == Guid.Empty) return TripBusErrors.InvalidTripId(tripId);
        if (busId == Guid.Empty) return TripBusErrors.InvalidBusId(busId);

        return new TripBus
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            TripId = tripId,
            BusId = busId,
            Direction = direction
        };
    }

    public Result Update(Guid busId, string name, TripBusDirection direction)
    {
        if (string.IsNullOrWhiteSpace(name)) return TripBusErrors.InvalidName(name);
        if (busId == Guid.Empty) return TripBusErrors.InvalidBusId(busId);

        Name = name;
        BusId = busId;
        Direction = direction;

        return Result.Ok();
    }
}

public static class TripBusErrors
{
    public static Error InvalidTripId(Guid tripId) =>
        Error.Validation("TripBus.InvalidTripId", $"Invalid trip ID: {tripId}");
    public static Error InvalidBusId(Guid busId) =>
        Error.Validation("TripBus.InvalidBusId", $"Invalid bus ID: {busId}");
    public static Error InvalidName(string name) =>
        Error.Validation("TripBus.InvalidName", $"Invalid name: {name}");
}

public enum TripBusDirection
{
    [LocalizedEnum] Departure,
    [LocalizedEnum] Return
}