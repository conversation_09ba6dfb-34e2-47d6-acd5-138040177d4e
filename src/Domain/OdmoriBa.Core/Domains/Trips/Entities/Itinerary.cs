namespace OdmoriBa.Core.Domains.Trips.Entities;

public sealed class Itinerary : EntityBase<Guid>
{
    public Guid TripDestinationId { get; private set; }
    public DateTimeOffset DateTime { get; private set; }
    public string Title { get; private set; } = null!;
    public string Description { get; private set; } = null!;

    private Itinerary()
    {
    }

    internal static Result<Itinerary> Create(Guid tripDestinationId, DateTimeOffset dateTime, string title,
        string description)
    {
        if (tripDestinationId == Guid.Empty) return ItineraryErrors.InvalidTripDestinationId(tripDestinationId);
        if (dateTime == default) return ItineraryErrors.DateTimeRequired;
        if (string.IsNullOrWhiteSpace(title)) return ItineraryErrors.InvalidTitle(title);
        if (string.IsNullOrWhiteSpace(description)) return ItineraryErrors.InvalidDescription(description);

        return new Itinerary
        {
            Id = Guid.CreateVersion7(),
            TripDestinationId = tripDestinationId,
            DateTime = dateTime,
            Title = title,
            Description = description
        };
    }

    internal Result Update(DateTimeOffset dateTime, string title, string description)
    {
        if (dateTime == default) return ItineraryErrors.DateTimeRequired;
        if (string.IsNullOrWhiteSpace(title)) return ItineraryErrors.InvalidTitle(title);
        if (string.IsNullOrWhiteSpace(description)) return ItineraryErrors.InvalidDescription(description);

        DateTime = dateTime;
        Title = title;
        Description = description;

        return Result.Ok();
    }
}

public static class ItineraryErrors
{
    public static Error InvalidTripDestinationId(Guid tripDestinationId) =>
        Error.Validation("Itinerary.InvalidTripDestinationId", $"Invalid trip destination ID: {tripDestinationId}");
    public static Error InvalidTitle(string title) =>
        Error.Validation("Itinerary.InvalidTitle", $"Invalid title: {title}");
    
    public static Error InvalidDescription(string title) =>
        Error.Validation("Itinerary.InvalidDescription", $"Invalid title: {title}");

    public static Error DateTimeRequired =>
        Error.Validation("Itinerary.DateTimeRequired", "Date and time is required");
}