using OdmoriBa.Core.Domains.Travelers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Core.Domains.Trips.Extensions;

public static class TripExtensions
{
    public static bool CanTransitionTo(this TripStatus from, TripStatus to)
    {
        return from switch
        {
            TripStatus.Draft => to == TripStatus.Published,
            TripStatus.Published => to is TripStatus.Draft or TripStatus.Cancelled or TripStatus.Completed,
            _ => false
        };
    }
    
    public static bool CanTransitionTo(this TravelerStatus from, TravelerStatus to)
    {
        return from switch
        {
            TravelerStatus.Requested => true,
            TravelerStatus.Draft => to is TravelerStatus.Confirmed or TravelerStatus.Cancelled,
            TravelerStatus.Confirmed => to is TravelerStatus.Cancelled,
            _ => false
        };
    }
}