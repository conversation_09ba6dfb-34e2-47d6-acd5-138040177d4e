using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Notifications.Entities;
using OdmoriBa.Core.Domains.Outbox.Events;

namespace OdmoriBa.Core.Domains.Outbox.Entities;

public sealed class PushNotificationOutbox : EntityBase<Guid>
{
    public Guid UserId { get; private set; }
    public PushNotificationType? Type { get; set; }
    public string Token { get; private set; } = null!;
    public string? Topic { get; private set; }
    public string Title { get; private set; } = null!;
    public string Body { get; private set; } = null!;
    public DeviceType? DeviceType { get; private set; }
    public Dictionary<string, string>? AdditionalData { get; private set; }
    public OutboxStatus Status { get; private set; }
    public string? MessageId { get; set; }
    public string? ErrorCode { get; private set; }
    public string? ErrorMessage { get; private set; }
    public int RetryCount { get; private set; }
    public int MaxRetryCount { get; private set; } = 3;

    private PushNotificationOutbox()
    {
    }

    public static PushNotificationOutbox Create(
        PushNotificationType? type,
        Guid userId, string? token, string? topic, string title, string body,
        DeviceType deviceType, Dictionary<string, string>? additionalData, int maxRetryCount = 3)
    {
        return new PushNotificationOutbox
        {
            Id = Guid.CreateVersion7(),
            Type = type,
            UserId = userId,
            Token = token,
            Topic = topic,
            Title = title,
            Body = body,
            DeviceType = deviceType,
            AdditionalData = additionalData,
            Status = OutboxStatus.Pending,
            MaxRetryCount = maxRetryCount,
        };
    }

    public void MarkAsSent(string messageId)
    {
        Status = OutboxStatus.Sent;
        MessageId = messageId;
    }

    public void MarkAsFailed(string errorCode, string errorMessage)
    {
        Status = OutboxStatus.Failed;
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
        AddDomainEvent(new PushNotificationFailedDomainEvent(UserId, Token, errorCode, errorMessage));
    }

    public void IncrementRetryCount()
    {
        RetryCount++;
    }
}

public enum OutboxStatus
{
    Pending,
    Sent,
    Failed
}