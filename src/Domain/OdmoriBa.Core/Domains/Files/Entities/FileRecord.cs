namespace OdmoriBa.Core.Domains.Files.Entities;

public sealed class FileRecord : EntityBase<Guid>
{
    public string Name { get; private set; } = null!;
    public string Url { get; private set; } = null!;
    public string ContentType { get; private set; } = null!;
    public long Size { get; private set; }
    public FileRecordType? Type { get; private set; }

    private FileRecord()
    {
    }

    public static Result<FileRecord> Create(string name, string url, string contentType, long size, FileRecordType type)
    {
        if (string.IsNullOrWhiteSpace(name)) return FileRecordErrors.InvalidName(name);
        if (string.IsNullOrWhiteSpace(url)) return FileRecordErrors.InvalidUrl(url);
        if (string.IsNullOrWhiteSpace(contentType)) return FileRecordErrors.InvalidContentType(contentType);
        if (size <= 0) return FileRecordErrors.InvalidSize(size);

        return new FileRecord
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            Url = url,
            ContentType = contentType,
            Size = size,
            Type = type
        };
    }
}

public enum FileRecordType
{
    DestinationImage,
    DestinationCover,
}

public static class FileRecordErrors
{
    public static Error InvalidId(Guid id) =>
        Error.Validation("FileRecord.InvalidId", $"Invalid file record ID: {id}");

    public static Error InvalidName(string name) =>
        Error.Validation("FileRecord.InvalidName", $"Invalid file record name: {name}");

    public static Error InvalidUrl(string url) =>
        Error.Validation("FileRecord.InvalidUrl", $"Invalid file record url: {url}");

    public static Error InvalidContentType(string name) =>
        Error.Validation("FileRecord.InvalidFileRecordName", $"Invalid file record name: {name}");

    public static Error InvalidSize(long size) =>
        Error.Validation("FileRecord.InvalidSize", $"Invalid file record size: {size}");

    public static Error FileRecordNotFound(Guid fileRecordId) =>
        Error.NotFound("FileRecord.NotFound", $"File record: {fileRecordId} not found");
}