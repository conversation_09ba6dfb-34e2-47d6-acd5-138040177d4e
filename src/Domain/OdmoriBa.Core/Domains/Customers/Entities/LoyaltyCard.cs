namespace OdmoriBa.Core.Domains.Customers.Entities;

public sealed class LoyaltyCard : EntityBase<Guid>, ISoftDelete
{
    public Person? Person { get; init; }
    public int TotalPoints { get; private set; }

    private readonly List<LoyaltyPointTransaction> _pointTransactions = [];
    public IReadOnlyList<LoyaltyPointTransaction> PointTransactions => _pointTransactions.AsReadOnly();
    public bool IsDeleted { get; private set; }
    public DateTimeOffset? DeletedAt { get; private set; }

    private LoyaltyCard()
    {
    }

    private void CalculateTotalPoints()
    {
        TotalPoints = _pointTransactions.Sum(s => s.Value);
    }

    internal static Result<LoyaltyCard> Create(Guid personId)
    {
        if (personId == Guid.Empty) return PersonErrors.InvalidId(personId);

        return new LoyaltyCard
        {
            Id = personId,
            TotalPoints = 0,
        };
    }

    internal Result EarnPoints(int value, LoyaltyPointTransactionType type, Guid? tripDestinationId)
    {
        if (value == 0) return LoyaltyCardErrors.InvalidPointsValue;

        var transactionResult = LoyaltyPointTransaction.Create(Id,
            value,
            type,
            tripDestinationId);

        if (transactionResult.IsError) return transactionResult.Error;

        _pointTransactions.Add(transactionResult.Value);
        CalculateTotalPoints();

        return Result.Ok();
    }

    internal Result RedeemPoints(int value, LoyaltyPointTransactionType type, Guid? tripDestinationId)
    {
        if (value == 0) return LoyaltyCardErrors.InvalidPointsValue;
        if (TotalPoints < value) return LoyaltyCardErrors.InsufficientPoints;

        var transactionResult = LoyaltyPointTransaction.Create(Id,
            -value,
            type,
            tripDestinationId);

        if (transactionResult.IsError) return transactionResult.Error;

        _pointTransactions.Add(transactionResult.Value);
        CalculateTotalPoints();

        return Result.Ok();
    }
}

public static class LoyaltyCardErrors
{
    public static Error InvalidPointsValue =>
        Error.Problem("LoyaltyCard.InvalidPointsValue", "Number of points cannot be 0");
    
    public static Error InsufficientPoints =>
        Error.Validation("LoyaltyCard.InsufficientPoints", "Insufficient points");

    public static Error NotFound(Guid personId) => 
        Error.NotFound("LoyaltyCard.NotFound", $"Loyalty card not found for person {personId}");
}