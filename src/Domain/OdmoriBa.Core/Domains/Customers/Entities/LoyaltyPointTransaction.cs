using System.ComponentModel;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Core.Domains.Customers.Entities;

public sealed class LoyaltyPointTransaction : EntityBase<Guid>, ISoftDelete
{
    public Guid LoyaltyCardId { get; private set; }
    public int Value { get; private set; }
    public LoyaltyPointTransactionType Type { get; private set; }

    public Guid? TripDestinationId { get; private set; }
    public TripDestination? TripDestination { get; init; }
    
    public bool IsDeleted { get; private set;  }
    public DateTimeOffset? DeletedAt { get; private set; }

    private LoyaltyPointTransaction()
    {
    }

    internal static Result<LoyaltyPointTransaction> Create(
        Guid loyaltyCardId,
        int points,
        LoyaltyPointTransactionType type,
        Guid? tripDestinationId)
    {
        return new LoyaltyPointTransaction
        {
            Id = Guid.CreateVersion7(),
            LoyaltyCardId = loyaltyCardId,
            Value = points,
            Type = type,
            TripDestinationId = tripDestinationId
        };
    }
}

public enum LoyaltyPointTransactionType
{
    [Description("Putovanje")]
    Trip = 0,
    [Description("Putovanje")]
    Gift = 1,
    [Description("Popust")]
    Discount = 2
}