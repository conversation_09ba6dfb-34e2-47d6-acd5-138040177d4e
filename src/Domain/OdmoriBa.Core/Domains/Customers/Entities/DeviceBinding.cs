namespace OdmoriBa.Core.Domains.Customers.Entities;

public sealed class DeviceBinding : EntityBase<Guid>, ISoftDelete
{
    public Guid UserId { get; private set; }
    public User? User { get; init; }
    public string PushNotificationToken { get; private set; } = null!;

    /// <summary>
    /// IOS/Android
    /// </summary>
    public DeviceType DeviceType { get; private set; }

    /// <summary>
    /// Android ID, iOS identifierForVendor
    /// </summary>
    public string DeviceId { get; private set; } = null!;

    /// <summary>
    /// Version of app (eg. 1.0.0)
    /// </summary>
    public string AppVersion { get; private set; } = null!;

    /// <summary>
    /// OS version (eg. 18.1)
    /// </summary>
    public string OsVersion { get; private set; } = null!;

    /// <summary>
    /// Device model (eg. iPhone 15 Pro)
    /// </summary>
    public string DeviceModel { get; private set; } = null!;
    
    public bool IsDeleted { get; private set;  }
    public DateTimeOffset? DeletedAt { get; private set; }

    private DeviceBinding()
    {
    }

    internal static Result<DeviceBinding> Create(Guid userId, string deviceId, DeviceType deviceType,
        string appVersion, string osVersion, string deviceModel, string pushNotificationToken)
    {
        if (string.IsNullOrWhiteSpace(pushNotificationToken))
            return DeviceBindingErrors.DeviceTokenRequired;

        if (string.IsNullOrWhiteSpace(appVersion))
            return DeviceBindingErrors.AppVersionRequired;

        if (string.IsNullOrWhiteSpace(osVersion))
            return DeviceBindingErrors.OsVersionRequired;

        if (string.IsNullOrWhiteSpace(deviceModel))
            return DeviceBindingErrors.DeviceModelRequired;


        return new DeviceBinding
        {
            Id = Guid.CreateVersion7(),
            UserId = userId,
            DeviceId = deviceId,
            PushNotificationToken = pushNotificationToken,
            DeviceType = deviceType,
            AppVersion = appVersion,
            OsVersion = osVersion,
            DeviceModel = deviceModel
        };
    }

    internal Result Update(string appVersion, string osVersion, string deviceModel, string pushNotificationToken)
    {
        if (string.IsNullOrWhiteSpace(pushNotificationToken))
            return DeviceBindingErrors.DeviceTokenRequired;

        if (string.IsNullOrWhiteSpace(appVersion))
            return DeviceBindingErrors.AppVersionRequired;

        if (string.IsNullOrWhiteSpace(osVersion))
            return DeviceBindingErrors.OsVersionRequired;

        if (string.IsNullOrWhiteSpace(deviceModel))
            return DeviceBindingErrors.DeviceModelRequired;


        PushNotificationToken = pushNotificationToken;
        AppVersion = appVersion;
        OsVersion = osVersion;
        DeviceModel = deviceModel;

        return Result.Ok();
    }
}

public enum DeviceType
{
    Ios,
    Android
}

public static class DeviceBindingErrors
{
    public static Error DeviceTokenRequired =>
        Error.Validation("DeviceBinding.DeviceTokenRequired", "Device token is required");

    public static Error DeviceTypeRequired =>
        Error.Validation("DeviceBinding.DeviceTypeRequired", "Device type is required");

    public static Error AppVersionRequired =>
        Error.Validation("DeviceBinding.AppVersionRequired", "App version is required");

    public static Error OsVersionRequired =>
        Error.Validation("DeviceBinding.OsVersionRequired", "OS version is required");

    public static Error DeviceModelRequired =>
        Error.Validation("DeviceBinding.DeviceModelRequired", "Device model is required");
}