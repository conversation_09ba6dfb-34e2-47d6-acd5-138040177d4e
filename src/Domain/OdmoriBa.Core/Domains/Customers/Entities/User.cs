using OdmoriBa.Core.Common.ValueObjects;

namespace OdmoriBa.Core.Domains.Customers.Entities;

public sealed class User : EntityBase<Guid>, ISoftDelete
{
    public string IdentityId { get; private set; } = null!;
    public string Email { get; private set; } = null!;
    public Phone Phone { get; private set; } = null!;
    public UserStatus Status { get; private set; }
    public UserRole Role { get; private set; }
    public Person? Person { get; init; }
    public string? SignInProvider { get; private set; }

    private readonly List<DeviceBinding> _devices = [];
    public IReadOnlyCollection<DeviceBinding> Devices => _devices.AsReadOnly();

    public bool IsDeleted { get; private set; }
    public DateTimeOffset? DeletedAt { get; private set; }

    private User()
    {
    }

    public static Result<User> CreateWithPerson(string identityId, UserRole role, string? signInProvider,
        string firstName, string lastName, string email, Phone phone,
        DateOnly birthDate, string? idDocument, string? countryCode, string? city, string? address)
    {
        var person = Person.Create(firstName, lastName, email, phone, birthDate, idDocument, countryCode, city,
            address);

        if (person.IsError) return person.Error;

        if (string.IsNullOrWhiteSpace(identityId)) return UserErrors.IdentityIdRequired;
        if (string.IsNullOrWhiteSpace(email)) return PersonErrors.InvalidEmail(email);
        if (!phone.IsValid()) return PersonErrors.InvalidPhone(phone);

        return new User
        {
            Id = person.Value.Id,
            Person = person.Value,
            IdentityId = identityId,
            Email = email,
            Phone = phone,
            Role = role,
            Status = UserStatus.Active,
            SignInProvider = signInProvider
        };
    }

    public Result UpdatePhone(Phone phone)
    {
        if (!phone.IsValid()) return PersonErrors.InvalidPhone(phone);
        Phone = phone;
        var result = Person!.UpdatePhone(phone);
        if (result.IsError) return result.Error;
        return Result.Ok();
    }

    public Result UpdateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email)) return PersonErrors.InvalidEmail(email);
        Email = email;
        var result = Person!.UpdateEmail(email);
        if (result.IsError) return result.Error;
        return Result.Ok();
    }

    public Result AddOrUpdateDeviceBinding(string deviceId, DeviceType deviceType,
        string appVersion, string osVersion, string deviceModel, string pushNotificationToken)
    {
        var device = _devices.FirstOrDefault(s => s.DeviceId == deviceId);

        if (device is null)
        {
            var result = DeviceBinding.Create(Id, deviceId, deviceType, appVersion, osVersion, deviceModel,
                pushNotificationToken);
            if (result.IsError) return result.Error;
            _devices.Add(result.Value);
        }
        else
        {
            var result = device.Update(appVersion, osVersion, deviceModel, pushNotificationToken);
            if (result.IsError) return result.Error;
        }

        return Result.Ok();
    }
}

public enum UserStatus
{
    [LocalizedEnum] Active,
    [LocalizedEnum] Inactive,
}

public enum UserRole
{
    User,
    Admin,
}

public static class UserErrors
{
    public static Error IdentityIdRequired =>
        Error.Validation("User.IdentityIdRequired", "Identity id is required");

    public static Error UserNotFound(Guid userId) =>
        Error.NotFound("User.UserNotFound", $"User {userId} not found");
    
    public static Error EmailExists =>
        Error.Conflict("User.EmailExists", "User with provided email exists in the system");
    
    public static Error PhoneExists =>
        Error.Conflict("User.PhoneExists", "User with provided phone number exists in the system");
}