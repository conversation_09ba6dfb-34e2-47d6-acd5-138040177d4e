using OdmoriBa.Core.Common.ValueObjects;

namespace OdmoriBa.Core.Domains.Customers.Entities;

public sealed class Person : EntityBase<Guid>, ISoftDelete
{
    public string FirstName { get; private set; } = null!;
    public string LastName { get; private set; } = null!;
    public string? Email { get; private set; }
    public Phone? Phone { get; private set; }
    public string? IdDocument { get; private set; }
    public DateOnly BirthDate { get; private set; }
    public string? CountryCode { get; private set; }
    public string? City { get; private set; }
    public string? Address { get; private set; }
    public User? User { get; init; }
    public LoyaltyCard LoyaltyCard { get; private set; } = null!;
    public bool IsDeleted { get; private set; }
    public DateTimeOffset? DeletedAt { get; private set; }

    private Person()
    {
    }

    public static Result<Person> Create(string firstName, string lastName, string? email, Phone? phone,
        DateOnly birthDate, string? idDocument, string? countryCode, string? city, string? address)
    {
        if (string.IsNullOrWhiteSpace(firstName)) return PersonErrors.InvalidFirstName(firstName);
        if (string.IsNullOrWhiteSpace(lastName)) return PersonErrors.InvalidLastName(lastName);
        if (birthDate == default) return PersonErrors.InvalidBirthDate(birthDate);

        var id = Guid.CreateVersion7();
        
        var loyaltyCard = LoyaltyCard.Create(id);
        if (loyaltyCard.IsError) return loyaltyCard.Error;

        return new Person
        {
            Id = id,
            FirstName = firstName,
            LastName = lastName,
            Email = email,
            Phone = phone,
            BirthDate = birthDate,
            IdDocument = idDocument,
            CountryCode = countryCode,
            City = city,
            Address = address,
            LoyaltyCard = loyaltyCard.Value,
        };
    }

    public Result Update(string firstName, string lastName, string? email, Phone? phone,
        DateOnly birthDate, string? idDocument, string? countryCode, string? city, string? address)
    {
        if (string.IsNullOrWhiteSpace(firstName)) return PersonErrors.InvalidFirstName(firstName);
        if (string.IsNullOrWhiteSpace(lastName)) return PersonErrors.InvalidLastName(lastName);
        if (birthDate == default) return PersonErrors.InvalidBirthDate(birthDate);

        FirstName = firstName;
        LastName = lastName;
        Email = email;
        Phone = phone;
        BirthDate = birthDate;
        IdDocument = idDocument;
        CountryCode = countryCode;
        City = city;
        Address = address;

        return Result.Ok();
    }

    public Result UpdateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email)) return PersonErrors.InvalidEmail(email);
        Email = email;
        return Result.Ok();
    }

    public Result UpdatePhone(Phone phone)
    {
        if (!phone.IsValid()) return PersonErrors.InvalidPhone(phone);
        Phone = phone;
        return Result.Ok();
    }
    
    public Result EarnPoints(int value, LoyaltyPointTransactionType type, Guid? tripDestinationId)
    {
        var result = LoyaltyCard.EarnPoints(value, type, tripDestinationId);
        if (result.IsError) return result.Error;
        return Result.Ok();
    }

    public Result RedeemPoints(int value, LoyaltyPointTransactionType type, Guid? tripDestinationId)
    {
        var result = LoyaltyCard.RedeemPoints(value, type, tripDestinationId);
        if (result.IsError) return result.Error;

        return Result.Ok();
    }
}

public static class PersonErrors
{
    public static Error InvalidId(Guid personId) =>
        Error.Validation("Person.InvalidId", $"Invalid person ID: {personId}");

    public static Error InvalidFirstName(string firstName) =>
        Error.Validation("Person.InvalidFirstName", $"Invalid first name: {firstName}");

    public static Error InvalidLastName(string lastName) =>
        Error.Validation("Person.InvalidLastName", $"Invalid first name: {lastName}");

    public static Error InvalidBirthDate(DateOnly birthDate) =>
        Error.Validation("Person.InvalidBirthDate", $"Invalid birthdate: {birthDate}");

    public static Error InvalidPhone(Phone phone) =>
        Error.Validation("Person.InvalidPhone", $"Invalid phone: {phone}");
    
    public static Error InvalidEmail(string email) =>
        Error.Validation("Person.InvalidEmail", $"Invalid email: {email}");

    public static Error PersonNotFound(Guid personId) => 
        Error.NotFound("Person.NotFound", $"Person: {personId} not found");
}