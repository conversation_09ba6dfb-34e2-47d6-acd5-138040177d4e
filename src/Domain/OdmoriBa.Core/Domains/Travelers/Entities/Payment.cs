using System.ComponentModel;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.Core.Domains.Travelers.Entities;

public sealed class Payment : EntityBase<Guid>, ISoftDelete
{
    public Guid TravelPartyId { get; private set; }
    public Guid PaidByPersonId { get; private set; }
    public Person? PaidByPerson { get; init; }
    public double Amount { get; private set; }
    public PaymentType Type { get; private set; }
    public DateTime PaidAt { get; private set; }
    public string? Note { get; private set; }
    public bool IsDeleted { get; private set;  }
    public DateTimeOffset? DeletedAt { get; private set; }

    private Payment()
    {
    }

    internal static Result<Payment> Create(
        Guid travelerPartyId,
        Guid paidByPersonId,
        double amount,
        PaymentType type,
        DateTime paidAt, string? note)
    {
        if (travelerPartyId == Guid.Empty) return PaymentErrors.InvalidTravelPartyId(travelerPartyId);
        if (paidByPersonId == Guid.Empty) return PaymentErrors.InvalidPaidByPersonId(paidByPersonId);
        if (amount <= 0) return PaymentErrors.InvalidAmount(amount);
        if (paidAt == default) return PaymentErrors.PaidAtRequired;

        return new Payment
        {
            Id = Guid.CreateVersion7(),
            TravelPartyId = travelerPartyId,
            PaidByPersonId = paidByPersonId,
            Amount = amount,
            Type = type,
            PaidAt = paidAt,
            Note = note
        };
    }

    internal Result Update(
        Guid paidByPersonId,
        double amount,
        PaymentType type,
        DateTime paidAt,
        string? note)
    {
        if (paidByPersonId == Guid.Empty) return PaymentErrors.InvalidPaidByPersonId(paidByPersonId);
        if (amount <= 0) return PaymentErrors.InvalidAmount(amount);
        if (paidAt == default) return PaymentErrors.PaidAtRequired;

        PaidByPersonId = paidByPersonId;
        Amount = amount;
        Type = type;
        PaidAt = paidAt;
        Note = note;

        return Result.Ok();
    }
}

public enum PaymentType
{
    [Description("Virman")] Bank,
    [Description("Keš")] Cash
}

public static class PaymentErrors
{
    public static Error InvalidPaidByPersonId(Guid personId) =>
        Error.Validation("Payment.InvalidPaidByPersonId", $"Invalid paid by person ID: {personId}");

    public static Error InvalidTravelPartyId(Guid travelPartyId) =>
        Error.Validation("Payment.InvalidTravelPartyId", $"Invalid travel party ID: {travelPartyId}");

    public static Error PaidAtRequired =>
        Error.Validation("Payment.PaidAtRequired", "Paid At is required");

    public static Error InvalidAmount(double amount) =>
        Error.Validation("Payment.InvalidAmount", $"Invalid amount: {amount}");
}