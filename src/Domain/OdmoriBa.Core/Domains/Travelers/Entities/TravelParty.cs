using System.ComponentModel.DataAnnotations.Schema;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.Core.Domains.Travelers.Entities;

public sealed class TravelParty : EntityBase<Guid>, IAggregateRoot, ISoftDelete
{
    public Guid TripId { get; private set; }
    public Trip? Trip { get; init; }
    public Guid MainContactId { get; private set; }
    public Person? MainContact { get; init; }
    public string? Note { get; private set; }
    public string? RequestNote { get; private set; }

    public Guid TripDestinationId { get; private set; }
    public TripDestination TripDestination { get; init; } = null!;

    private readonly List<Traveler> _travelers = [];
    public IReadOnlyCollection<Traveler> Travelers => _travelers.AsReadOnly();

    private readonly List<Payment> _payments = [];
    public IReadOnlyCollection<Payment> Payments => _payments.AsReadOnly();

    [NotMapped] public double TotalPrice => Travelers.Sum(x => x.Price + x.InsurancePrice + x.TaxPrice - x.Discount);
    [NotMapped] public double TotalPaid => Payments.Sum(x => x.Amount);
    [NotMapped] public double TotalDue => TotalPrice - TotalPaid;
    
    public bool IsDeleted { get; private set; }
    public DateTimeOffset? DeletedAt { get; private set; }

    private TravelParty()
    {
    }

    public static Result<TravelParty> Create(Guid tripId,
        Guid tripDestinationId,
        Guid mainContactId,
        string? note,
        string? requestNote)
    {
        if (tripId == Guid.Empty) return TravelPartyErrors.InvalidTripId(tripId);
        if (tripDestinationId == Guid.Empty) return TravelPartyErrors.InvalidTripDestinationId(tripDestinationId);
        if (mainContactId == Guid.Empty) return TravelPartyErrors.InvalidMainContactId(mainContactId);
        return new TravelParty
        {
            Id = Guid.CreateVersion7(),
            TripId = tripId,
            TripDestinationId = tripDestinationId,
            MainContactId = mainContactId,
            Note = note,
            RequestNote = requestNote
        };
    }

    public Result Update(Guid mainContactId, string? note)
    {
        if (mainContactId == Guid.Empty) return TravelPartyErrors.InvalidMainContactId(mainContactId);

        MainContactId = mainContactId;
        Note = note;

        return Result.Ok();
    }

    public Result<Traveler> AddTraveler(Guid personId, double price, double insurancePrice,
        double taxPrice, double discount, string? note, Guid? departurePointId, Guid? returnDeparturePointId,
        TravelerStatus status = TravelerStatus.Draft)
    {
        var travelerResult = Traveler.Create(Id, personId, price, insurancePrice, taxPrice, discount,
            note, departurePointId, returnDeparturePointId);

        if (travelerResult.IsError) return travelerResult.Error;

        _travelers.Add(travelerResult.Value);
        return travelerResult.Value;
    }

    public Result<Traveler> UpdateTraveler(Guid travelerId, double price, double insurancePrice, double taxPrice, 
        double discount, string? note, Guid? departurePointId, Guid? returnDeparturePointId)
    {
        var traveler = _travelers.FirstOrDefault(x => x.Id == travelerId);
        if (traveler is null) return TripErrors.TravelerNotFound(travelerId);
        var result = traveler.Update(price, insurancePrice, taxPrice, discount, note,
            departurePointId, returnDeparturePointId);
        if (result.IsError) return result.Error;
        return traveler;
    }

    public Result RemoveTraveler(Guid travelerId)
    {
        var traveler = _travelers.FirstOrDefault(x => x.Id == travelerId);
        if (traveler is null) return TripErrors.TravelerNotFound(travelerId);
        _travelers.Remove(traveler);
        return Result.Ok();
    }

    public Result<Payment> AddPayment(Guid paidByPersonId, double amount, PaymentType type,
        DateTime paidAt, string? note)
    {
        var result = Payment.Create(Id, paidByPersonId, amount, type, paidAt, note);

        if (result.IsError) return result.Error;

        _payments.Add(result.Value);

        return result.Value;
    }

    public Result<Payment> UpdatePayment(Guid paymentId, Guid paidByPersonId, double amount, PaymentType type,
        DateTime paidAt, string? note)
    {
        var payment = _payments.FirstOrDefault(x => x.Id == paymentId);
        if (payment is null) return TravelPartyErrors.PaymentNotFound(paymentId);

        var result = payment.Update(paidByPersonId, amount, type, paidAt, note);

        if (result.IsError) return result.Error;

        return payment;
    }

    public Result RemovePayment(Guid paymentId)
    {
        var payment = _payments.FirstOrDefault(x => x.Id == paymentId);
        if (payment is null) return TravelPartyErrors.PaymentNotFound(paymentId);

        _payments.Remove(payment);

        return Result.Ok();
    }

    public Result ApplyLoyaltyPointsDiscount(Guid personId, int points)
    {
        if (points < 0) return TravelerErrors.InvalidDiscount(points);

        var traveler = _travelers.FirstOrDefault(s => s.PersonId == personId);
        if (traveler is null) return TripErrors.PersonTravelerNotFound(personId);

        if (!TripDestination.Loyalty.CanSpendPoints) return TripErrors.CannotSpendPoints;
        if (TripDestination.Loyalty.MaximumPointsToSpend < points) return TripErrors.MaximumPointsToSpend(points);

        var redeemResult = traveler.Person.RedeemPoints(points, LoyaltyPointTransactionType.Discount, TripDestinationId);
        if (redeemResult.IsError) return redeemResult.Error;
        
        var result = traveler.ApplyDiscount(points);
        if (result.IsError) return result.Error;

        return Result.Ok();
    }
    
    public Result UpdateBusAndSeat(Guid travelerId, Guid? departureBusId, int? departureSeatNumber, Guid? returnBusId,
        int? returnSeatNumber)
    {
        var traveler = _travelers.FirstOrDefault(x => x.Id == travelerId);
        if (traveler is null) return TripErrors.TravelerNotFound(travelerId);

        var result = traveler.UpdateBusAndSeat(departureBusId, departureSeatNumber, returnBusId, returnSeatNumber);

        if (result.IsError) return result.Error;

        return Result.Ok();
    }

    /// <summary>
    /// Update status to all travelers in the party.
    /// </summary>
    /// <param name="status">Status</param>
    public Result UpdateStatus(TravelerStatus status)
    {
        foreach (var traveler in _travelers)
        {
            var result = traveler.UpdateStatus(status);
            if (result.IsError) return result.Error;
        }
        
        return Result.Ok();
    }

    /// <summary>
    /// Update status to for traveler
    /// </summary>
    /// <param name="travelerId">Traveler ID</param>
    /// <param name="status">Status</param>
    public Result UpdateStatus(Guid travelerId, TravelerStatus status)
    {
        var traveler = _travelers.FirstOrDefault(x => x.Id == travelerId);
        if (traveler is null) return TripErrors.TravelerNotFound(travelerId);
        
        var result = traveler.UpdateStatus(status);
        if (result.IsError) return result.Error;
        
        return Result.Ok();
    }
}

public static class TravelPartyErrors
{
    public static Error PaymentNotFound(Guid paymentId) =>
        Error.NotFound("TravelParty.PaymentNotFound", $"Payment: {paymentId} not found");

    public static Error InvalidMainContactId(Guid mainContactId) =>
        Error.Validation("TravelParty.InvalidMainContactId", $"Invalid main contact ID: {mainContactId}");

    public static Error InvalidTripId(Guid tripId) =>
        Error.Validation("TravelParty.InvalidTripId", $"Invalid trip ID: {tripId}");

    public static Error InvalidTripDestinationId(Guid tripDestinationId) =>
        Error.Validation("TravelParty.InvalidTripDestinationId", $"Invalid trip destination ID: {tripDestinationId}");
}