using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.Core.Domains.Travelers.Events;
using OdmoriBa.Core.Domains.Trips.Entities;
using OdmoriBa.Core.Domains.Trips.Extensions;

namespace OdmoriBa.Core.Domains.Travelers.Entities;

public sealed class Traveler : EntityBase<Guid>, ISoftDelete
{
    public Guid TravelPartyId { get; private set; }
    public TravelParty? TravelParty { get; init; }

    public Guid PersonId { get; private set; }
    public Person Person { get; init; } = null!;

    public TravelerStatus Status { get; private set; }
    public double Price { get; private set; }
    public double InsurancePrice { get; private set; }
    public double TaxPrice { get; private set; }
    public double Discount { get; private set; }
    public string? Note { get; private set; }

    public TravelServiceType TravelServiceType { get; set; }

    public Guid? DeparturePointId { get; private set; }
    public TripStop? DeparturePoint { get; init; }

    public Guid? ReturnDeparturePointId { get; private set; }
    public TripStop? ReturnDeparturePoint { get; init; }

    public Guid? DepartureTripBusId { get; private set; }
    public TripBus? DepartureTripBus { get; init; }
    public int? DepartureSeatNumber { get; private set; }
    public Guid? ReturnTripBusId { get; private set; }
    public TripBus? ReturnTripBus { get; init; }
    public int? ReturnSeatNumber { get; private set; }
    
    public bool IsDeleted { get; private set;  }
    public DateTimeOffset? DeletedAt { get; private set; }

    private Traveler()
    {
    }

    internal static Result<Traveler> Create(Guid travelPartyId, Guid personId, double price,
        double insurancePrice,
        double taxPrice, double discount, string? note, Guid? departurePointId, Guid? returnDeparturePointId,
        TravelerStatus status = TravelerStatus.Draft)
    {
        if (travelPartyId == Guid.Empty) return TravelerErrors.InvalidTravelPartyId(travelPartyId);
        if (personId == Guid.Empty) return TravelerErrors.InvalidPersonId(personId);
        if (price < 0) return TravelerErrors.InvalidPrice(price);
        if (insurancePrice < 0) return TravelerErrors.InvalidInsurancePrice(insurancePrice);
        if (taxPrice < 0) return TravelerErrors.InvalidTaxPrice(taxPrice);
        if (discount < 0) return TravelerErrors.InvalidDiscount(discount);

        return new Traveler
        {
            Id = Guid.CreateVersion7(),
            TravelPartyId = travelPartyId,
            PersonId = personId,
            Status = status,
            Price = price,
            InsurancePrice = insurancePrice,
            TaxPrice = taxPrice,
            Discount = discount,
            Note = note,
            DeparturePointId = departurePointId,
            ReturnDeparturePointId = returnDeparturePointId
        };
    }

    internal Result Update(double price, double insurancePrice, double taxPrice, double discount,
        string? note, Guid? departurePointId, Guid? returnDeparturePointId)
    {
        if (price < 0) return TravelerErrors.InvalidPrice(price);
        if (insurancePrice < 0) return TravelerErrors.InvalidInsurancePrice(insurancePrice);
        if (taxPrice < 0) return TravelerErrors.InvalidTaxPrice(taxPrice);
        if (discount < 0) return TravelerErrors.InvalidDiscount(discount);

        Price = price;
        InsurancePrice = insurancePrice;
        TaxPrice = taxPrice;
        Discount = discount;
        Note = note;
        DeparturePointId = departurePointId;
        ReturnDeparturePointId = returnDeparturePointId;

        return Result.Ok();
    }

    internal Result UpdateBusAndSeat(Guid? departureBusId, int? departureSeatNumber, Guid? returnBusId,
        int? returnSeatNumber)
    {
        if (departureSeatNumber < 0) return TravelerErrors.InvalidSeatNumber(departureSeatNumber);
        if (returnSeatNumber < 0) return TravelerErrors.InvalidSeatNumber(returnSeatNumber);

        DepartureTripBusId = departureBusId;
        DepartureSeatNumber = departureSeatNumber;
        ReturnTripBusId = returnBusId;
        ReturnSeatNumber = returnSeatNumber;

        return Result.Ok();
    }

    internal Result ApplyDiscount(double discount)
    {
        if (discount < 0) return TravelerErrors.InvalidDiscount(discount);
        Discount += discount;
        return Result.Ok();
    }

    internal Result UpdateStatus(TravelerStatus status)
    {
        if (!Status.CanTransitionTo(status))
        {
            return TravelerErrors.UpdateStatus(Status, status);
        }
        
        AddDomainEvent(new TravelerStatusChangedDomainEvent(Id, Status, status));

        Status = status;
        return Result.Ok();
    }
}

public static class TravelerErrors
{
    public static Error UpdateStatus(TravelerStatus oldStatus, TravelerStatus newStatus) =>
        Error.Validation("Traveler.UpdateStatus",
            $"Cannot change status from {oldStatus.ToString()} to: {newStatus.ToString()}");

    public static Error InvalidTravelPartyId(Guid id) => Error.Validation(
        "Traveler.InvalidTravelPartyId",
        $"Travel party id cannot be empty. Provided value: {id}");

    public static Error InvalidPersonId(Guid id) => Error.Validation(
        "Traveler.InvalidPersonId",
        $"Person id cannot be empty. Provided value: {id}");

    public static Error InvalidPrice(double price) => Error.Validation(
        "Traveler.InvalidPrice",
        $"Price cannot be negative. Provided value: {price}");

    public static Error InvalidInsurancePrice(double price) => Error.Validation(
        "Traveler.InvalidInsurancePrice",
        $"Insurance price cannot be negative. Provided value: {price}");

    public static Error InvalidTaxPrice(double price) => Error.Validation(
        "Traveler.InvalidTaxPrice",
        $"Tax price cannot be negative. Provided value: {price}");

    public static Error InvalidDiscount(double discount) => Error.Validation(
        "Traveler.InvalidDiscount",
        $"Discount cannot be negative. Provided value: {discount}");

    public static Error InvalidSeatNumber(int? seatNumber) => Error.Validation(
        "Traveler.InvalidSeatNumber",
        $"Seat number cannot be negative. Provided value: {seatNumber}");
}

public enum TravelerStatus
{
    [LocalizedEnum] Requested,
    [LocalizedEnum] Draft,
    [LocalizedEnum] Confirmed,
    [LocalizedEnum] Cancelled
}

public enum TravelServiceType
{
    Full,
    OnlyTransportation,
    OnlyAccommodation
}