using System.ComponentModel;
using OdmoriBa.Core.Domains.Companies.Entities;

namespace OdmoriBa.Core.Domains.Transportations.Entities;

public sealed class Bus : EntityBase<Guid>
{
    public string Name { get; private set; } = null!;
    public Guid CompanyId { get; private set; }
    public Company? Company { get; init; }
    public int Capacity { get; private set; }
    public BusStatus Status { get; set; }

    private Bus()
    {
    }

    public static Result<Bus> Create(
        string name,
        Guid companyId,
        int capacity,
        BusStatus status)
    {
        if (string.IsNullOrWhiteSpace(name)) return BusErrors.InvalidName(name);
        if (companyId == Guid.Empty) return CompanyErrors.InvalidId(companyId);

        return new Bus
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            CompanyId = companyId,
            Capacity = capacity,
            Status = status
        };
    }

    public Result Update(string name, Guid companyId, int capacity, BusStatus status)
    {
        if (string.IsNullOrWhiteSpace(name)) return BusErrors.InvalidName(name);
        if (companyId == Guid.Empty) return CompanyErrors.InvalidId(companyId);

        Name = name;
        CompanyId = companyId;
        Capacity = capacity;
        Status = status;

        return Result.Ok();
    }
}

public enum BusStatus
{
    [Description("Aktivan")] Active,
    [Description("Neaktivan")] Inactive
}

public static class BusErrors
{
    public static Error InvalidName(string name) =>
        Error.Validation("Bus.InvalidName", $"Invalid bus name: {name}");

    public static Error NotFound(Guid busId) => Error.NotFound("Bus.NotFound", $"Bus: {busId} not found");
}