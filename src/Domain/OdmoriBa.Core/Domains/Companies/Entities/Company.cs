using OdmoriBa.Core.Common.ValueObjects;

namespace OdmoriBa.Core.Domains.Companies.Entities;

public sealed class Company : EntityBase<Guid>
{
    public string Name { get; private set; } = null!;
    public string? Address { get; private set; }
    public Phone? ContactPhone { get; private set; }
    public string? ContactEmail { get; private set; }

    private Company()
    {
    }

    public static Result<Company> Create(string name,
        string? address,
        Phone? contactPhone,
        string? contactEmail)
    {
        if (string.IsNullOrWhiteSpace(name)) return CompanyErrors.InvalidCompanyName(name);

        return new Company
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            Address = address,
            ContactPhone = contactPhone,
            ContactEmail = contactEmail,
        };
    }


    public Result Update(string name, string? address, Phone? contactPhone, string? contactEmail)
    {
        if (string.IsNullOrWhiteSpace(name)) return CompanyErrors.InvalidCompanyName(name);
        Name = name;
        Address = address;
        ContactPhone = contactPhone;
        ContactEmail = contactEmail;

        return Result.Ok();
    }
}

public static class CompanyErrors
{
    public static Error InvalidId(Guid id) =>
        Error.Validation("Company.InvalidId", $"Invalid company ID: {id}");

    public static Error InvalidCompanyName(string name) =>
        Error.Validation("Company.InvalidCompanyName", $"Invalid company name: {name}");

    public static Error CompanyNotFound(Guid id) => Error.NotFound("Company.NotFound", $"Company not found: {id}");
}