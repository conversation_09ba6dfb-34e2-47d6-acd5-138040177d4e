namespace OdmoriBa.Core.Common.ValueObjects;

public sealed record Phone
{
    public string CountryCode { get; set; }
    public string Number { get; set; }

    public Phone(string countryCode, string number)
    {
        CountryCode = countryCode;
        Number = number;
    }
    
    public override string ToString() => $"{CountryCode} {Number}";

    public static Phone Parse(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Phone number cannot be empty.", nameof(value));

        var parts = value.Split(' ', 2);
        if (parts.Length != 2)
            throw new FormatException("Phone number must be in format '{country code} {number}'.");

        return new Phone(parts[0], parts[1]);
    }

    public static implicit operator string?(Phone? phone) => phone?.ToString();
    public static implicit operator Phone(string value) => Parse(value);
    
    public bool IsValid() => !string.IsNullOrWhiteSpace(CountryCode) && !string.IsNullOrWhiteSpace(Number);
}