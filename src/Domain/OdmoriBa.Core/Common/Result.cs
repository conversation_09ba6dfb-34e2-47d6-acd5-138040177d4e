namespace OdmoriBa.Core.Common;

public class Result
{
    protected Result(Error? error = null)
    {
        _error = error;
        IsError = error is not null;
    }

    public bool IsError { get; }
    private readonly Error? _error;
    public Error Error => _error!;

    public bool IsOk => !IsError;

    public static Result Ok() => new();
    public static implicit operator Result(Error t) => new(error: t);

    public void Switch(Action? f0, Action<Error>? f1)
    {
        if (f0 != null && IsOk)
        {
            f0();
            return;
        }

        if (f1 != null && IsError)
        {
            f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public async Task SwitchAsync(Func<Task>? f0, Func<Error, Task>? f1)
    {
        if (f0 != null && IsOk)
        {
            await f0();
            return;
        }

        if (f1 != null && IsError)
        {
            await f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public TResult Match<TResult>(Func<TResult>? f0, Func<Error, TResult>? f1)
    {
        if (f0 != null && IsOk)
        {
            return f0();
        }

        if (f1 != null && IsError)
        {
            return f1(Error);
        }

        throw new InvalidOperationException();
    }

    public async Task<TResult> MatchAsync<TResult>(Func<Task<TResult>>? f0, Func<Error, Task<TResult>>? f1)
    {
        if (f0 != null && IsOk)
        {
            return await f0();
        }

        if (f1 != null && IsError)
        {
            return await f1(Error);
        }

        throw new InvalidOperationException();
    }
}

public class Result<TValueResult> : Result where TValueResult : class?
{
    private Result(TValueResult? value = null!, Error? error = null!) : base(error)
    {
        _value = value;
    }

    private readonly TValueResult? _value;
    public TValueResult Value => _value!;

    public static implicit operator Result<TValueResult>(TValueResult t) => new(value: t);
    public static implicit operator Result<TValueResult>(Error t) => new(error: t);

    public void Switch(Action<TValueResult>? f0, Action<Error>? f1)
    {
        if (f0 != null && IsOk)
        {
            f0(Value);
            return;
        }

        if (f1 != null && IsError)
        {
            f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public async Task SwitchAsync(Func<TValueResult, Task>? f0, Func<Error, Task>? f1)
    {
        if (f0 != null && IsOk)
        {
            await f0(Value);
            return;
        }

        if (f1 != null && IsError)
        {
            await f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public TResult Match<TResult>(Func<TValueResult, TResult>? f0, Func<Error, TResult>? f1)
    {
        if (f0 != null && IsOk)
        {
            return f0(Value);
        }

        if (f1 != null && IsError)
        {
            return f1(Error);
        }

        throw new InvalidOperationException();
    }
}

public class Result<TValueResult, TErrorResult> : Result where TValueResult : class? where TErrorResult : class
{
    private Result(TValueResult? value = null, TErrorResult? error = null)
    {
        _value = value;
        _error = error;
    }

    private readonly TValueResult? _value;
    public TValueResult Value => _value!;

    private readonly TErrorResult? _error;
    public new TErrorResult Error => _error!;
    
    public new bool IsError => _error is not null;
    public new bool IsOk => _value is not null;

    public static implicit operator Result<TValueResult, TErrorResult>(TValueResult t) => new(value: t);
    public static implicit operator Result<TValueResult, TErrorResult>(TErrorResult t) => new(error: t);

    public void Switch(Action<TValueResult>? f0, Action<TErrorResult>? f1)
    {
        if (f0 != null && IsOk)
        {
            f0(Value);
            return;
        }

        if (f1 != null && IsError)
        {
            f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public async Task SwitchAsync(Func<TValueResult, Task>? f0, Func<TErrorResult, Task>? f1)
    {
        if (f0 != null && IsOk)
        {
            await f0(Value);
            return;
        }

        if (f1 != null && IsError)
        {
            await f1(Error);
            return;
        }

        throw new InvalidOperationException();
    }

    public TResult Match<TResult>(Func<TValueResult, TResult>? f0, Func<TErrorResult, TResult>? f1)
    {
        if (f0 != null && IsOk)
        {
            return f0(Value);
        }

        if (f1 != null && IsError)
        {
            return f1(Error);
        }

        throw new InvalidOperationException();
    }
}