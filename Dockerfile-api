FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Presentation/OdmoriBa.Api/OdmoriBa.Api.csproj", "src/Presentation/OdmoriBa.Api/"]
COPY ["src/Domain/OdmoriBa.Core/OdmoriBa.Core.csproj", "src/Domain/OdmoriBa.Core/"]
COPY ["src/Application/OdmoriBa.Application/OdmoriBa.Application.csproj", "src/Application/OdmoriBa.Application/"]
COPY ["src/Presentation/OdmoriBa.Presentation/OdmoriBa.Presentation.csproj", "src/Presentation/OdmoriBa.Presentation/"]
COPY ["src/Infrastructure/OdmoriBa.Infrastructure/OdmoriBa.Infrastructure.csproj", "src/Infrastructure/OdmoriBa.Infrastructure/"]
RUN dotnet restore "src/Presentation/OdmoriBa.Api/OdmoriBa.Api.csproj"
COPY . .
WORKDIR "/src/src/Presentation/OdmoriBa.Api"
RUN dotnet build "OdmoriBa.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "OdmoriBa.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OdmoriBa.Api.dll"]
