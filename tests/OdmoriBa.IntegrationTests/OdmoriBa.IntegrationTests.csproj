<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
        <RootNamespace>OdmoriBa.IntegrationTests</RootNamespace>
        <TargetFramework>net9.0</TargetFramework>
        <!--
        To enable the Microsoft Testing Platform 'dotnet test' experience, add property:
          <TestingPlatformDotnetTestSupport>true</TestingPlatformDotnetTestSupport>
    
        To enable the Microsoft Testing Platform native command line experience, add property:
          <UseMicrosoftTestingPlatformRunner>true</UseMicrosoftTestingPlatformRunner>
    
        For more information on Microsoft Testing Platform support in xUnit.net, please visit:
          https://xunit.net/docs/getting-started/v3/microsoft-testing-platform
        -->
    </PropertyGroup>

    <ItemGroup>
        <Content Include="xunit.runner.json" CopyToOutputDirectory="PreserveNewest"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="coverlet.collector" Version="6.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentAssertions" Version="[7.0.0]" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.7" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="Respawn" Version="6.2.1" />
        <PackageReference Include="Testcontainers.PostgreSql" Version="4.6.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="xunit.v3" Version="3.0.0" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\Presentation\OdmoriBa.Api\OdmoriBa.Api.csproj" />
    </ItemGroup>

</Project>
