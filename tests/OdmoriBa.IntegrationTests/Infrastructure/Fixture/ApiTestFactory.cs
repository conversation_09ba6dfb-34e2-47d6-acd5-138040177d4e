using System.Data.Common;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using NSubstitute;
using OdmoriBa.Application.Clients.Firebase;
using Respawn;
using Testcontainers.PostgreSql;

namespace OdmoriBa.IntegrationTests.Infrastructure.Fixture;

public sealed class ApiTestFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    public TestUserProvider TestUserProvider { get; private set; } = null!;
    public HttpClient Client { get; private set; } = null!;
    private DbConnection _dbConnection = null!;
    private Respawner _respawner = null!;
    public IFirebaseClient FirebaseClientMock { get; private set; } = null!;

    private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
        .WithDatabase("odmori_ba")
        .Build();
    
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Testing");
        
        builder.ConfigureTestServices(services =>
        {
            // Firebase Mock
            services.Remove(services.SingleOrDefault(service =>
                typeof(IFirebaseClient) == service.ServiceType)!);
            var firebaseClientMock = Substitute.For<IFirebaseClient>();
            services.AddSingleton(firebaseClientMock);
            FirebaseClientMock = firebaseClientMock;
            // END Firebase Mock
            
            // Database config
            services.Remove(services.SingleOrDefault(service =>
                typeof(DbContextOptions<AppDbContext>) == service.ServiceType)!);
            var dataSource = new NpgsqlDataSourceBuilder(_dbContainer.GetConnectionString())
                .EnableDynamicJson()
                .Build();
            services.AddScoped<AppDbContext>(_ => new AppDbContext(
                new DbContextOptionsBuilder<AppDbContext>()
                    .UseNpgsql(dataSource)
                    .UseSnakeCaseNamingConvention()
                    .EnableSensitiveDataLogging()
                    .EnableDetailedErrors()
                    .Options));
            var sp = services.BuildServiceProvider();
            using var scope = sp.CreateScope();
            var db = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            db.Database.EnsureCreated();
            // END Database config
            
            // Auth Config
            services.AddSingleton<TestUserProvider>();
            services.AddAuthentication("Test")
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", _ => { });
            services.AddAuthorizationBuilder()
                .SetDefaultPolicy(new AuthorizationPolicyBuilder("Test")
                    .RequireAuthenticatedUser()
                    .Build());
            // END auth config
        });
    }

    public async ValueTask InitializeAsync()
    {
        await _dbContainer.StartAsync();
        _dbConnection = new NpgsqlConnection(_dbContainer.GetConnectionString());
        Client = CreateClient();
        
        TestUserProvider = Services.GetRequiredService<TestUserProvider>();
        
        await _dbConnection.OpenAsync();
        _respawner = await Respawner.CreateAsync(_dbConnection, new RespawnerOptions
        {
            DbAdapter = DbAdapter.Postgres
        });
    }

    public async Task ResetDatabaseAsync() => await _respawner.ResetAsync(_dbConnection);

    public new async ValueTask DisposeAsync()
    {
        await _dbContainer.StopAsync();
    }
}