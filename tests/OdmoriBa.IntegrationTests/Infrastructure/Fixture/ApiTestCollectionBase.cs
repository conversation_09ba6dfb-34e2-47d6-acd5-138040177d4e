using Microsoft.Extensions.DependencyInjection;
using OdmoriBa.Application.Clients.Firebase;

namespace OdmoriBa.IntegrationTests.Infrastructure.Fixture;

[Collection(nameof(ApiTestCollection))]
public abstract class ApiTestCollectionBase : IAsyncLifetime
{
    private readonly ApiTestFactory _factory;
    
    protected static readonly Faker Faker = new();

    protected readonly HttpClient Client;
    protected readonly AppDbContext DbContext;
    protected readonly TestUserProvider TestUserProvider;
    protected readonly IFirebaseClient FirebaseClientMock;
    
    protected ApiTestCollectionBase(ApiTestFactory factory)
    {
        _factory = factory;
        Client = factory.Client;
        DbContext = factory.Services.GetRequiredService<AppDbContext>();
        TestUserProvider = factory.TestUserProvider;
        FirebaseClientMock = factory.FirebaseClientMock;
    }
    
    protected void AuthenticateAs(TestUser user)
    {
        TestUserProvider.SetUser(user);
    }

    public ValueTask InitializeAsync()
    {
        AuthenticateAs(TestUser.Create());
        return ValueTask.CompletedTask;
    }

    public async ValueTask DisposeAsync()
    {
        TestUserProvider.ClearUser();
        await _factory.ResetDatabaseAsync();
    }
}