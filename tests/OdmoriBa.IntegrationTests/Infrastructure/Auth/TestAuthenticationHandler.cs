using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OdmoriBa.Application.Common.Constants;

namespace OdmoriBa.IntegrationTests.Infrastructure.Auth;

public sealed class TestAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    IServiceProvider serviceProvider)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var testUserProvider = serviceProvider.GetRequiredService<TestUserProvider>();

        if (testUserProvider.CurrentUser == null)
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        var user = testUserProvider.CurrentUser;
        var claims = new List<Claim>
        {
            new(CustomClaimTypes.IdentityId, user.IdentityId),
            new(CustomClaimTypes.SysId, user.UserId.ToString()),
            new(CustomClaimTypes.Status, user.Status.ToString())
        };

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, "Test");

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}