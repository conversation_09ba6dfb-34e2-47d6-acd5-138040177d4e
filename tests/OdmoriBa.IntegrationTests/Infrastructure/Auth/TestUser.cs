using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.IntegrationTests.Infrastructure.Auth;

public sealed class TestUser
{
    public string IdentityId { get; private set; }
    public Guid UserId { get; private set; }
    public UserStatus Status { get; private set; }

    private TestUser()
    {
        IdentityId = "test-identity-id";
        Status = UserStatus.Active;
        UserId = Guid.NewGuid();
    }
    
    public static TestUser Create() => new();

    public TestUser WithIdentityId(string identityId)
    {
        IdentityId = identityId;
        return this;
    }

    public TestUser WithUserId(Guid userId)
    {
        UserId = userId;
        return this;
    }

    public TestUser WithStatus(UserStatus status)
    {
        Status = status;
        return this;
    }
}