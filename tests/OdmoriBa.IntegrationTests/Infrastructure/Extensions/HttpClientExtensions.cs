using System.Text.Json;
using System.Text.Json.Serialization;

namespace OdmoriBa.IntegrationTests.Infrastructure.Extensions;

internal static class HttpClientExtensions
{
    private static readonly JsonSerializerOptions DefaultSettings = new()
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    };

    public static async Task<T?> ReadJsonAsync<T>(this HttpContent content, CancellationToken cancellationToken)
    {
        return await content.ReadFromJsonAsync<T>(DefaultSettings, cancellationToken);
    }

    public static async Task<T?> ReadJsonAsync<T>(this HttpResponseMessage response,
        CancellationToken cancellationToken = default)
    {
        return await response.Content.ReadFromJsonAsync<T>(DefaultSettings, cancellationToken);
    }
}