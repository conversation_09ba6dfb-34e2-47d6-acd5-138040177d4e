using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Core.Domains.Customers.Entities;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;

public sealed class UserBuilder : AppDbContextBuilder<User>
{
    private UserBuilder(AppDbContext dbContext, User user) : base(dbContext, user)
    {
    }

    public static UserBuilder CreateWithPerson(AppDbContext dbContext)
    {
        var faker = new Faker();
        var entity = User.CreateWithPerson(
            faker.Random.Guid().ToString(),
            faker.PickRandom<UserRole>(),
            faker.<PERSON><PERSON>and<PERSON>(SignInProviders.All),
            faker.Name.FirstName(),
            faker.Name.LastName(),
            faker.Internet.Email(),
            faker.Phone.PhoneNumber("+### ##########"),
            faker.Date.Past(30, DateTime.Today.AddYears(-18)).ToDateOnly(), // age 18–48
            faker.Random.AlphaNumeric(15),
            faker.Address.CountryCode(),
            faker.Address.City(),
            faker.Address.StreetAddress()
        ).Value;
        return new UserBuilder(dbContext, entity);
    }
}