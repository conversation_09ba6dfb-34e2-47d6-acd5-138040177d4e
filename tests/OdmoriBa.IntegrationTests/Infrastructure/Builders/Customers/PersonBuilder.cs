using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Core.Common.ValueObjects;
using Person = OdmoriBa.Core.Domains.Customers.Entities.Person;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;

public sealed class PersonBuilder : AppDbContextBuilder<Person>
{
    private PersonBuilder(AppDbContext dbContext, Person person) : base(dbContext, person)
    {
    }

    public static PersonBuilder Create(AppDbContext dbContext)
    {
        var faker = new Faker();
        var entity = Person.Create(
            faker.Name.FirstName(),
            faker.Name.LastName(),
            faker.Internet.Email(),
            (Phone)faker.Phone.PhoneNumber("+### ##########"),
            faker.Date.Past(30, DateTime.Today.AddYears(-18)).ToDateOnly(), // age 18–48
            faker.Random.AlphaNumeric(15),
            faker.Address.CountryCode(),
            faker.Address.City(),
            faker.Address.StreetAddress()
        ).Value;
        return new PersonBuilder(dbContext, entity);
    }

    public PersonBuilder WithEmail(string? email)
    {
        SetValue(nameof(Entity.Email), email);
        return this;
    }
}