using OdmoriBa.Core.Domains.Travelers.Entities;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders.Trips;

public sealed class TravelPartyBuilder : AppDbContextBuilder<TravelParty>
{
    private static readonly Faker Faker = new();

    private TravelPartyBuilder(AppDbContext context, TravelParty entity) : base(context, entity)
    {
    }

    public static TravelPartyBuilder Create(AppDbContext dbContext)
    {
        var entity = TravelParty.Create(
            Guid.NewGuid(),
            Guid.NewGuid(),
            Guid.NewGuid(),
            Faker.Lorem.Sentence(),
            Faker.Lorem.Sentence()
        ).Value;

        return new TravelPartyBuilder(dbContext, entity);
    }

    public TravelPartyBuilder WithTripId(Guid tripId)
    {
        SetValue(nameof(Entity.TripId), tripId);
        return this;
    }

    public TravelPartyBuilder WithTripDestinationId(Guid tripDestinationId)
    {
        SetValue(nameof(Entity.TripDestinationId), tripDestinationId);
        return this;
    }

    public TravelPartyBuilder WithNote(string? note)
    {
        SetValue(nameof(Entity.Note), note);
        return this;
    }

    public TravelPartyBuilder WithRequestNote(string? requestNote)
    {
        SetValue(nameof(Entity.RequestNote), requestNote);
        return this;
    }

    public TravelPartyBuilder WithMainContact(Guid mainContactId)
    {
        SetValue(nameof(Entity.MainContactId), mainContactId);
        return this;
    }

    public TravelPartyBuilder WithTraveler(Guid personId, double price = 200.0)
    {
        Entity.AddTraveler(
            personId,
            price,
            Faker.Random.Double(10, 20), // insurance
            Faker.Random.Double(5, 15), // tax
            0, // discount
            Faker.Lorem.Sentence(),
            null, // departure point
            null // return departure point
        );
        return this;
    }

    public TravelPartyBuilder WithTraveler(Guid personId, double price, double insurancePrice, double taxPrice,
        double discount = 0)
    {
        Entity.AddTraveler(
            personId,
            price,
            insurancePrice,
            taxPrice,
            discount,
            Faker.Lorem.Sentence(),
            null, // departure point
            null // return departure point
        );
        return this;
    }

    public TravelPartyBuilder WithTravelerAndDeparturePoints(Guid personId, double price, Guid? departurePointId,
        Guid? returnDeparturePointId)
    {
        Entity.AddTraveler(
            personId,
            price,
            Faker.Random.Double(10, 20), // insurance
            Faker.Random.Double(5, 15), // tax
            0, // discount
            Faker.Lorem.Sentence(),
            departurePointId,
            returnDeparturePointId
        );
        return this;
    }

    public TravelPartyBuilder WithPayment(Guid paidByPersonId, double amount, PaymentType type)
    {
        Entity.AddPayment(
            paidByPersonId,
            amount,
            type,
            Faker.Date.Recent(30),
            Faker.Lorem.Sentence()
        );
        return this;
    }

    public TravelPartyBuilder WithLoyaltyDiscount(Guid personId, int points)
    {
        Entity.ApplyLoyaltyPointsDiscount(personId, points);
        return this;
    }

    public TravelPartyBuilder WithTravelerStatus(Guid travelerId, TravelerStatus status)
    {
        Entity.UpdateStatus(travelerId, status);
        return this;
    }
}