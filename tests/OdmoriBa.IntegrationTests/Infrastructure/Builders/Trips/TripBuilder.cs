using OdmoriBa.Core.Domains.Trips.Entities;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders.Trips;

public sealed class TripBuilder : AppDbContextBuilder<Trip>
{
    private static readonly Faker Faker = new();

    private TripBuilder(AppDbContext context, Trip entity) : base(context, entity)
    {
    }

    public static TripBuilder Create(AppDbContext dbContext)
    {
        var startDate = Faker.Date.FutureDateOnly();
        var endDate = startDate.AddDays(7);
        var entity = Trip.Create(
            Faker.Lorem.Sentence(2),
            Faker.Lorem.Sentence(30),
            Faker.PickRandom<TripType>(),
            startDate,
            endDate,
            Faker.PickRandom<TransportationType>()
        ).Value;

        return new TripBuilder(dbContext, entity);
    }

    public TripBuilder WithTitle(string title)
    {
        SetValue(nameof(Trip.Title), title);
        return this;
    }

    public TripBuilder WithDescription(string? description)
    {
        SetValue(nameof(Trip.Description), description);
        return this;
    }

    public TripBuilder WithType(TripType type)
    {
        SetValue(nameof(Trip.Type), type);
        return this;
    }

    public TripBuilder WithDates(DateOnly startDate, DateOnly endDate)
    {
        SetValue(nameof(Trip.StartDate), startDate);
        SetValue(nameof(Trip.EndDate), endDate);
        return this;
    }

    public TripBuilder WithTransportationType(TransportationType transportationType)
    {
        SetValue(nameof(Trip.TransportationType), transportationType);
        return this;
    }

    public TripBuilder WithStatus(TripStatus status)
    {
        SetValue(nameof(Trip.Status), status);
        return this;
    }

    public TripBuilder WithDestination(Guid destinationId)
    {
        Entity.AddDestination(destinationId,
            Entity.StartDate,
            Entity.EndDate,
            Faker.Random.Double(50, 300),
            Faker.Random.Double(5, 15),
            Faker.Random.Double(5, 20),
            new TripDestinationLoyalty
            {
                CanSpendPoints = Faker.Random.Bool(),
                MaximumPointsToSpend = Faker.Random.Int(10, 100),
                Points = Faker.Random.Int(10, 100)
            },
            Faker.Random.Bool()
        );

        return this;
    }

    public TripBuilder WithDestination(Guid destinationId, double price, bool canSpendPoints = true, int loyaltyPoints = 50)
    {
        Entity.AddDestination(destinationId,
            Entity.StartDate,
            Entity.EndDate,
            price,
            Faker.Random.Double(5, 15),
            Faker.Random.Double(5, 20),
            new TripDestinationLoyalty
            {
                CanSpendPoints = canSpendPoints,
                MaximumPointsToSpend = loyaltyPoints,
                Points = loyaltyPoints
            },
            false
        );

        return this;
    }

    public TripBuilder WithStop(Guid stopId, TripStopType type = TripStopType.Break)
    {
        var beginAt = Faker.Date.Future().ToDateTimeOffset();
        var endAt = beginAt.AddHours(Faker.Random.Int(1, 4));
        
        Entity.AddStop(stopId, beginAt, endAt, type, Faker.Lorem.Sentence());

        return this;
    }

    public TripBuilder WithBus(Guid busId, TripBusDirection direction = TripBusDirection.Departure)
    {
        Entity.AddBus(busId, Faker.Vehicle.Model(), direction);
        return this;
    }
    
    public TripBuilder AsDraft()
    {
        Entity.UpdateStatus(TripStatus.Draft);
        return this;
    }

    public TripBuilder AsPublished()
    {
        Entity.UpdateStatus(TripStatus.Published);
        return this;
    }

    public TripBuilder AsCancelled()
    {
        Entity.UpdateStatus(TripStatus.Cancelled);
        return this;
    }

    public TripBuilder AsCompleted()
    {
        Entity.UpdateStatus(TripStatus.Completed);
        return this;
    }
}