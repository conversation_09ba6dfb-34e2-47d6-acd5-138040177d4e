using Microsoft.EntityFrameworkCore.ChangeTracking;
using OdmoriBa.Core.Common;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders;

public abstract class AppDbContextBuilder<T> where T : EntityBase
{
    private readonly AppDbContext _context;
    protected readonly T Entity;
    private readonly EntityEntry<T> _entry;

    protected AppDbContextBuilder(AppDbContext context, T entity)
    {
        _context = context;
        Entity = entity;
        _entry = _context.Set<T>().Add(entity);
    }

    public T Build()
    {
        _context.SaveChanges();
        return _entry.Entity;
    }

    public async Task<T> BuildAsync(CancellationToken cancellationToken = default)
    {
        await _context.SaveChangesAsync(cancellationToken);
        return _entry.Entity;
    }

    protected void SetValue(string propertyName, object? value)
    {
        _entry.Property(propertyName).CurrentValue = value;
    }
    
    protected void SetNavigation(string propertyName, object? value)
    {
        _entry.Navigation(propertyName).CurrentValue = value;
    }
}