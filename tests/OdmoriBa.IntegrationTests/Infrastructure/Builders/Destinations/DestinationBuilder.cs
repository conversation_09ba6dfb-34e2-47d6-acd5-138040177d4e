using OdmoriBa.Core.Domains.Destinations.Entities;

namespace OdmoriBa.IntegrationTests.Infrastructure.Builders.Destinations;

public sealed class DestinationBuilder : AppDbContextBuilder<Destination>
{
    private DestinationBuilder(AppDbContext context, Destination entity) : base(context, entity)
    {
    }

    public static DestinationBuilder Create(AppDbContext dbContext)
    {
        var faker = new Faker();
        var entity = Destination.Create(faker.Address.City()).Value;
        return new DestinationBuilder(dbContext, entity);
    }
}