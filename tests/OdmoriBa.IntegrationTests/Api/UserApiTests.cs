using OdmoriBa.Application.Common.Constants;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Common.ValueObjects;
using OdmoriBa.Core.Domains.Customers.Entities;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.IntegrationTests.Api;

public sealed class UserApiTests(ApiTestFactory factory) : ApiTestCollectionBase(factory)
{
    private readonly Faker<CreateUserRequest> _createUserRequestFaker = new Faker<CreateUserRequest>()
        .RuleFor(x => x.FirstName, f => f.Name.FirstName())
        .RuleFor(x => x.LastName, f => f.Name.LastName())
        .RuleFor(x => x.Email, f => f.Internet.Email())
        .RuleFor(x => x.Phone, f => (Phone)f.Phone.PhoneNumber("+### ##########"))
        .RuleFor(x => x.BirthDate, f => f.Date.Past(50))
        .RuleFor(x => x.IdDocument, f => f.Random.String2(10))
        .RuleFor(x => x.CountryCode, f => f.Address.CountryCode())
        .RuleFor(x => x.City, f => f.Address.City())
        .RuleFor(x => x.Address, f => f.Address.FullAddress())
        .RuleFor(x => x.SignInProvider, f => f.PickRandom(SignInProviders.All));

    [Fact]
    public async Task CreateUser_Should_Be_Ok()
    {
        // Arrange
        var personBuilder = PersonBuilder.Create(DbContext);
        personBuilder.WithEmail("<EMAIL>");
        var request = _createUserRequestFaker.Generate();

        // Act
        var response = await Client.PostAsJsonAsync("api/User", request, TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.ReadJsonAsync<UserDto>(TestContext.Current.CancellationToken);
        result!.Id.Should().NotBeEmpty();

        var dbPerson = await DbContext.Persons
            .AsNoTracking()
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.Id == result.Id, TestContext.Current.CancellationToken);
        dbPerson.Should().NotBeNull();
        dbPerson!.User.Should().NotBeNull();
        dbPerson.Email.Should().Be(request.Email);
        dbPerson.Phone.Should().Be(request.Phone);
        dbPerson.FirstName.Should().Be(request.FirstName);
        dbPerson.IdDocument.Should().Be(request.IdDocument);
        dbPerson.LastName.Should().Be(request.LastName);
        dbPerson.CountryCode.Should().Be(request.CountryCode);
        dbPerson.City.Should().Be(request.City);
        dbPerson.Address.Should().Be(request.Address);
        dbPerson.User!.IdentityId.Should().Be(TestUserProvider.CurrentUser?.IdentityId);
        dbPerson.User!.SignInProvider.Should().Be(request.SignInProvider);
        dbPerson.User!.Email.Should().Be(request.Email);
        dbPerson.User!.Phone.Should().Be(request.Phone);
    }

    [Fact]
    public async Task Update_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        AuthenticateAs(TestUser.Create().WithUserId(user.Id));

        var request = new UpdateUserRequest
        {
            Email = "<EMAIL>",
            Phone = "+11 **********"
        };

        // Act
        var response = await Client.PatchAsJsonAsync("api/User", request, TestContext.Current.CancellationToken);

        response.EnsureSuccessStatusCode();

        var dbPerson = await DbContext.Persons
            .AsNoTracking()
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.Id == user.Id, TestContext.Current.CancellationToken);
        dbPerson.Should().NotBeNull();
        dbPerson!.Email.Should().Be(request.Email);
        dbPerson.Phone.Should().Be(request.Phone);
        dbPerson.User!.Email.Should().Be(request.Email);
        dbPerson.User.Phone.Should().Be(request.Phone);
    }

    [Fact]
    public async Task CreateDeviceBiding_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        AuthenticateAs(TestUser.Create().WithUserId(user.Id));
        var request = new SaveDeviceBindingRequest
        {
            DeviceType = DeviceType.Ios,
            PushNotificationToken = Faker.Random.AlphaNumeric(126),
            DeviceId = Faker.Random.AlphaNumeric(16),
            DeviceModel = Faker.Random.String2(10),
            AppVersion = Faker.Random.Double(10, 20).ToString("0.0"),
            OsVersion = Faker.Random.Double(10, 20).ToString("0.0")
        };

        // Act
        var response =
            await Client.PostAsJsonAsync("api/User/DeviceBinding", request, TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();

        var deviceBinding = await DbContext.DeviceBindings
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.UserId == user.Id, TestContext.Current.CancellationToken);
        deviceBinding.Should().NotBeNull();
        deviceBinding!.DeviceType.Should().Be(request.DeviceType);
        deviceBinding.PushNotificationToken.Should().Be(request.PushNotificationToken);
        deviceBinding.OsVersion.Should().Be(request.OsVersion);
        deviceBinding.AppVersion.Should().Be(request.AppVersion);
        deviceBinding.DeviceId.Should().Be(request.DeviceId);
        deviceBinding.DeviceModel.Should().Be(request.DeviceModel);
    }
}