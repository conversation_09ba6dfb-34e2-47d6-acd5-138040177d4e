using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Core.Common.ValueObjects;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Destinations;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Trips;
using OdmoriBa.Presentation.Features.Customers.Models;

namespace OdmoriBa.IntegrationTests.Api;

public sealed class PersonApiTests(ApiTestFactory factory) : ApiTestCollectionBase(factory)
{
    [Fact]
    public async Task GetMe_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext)
            .BuildAsync(TestContext.Current.CancellationToken);

        AuthenticateAs(TestUser.Create().WithUserId(user.Id));

        // Act
        var response = await Client.GetAsync("api/Person/Me",
            cancellationToken: TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadJsonAsync<PersonDto>(TestContext.Current.CancellationToken);
        result.Should().NotBeNull();
        result!.Id.Should().Be(user.Id);
        result.Phone.Should().Be(user.Phone);
        result.Email.Should().Be(user.Email);
        result.FirstName.Should().Be(user.Person!.FirstName);
        result.LastName.Should().Be(user.Person.LastName);
        result.Phone.Should().Be(user.Person.Phone);
        result.Email.Should().Be(user.Person.Email);
        result.BirthDate.Should().Be(user.Person.BirthDate.ToDateTime(TimeOnly.MinValue));
        result.CountryCode.Should().Be(user.Person!.CountryCode);
        result.City.Should().Be(user.Person!.City);
        result.Address.Should().Be(user.Person!.Address);
    }

    private readonly Faker<SavePersonRequest> _createUserRequestFaker = new Faker<SavePersonRequest>()
        .RuleFor(x => x.FirstName, f => f.Name.FirstName())
        .RuleFor(x => x.LastName, f => f.Name.LastName())
        .RuleFor(x => x.Email, f => f.Internet.Email())
        .RuleFor(x => x.Phone, f => (Phone)f.Phone.PhoneNumber("+### ##########"))
        .RuleFor(x => x.BirthDate, f => f.Date.Past(50))
        .RuleFor(x => x.IdDocument, f => f.Random.String2(10))
        .RuleFor(x => x.CountryCode, f => f.Address.CountryCode())
        .RuleFor(x => x.City, f => f.Address.City())
        .RuleFor(x => x.Address, f => f.Address.FullAddress());

    [Fact]
    public async Task UpdateMe_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext)
            .BuildAsync(TestContext.Current.CancellationToken);

        AuthenticateAs(TestUser.Create().WithUserId(user.Id));

        var request = _createUserRequestFaker.Generate();

        // Act
        var response = await Client.PutAsJsonAsync("api/Person/Me", request,
            cancellationToken: TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var dbPerson = await DbContext.Persons
            .AsNoTracking()
            .Include(s => s.User)
            .FirstOrDefaultAsync(s => s.Id == user.Id, TestContext.Current.CancellationToken);

        dbPerson.Should().NotBeNull();
        dbPerson!.FirstName.Should().Be(request.FirstName);
        dbPerson.LastName.Should().Be(request.LastName);
        dbPerson.Email.Should().Be(request.Email);
        dbPerson.Phone.Should().Be(request.Phone);
        dbPerson.BirthDate.Should().Be(request.BirthDate.ToDateOnly());
        dbPerson.IdDocument.Should().Be(request.IdDocument);
        dbPerson.City.Should().Be(request.City);
        dbPerson.Address.Should().Be(request.Address);
        dbPerson.CountryCode.Should().Be(request.CountryCode);
    }


    [Fact]
    public async Task GetRelatedPersons_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var relatedPerson = await PersonBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var destination = await DestinationBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var trip = await TripBuilder.Create(DbContext)
            .WithDestination(destination.Id)
            .BuildAsync(TestContext.Current.CancellationToken);

        var travelParty = await TravelPartyBuilder.Create(DbContext)
            .WithTripId(trip.Id)
            .WithTripDestinationId(trip.TripDestinations.First().Id)
            .WithMainContact(user.Id)
            .WithTraveler(user.Id)
            .WithTraveler(relatedPerson.Id)
            .BuildAsync(TestContext.Current.CancellationToken);
        
        AuthenticateAs(TestUser.Create().WithUserId(user.Id));
        
        // Act
        var response = await Client.GetAsync("api/Person/RelatedPersons",
            cancellationToken: TestContext.Current.CancellationToken);
        
        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadJsonAsync<ListResultDto<PersonDto>>(TestContext.Current.CancellationToken);
        result!.Items.Should().NotBeEmpty();
        result.Items.Should().HaveCount(1);
        result.Items[0].Id.Should().Be(relatedPerson.Id);
    }

    [Fact]
    public async Task GetLoyalty_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        AuthenticateAs(TestUser.Create().WithUserId(user.Id));
        
        // Act
        var response = await Client.GetAsync("api/Person/Loyalty",
            cancellationToken: TestContext.Current.CancellationToken);
        
        // Assert
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadJsonAsync<LoyaltyCardDto>(TestContext.Current.CancellationToken);
        result.Should().NotBeNull();
        result!.TotalPoints.Should().Be(0);
    }
}