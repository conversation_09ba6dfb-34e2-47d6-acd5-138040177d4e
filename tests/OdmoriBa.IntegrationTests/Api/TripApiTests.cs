using OdmoriBa.Application.Common.Extensions;
using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Customers.Models;
using OdmoriBa.Application.Features.Travelers.Models;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Destinations;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Trips;
using OdmoriBa.Presentation.Features.Trips.Models;

namespace OdmoriBa.IntegrationTests.Api;

public sealed class TripApiTests(ApiTestFactory factory) : ApiTestCollectionBase(factory)
{
    [Fact]
    public async Task GetTrips_Should_Be_Ok()
    {
        // Arrange
        var trip1 = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Published)
            .BuildAsync(TestContext.Current.CancellationToken);
        var trip2 = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Published)
            .BuildAsync(TestContext.Current.CancellationToken);
        var trip3 = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Draft)
            .BuildAsync(TestContext.Current.CancellationToken);
        var trip4 = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Cancelled)
            .BuildAsync(TestContext.Current.CancellationToken);
        var trip5 = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Completed)
            .BuildAsync(TestContext.Current.CancellationToken);

        // Act
        var response = await Client.GetAsync("api/Trip", cancellationToken: TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result =
            await response.Content.ReadJsonAsync<PaginatedResultDto<TripDto>>(TestContext.Current.CancellationToken);

        result!.TotalCount.Should().Be(2);
        result.Items.Should().HaveCount(2);

        // Verify trip1 details
        var trip1Dto = result.Items.Should().ContainSingle(t => t.Id == trip1.Id).Subject;
        trip1Dto.Title.Should().Be(trip1.Title);
        trip1Dto.Description.Should().Be(trip1.Description);
        trip1Dto.Type.Should().Be(trip1.Type);
        trip1Dto.Status.Should().Be(trip1.Status);
        trip1Dto.StartDate.Should().Be(trip1.StartDate.ToDateTime(TimeOnly.MinValue));
        trip1Dto.EndDate.Should().Be(trip1.EndDate.ToDateTime(TimeOnly.MinValue));
        trip1Dto.TransportationType.Should().Be(trip1.TransportationType);

        // Verify trip2 details
        var trip2Dto = result.Items.Should().ContainSingle(t => t.Id == trip2.Id).Subject;
        trip2Dto.Title.Should().Be(trip2.Title);
        trip2Dto.Description.Should().Be(trip2.Description);
        trip2Dto.Type.Should().Be(trip2.Type);
        trip2Dto.Status.Should().Be(trip2.Status);
        trip2Dto.StartDate.Should().Be(trip2.StartDate.ToDateTime(TimeOnly.MinValue));
        trip2Dto.EndDate.Should().Be(trip2.EndDate.ToDateTime(TimeOnly.MinValue));
        trip2Dto.TransportationType.Should().Be(trip2.TransportationType);

        // Verify only published trips are returned
        result.Items.Should().NotContain(s => s.Id == trip3.Id, "draft trips should not be returned");
        result.Items.Should().NotContain(s => s.Id == trip4.Id, "cancelled trips should not be returned");
        result.Items.Should().NotContain(s => s.Id == trip5.Id, "completed trips should not be returned");
    }


    [Fact]
    public async Task GetTripById_Should_Be_Ok()
    {
        // Arrange
        var destination = await DestinationBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var trip = await TripBuilder.Create(DbContext)
            .WithDestination(destination.Id)
            .WithStatus(TripStatus.Published)
            .BuildAsync(TestContext.Current.CancellationToken);

        // Act
        var response = await Client.GetAsync($"api/Trip/{trip.Id}",
            cancellationToken: TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result =
            await response.Content.ReadJsonAsync<TripDto>(TestContext.Current.CancellationToken);

        result!.Should().NotBeNull();
        result!.Title.Should().Be(trip.Title);
        result.Description.Should().Be(trip.Description);
        result.Type.Should().Be(trip.Type);
        result.Status.Should().Be(trip.Status);
        result.StartDate.Should().Be(trip.StartDate.ToDateTime(TimeOnly.MinValue));
        result.EndDate.Should().Be(trip.EndDate.ToDateTime(TimeOnly.MinValue));
        result.TransportationType.Should().Be(trip.TransportationType);
        result.TripDestinations.Should().HaveCount(1);
        result.TripDestinations.Should().ContainSingle(s => s.DestinationId == destination.Id);
    }

    [Fact]
    public async Task CreateReservation_SingleTraveler_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var destination = await DestinationBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var trip = await TripBuilder.Create(DbContext)
            .WithDestination(destination.Id)
            .WithStatus(TripStatus.Published)
            .BuildAsync(TestContext.Current.CancellationToken);
        var tripDestination = trip.TripDestinations.First();

        var request = new CreateReservationRequest
        {
            TripDestinationId = tripDestination.Id,
            RequestNote = Faker.Lorem.Paragraph()
        };

        AuthenticateAs(TestUser.Create().WithUserId(user.Id));

        // Act
        var response =
            await Client.PostAsJsonAsync($"api/Trip/{trip.Id}/Reservation", request,
                TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result =
            await response.Content.ReadJsonAsync<UserTravelPartyDto>(TestContext.Current.CancellationToken);

        result.Should().NotBeNull();
        result!.TravelerId.Should().NotBeEmpty();
        result.TravelParty.MainContactId.Should().Be(user.Id);
        result.TravelParty.RequestNote.Should().Be(request.RequestNote);
        result.TravelParty.Travelers.Should().ContainSingle(s => s.PersonId == user.Id);

        var travelParty = await DbContext.TravelParties
            .Include(s => s.Travelers)
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.Id == result.TravelParty.Id, TestContext.Current.CancellationToken);
        travelParty.Should().NotBeNull();
        travelParty!.Travelers.Should().ContainSingle(s => s.PersonId == user.Id);
        travelParty.MainContactId.Should().Be(user.Id);
        travelParty.RequestNote.Should().Be(request.RequestNote);
    }

    [Fact]
    public async Task CreateReservation_MultipleTravelers_Should_Be_Ok()
    {
        // Arrange
        var user1 = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var user2 = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var person1 = await PersonBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var newPerson = new PersonDto
        {
            FirstName = Faker.Name.FirstName(),
            LastName = Faker.Name.LastName(),
            BirthDate = Faker.Date.Past(100).Date,
        };

        var destination = await DestinationBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var trip = await TripBuilder.Create(DbContext)
            .WithDestination(destination.Id)
            .WithStatus(TripStatus.Published)
            .BuildAsync(TestContext.Current.CancellationToken);
        var tripDestination = trip.TripDestinations.First();

        var request = new CreateReservationRequest
        {
            TripDestinationId = tripDestination.Id,
            RequestNote = Faker.Lorem.Paragraph(),
            AdditionalPersons =
            [
                new PersonDto
                {
                    Id = user2.Id,
                },
                new PersonDto
                {
                    Id = person1.Id,
                },
                newPerson
            ]
        };

        AuthenticateAs(TestUser.Create().WithUserId(user1.Id));

        // Act
        var response =
            await Client.PostAsJsonAsync($"api/Trip/{trip.Id}/Reservation", request,
                TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result =
            await response.Content.ReadJsonAsync<UserTravelPartyDto>(TestContext.Current.CancellationToken);

        result.Should().NotBeNull();
        result!.TravelerId.Should().NotBeEmpty();
        result.TravelParty.MainContactId.Should().Be(user1.Id);
        result.TravelParty.RequestNote.Should().Be(request.RequestNote);
        result.TravelParty.Travelers.Should().ContainSingle(s => s.PersonId == user1.Id);
        result.TravelParty.Travelers.Should().ContainSingle(s => s.PersonId == user2.Id);
        result.TravelParty.Travelers.Should().ContainSingle(s => s.PersonId == person1.Id);
        result.TravelParty.Travelers.Should().ContainSingle(s =>
            s.Person!.FirstName == newPerson.FirstName && s.Person.LastName == newPerson.LastName &&
            s.Person.BirthDate == newPerson.BirthDate);

        var travelParty = await DbContext.TravelParties
            .Include(s => s.Travelers)
            .AsNoTracking()
            .FirstOrDefaultAsync(s => s.Id == result.TravelParty.Id, TestContext.Current.CancellationToken);
        travelParty.Should().NotBeNull();
        travelParty!.Travelers.Should().HaveCount(request.AdditionalPersons.Count + 1);
        travelParty.Travelers.Should().ContainSingle(s => s.PersonId == user1.Id);
        travelParty.Travelers.Should().ContainSingle(s => s.PersonId == user2.Id);
        travelParty.Travelers.Should().ContainSingle(s => s.PersonId == person1.Id);
        travelParty.Travelers.Should().ContainSingle(s =>
            s.Person.FirstName == newPerson.FirstName && s.Person.LastName == newPerson.LastName &&
            s.Person.BirthDate == newPerson.BirthDate.ToDateOnly());
        travelParty.MainContactId.Should().Be(user1.Id);
        travelParty.RequestNote.Should().Be(request.RequestNote);
    }
}