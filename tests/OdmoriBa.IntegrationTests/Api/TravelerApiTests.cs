using OdmoriBa.Application.Common.Models;
using OdmoriBa.Application.Features.Trips.Models;
using OdmoriBa.Core.Domains.Trips.Entities;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Customers;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Destinations;
using OdmoriBa.IntegrationTests.Infrastructure.Builders.Trips;

namespace OdmoriBa.IntegrationTests.Api;

public sealed class TravelerApiTests(ApiTestFactory factory) : ApiTestCollectionBase(factory)
{
    [Fact]
    public async Task GetUserTrips_Should_Be_Ok()
    {
        // Arrange
        var user = await UserBuilder.CreateWithPerson(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var destination = await DestinationBuilder.Create(DbContext).BuildAsync(TestContext.Current.CancellationToken);
        var trip = await TripBuilder.Create(DbContext)
            .WithStatus(TripStatus.Published)
            .WithDestination(destination.Id)
            .BuildAsync(TestContext.Current.CancellationToken);
        var travelParty = await TravelPartyBuilder.Create(DbContext)
            .WithTripId(trip.Id)
            .WithTripDestinationId(trip.TripDestinations.First().Id)
            .WithMainContact(user.Id)
            .WithTraveler(user.Id)
            .BuildAsync(TestContext.Current.CancellationToken);
        var traveler = travelParty.Travelers.First();

        AuthenticateAs(TestUser.Create().WithUserId(user.Id));
        
        // Act
        var response = await Client.GetAsync("api/Traveler", TestContext.Current.CancellationToken);

        // Assert
        response.EnsureSuccessStatusCode();
        var result =
            await response.Content.ReadJsonAsync<ListResultDto<UserTripListItemDto>>(TestContext.Current
                .CancellationToken);

        result!.Should().NotBeNull();
        result!.Items.Should().HaveCount(1);
        result.Items[0].Status.Should().Be(traveler.Status);
        result.Items[0].TransportationType.Should().Be(trip.TransportationType);
        result.Items[0].TripStatus.Should().Be(trip.Status);
        result.Items[0].TripDestination?.DestinationId.Should().Be(destination.Id);
    }
}