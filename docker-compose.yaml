services:
  
  odmori-web:
    container_name: odmori-web
    image: odmori-web
    restart: always
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      OTEL_SERVICE_NAME: odmori-web
#      OTEL_EXPORTER_OTLP_ENDPOINT: http://aspire:18889
      OTEL_EXPORTER_OTLP_ENDPOINT: http://datadog:4317
      OTEL_EXPORTER_OTLP_PROTOCOL: grpc
      OTEL_RESOURCE_ATTRIBUTES: deployment.environment=dev,host.name=odmori-web'
      ConnectionStrings__Postgres: Host=postgres;Port=5432;Database=odmori;Username=postgres;Password=postgres
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    ports:
      - 5000:8080
      - 5001:8081
    networks:
      - pi

  odmori-api:
    container_name: odmori-api
    image: odmori-api
    restart: always
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      OTEL_SERVICE_NAME: odmori-api
#      OTEL_EXPORTER_OTLP_ENDPOINT: http://aspire:18889
      OTEL_EXPORTER_OTLP_ENDPOINT: http://datadog:4317
      OTEL_EXPORTER_OTLP_PROTOCOL: grpc
      OTEL_RESOURCE_ATTRIBUTES: deployment.environment=dev,host.name=odmori-api'
      ConnectionStrings__Postgres: Host=postgres;Port=5432;Database=odmori;Username=postgres;Password=postgres
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    ports:
      - 5002:8080
      - 5003:8081
    networks:
      - pi

volumes:
  pgdata:
    
networks:
  pi:
    name: pi_default
    external: true